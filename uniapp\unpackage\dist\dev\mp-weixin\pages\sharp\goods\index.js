"use strict";
const core_mixins_wxofficial = require("../../../core/mixins/wxofficial.js");
const core_app = require("../../../core/app.js");
const api_sharp_goods = require("../../../api/sharp/goods.js");
const api_cart = require("../../../api/cart.js");
const common_model_Setting = require("../../../common/model/Setting.js");
const common_enum_sharp_ActiveStatus = require("../../../common/enum/sharp/ActiveStatus.js");
const common_enum_sharp_GoodsStatus = require("../../../common/enum/sharp/GoodsStatus.js");
const common_vendor = require("../../../common/vendor.js");
const ShareSheet = () => "../../../components/share-sheet/index.js";
const CustomerBtn = () => "../../../components/customer-btn/index.js";
const SkuPopup = () => "./components/SkuPopup.js";
const SlideImage = () => "../../goods/components/SlideImage.js";
const Comment = () => "../../goods/components/Comment.js";
const CountDown = () => "../../../components/countdown/index.js";
const _sfc_main = {
  components: {
    ShareSheet,
    CustomerBtn,
    // Shortcut,
    SlideImage,
    SkuPopup,
    Comment,
    // Service,
    CountDown
  },
  mixins: [core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 正在加载
      isLoading: true,
      // 枚举类
      ActiveStatusEnum: common_enum_sharp_ActiveStatus.ActiveStatusEnum,
      GoodsStatusEnum: common_enum_sharp_GoodsStatus.GoodsStatusEnum,
      // 显示/隐藏SKU弹窗
      showSkuPopup: false,
      // 模式 1:都显示 2:只显示购物车 3:只显示立即购买
      skuMode: 3,
      // 活动未开始时的文字
      noStockText: "该商品已抢完",
      // 显示/隐藏分享菜单
      showShareSheet: false,
      // 获取商品海报图api方法
      posterApiCall: api_sharp_goods.poster,
      // 当前秒杀场次ID
      activeTimeId: null,
      // 当前秒杀商品ID
      sharpGoodsId: null,
      // 秒杀活动详情
      active: {},
      // 秒杀商品详情
      goods: {},
      // 购物车总数量
      cartTotal: 0,
      // 是否显示在线客服按钮
      isShowCustomerBtn: false
    };
  },
  computed: {
    // 当前页面链接
    pagePath() {
      const params = this.$getShareUrlParams({ grouponGoodsId: this.grouponGoodsId });
      return `/pages/groupon/goods/detail?${params}`;
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    this.onRecordQuery(options);
    this.onRefreshPage();
    this.isShowCustomerBtn = await common_model_Setting.SettingModel.isShowCustomerBtn();
  },
  methods: {
    // 记录query参数
    onRecordQuery(query) {
      const scene = core_app.getSceneData(query);
      this.activeTimeId = query.activeTimeId ? parseInt(query.activeTimeId) : parseInt(scene.aid);
      this.sharpGoodsId = query.sharpGoodsId ? parseInt(query.sharpGoodsId) : parseInt(scene.gid);
    },
    // 刷新页面数据
    onRefreshPage() {
      const app = this;
      app.isLoading = true;
      Promise.all([app.getActiveDetail(), app.getCartTotal()]).then(() => {
        const activeStatus = app.active.active_status;
        if (activeStatus != common_enum_sharp_GoodsStatus.GoodsStatusEnum.STATE_BEGIN.value) {
          app.skuMode = 4;
          app.noStockText = activeStatus == common_enum_sharp_GoodsStatus.GoodsStatusEnum.STATE_SOON.value ? "活动未开始" : "活动已结束";
        }
        app.setWxofficialShareData();
      }).finally(() => app.isLoading = false);
    },
    // 获取秒杀活动详情
    getActiveDetail() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_sharp_goods.detail(app.activeTimeId, app.sharpGoodsId).then((result) => {
          app.active = result.data.active;
          app.goods = result.data.goods;
          resolve(result);
        }).catch(reject);
      });
    },
    // 获取购物车总数量
    getCartTotal() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_cart.total().then((result) => {
          app.cartTotal = result.data.cartTotal;
          resolve(result);
        }).catch(reject);
      });
    },
    /**
     * 显示/隐藏SKU弹窗
     */
    onShowSkuPopup() {
      this.showSkuPopup = !this.showSkuPopup;
    },
    // 显示隐藏分享菜单
    onShowShareSheet() {
      this.showShareSheet = !this.showShareSheet;
    },
    // 跳转到首页
    onTargetHome(e) {
      this.$navTo("pages/index/index");
    },
    // 跳转到购物车页
    onTargetCart() {
      this.$navTo("pages/cart/index");
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      const { goods } = this;
      this.updateShareCardData({
        title: goods.goods_name,
        desc: goods.selling_point,
        imgUrl: goods.goods_image
      });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    return {
      title: this.goods.goods_name,
      path: this.pagePath
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    return {
      title: this.goods.goods_name,
      path: this.pagePath
    };
  }
};
if (!Array) {
  const _component_SlideImage = common_vendor.resolveComponent("SlideImage");
  const _component_count_down = common_vendor.resolveComponent("count-down");
  const _component_SkuPopup = common_vendor.resolveComponent("SkuPopup");
  const _component_Comment = common_vendor.resolveComponent("Comment");
  const _easycom_mp_html2 = common_vendor.resolveComponent("mp-html");
  const _component_customer_btn = common_vendor.resolveComponent("customer-btn");
  const _component_share_sheet = common_vendor.resolveComponent("share-sheet");
  (_component_SlideImage + _component_count_down + _component_SkuPopup + _component_Comment + _easycom_mp_html2 + _component_customer_btn + _component_share_sheet)();
}
const _easycom_mp_html = () => "../../../uni_modules/mp-html/components/mp-html/mp-html.js";
if (!Math) {
  _easycom_mp_html();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? {
    b: common_vendor.p({
      video: $data.goods.video,
      videoCover: $data.goods.videoCover,
      images: $data.goods.goods_images
    })
  } : {}, {
    c: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    d: common_vendor.t($data.goods.seckill_price),
    e: common_vendor.t($data.goods.original_price),
    f: common_vendor.t($data.active.sales_actual),
    g: common_vendor.t($data.goods.goods_name),
    h: common_vendor.o(($event) => $options.onShowShareSheet()),
    i: $data.goods.selling_point
  }, $data.goods.selling_point ? {
    j: common_vendor.t($data.goods.selling_point)
  } : {}, {
    k: $data.active.active_status != $data.GoodsStatusEnum.STATE_END.value
  }, $data.active.active_status != $data.GoodsStatusEnum.STATE_END.value ? {
    l: common_vendor.t($data.active.active_status == $data.GoodsStatusEnum.STATE_SOON.value ? "开始" : "结束"),
    m: common_vendor.p({
      date: $data.active.count_down_time,
      separator: "zh",
      theme: "text"
    })
  } : {}) : {}, {
    n: $data.goods.spec_type == 20
  }, $data.goods.spec_type == 20 ? {
    o: common_vendor.f($data.goods.specList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.spec_name),
        b: index
      };
    }),
    p: common_vendor.o(($event) => $options.onShowSkuPopup())
  } : {}, {
    q: !$data.isLoading
  }, !$data.isLoading ? {
    r: common_vendor.o(($event) => $data.showSkuPopup = $event),
    s: common_vendor.p({
      skuMode: $data.skuMode,
      active: $data.active,
      goods: $data.goods,
      noStockText: $data.noStockText,
      modelValue: $data.showSkuPopup
    })
  } : {}, {
    t: !$data.isLoading
  }, !$data.isLoading ? {
    v: common_vendor.p({
      ["goods-id"]: $data.goods.goods_id,
      limit: 2
    })
  } : {}, {
    w: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    x: $data.goods.content != ""
  }, $data.goods.content != "" ? {
    y: common_vendor.p({
      content: $data.goods.content
    })
  } : {}) : {}, {
    z: common_vendor.o((...args) => $options.onTargetHome && $options.onTargetHome(...args)),
    A: $data.isShowCustomerBtn
  }, $data.isShowCustomerBtn ? {
    B: common_vendor.p({
      showCard: true,
      cardTitle: $data.goods.goods_name,
      cardImage: $data.goods.goods_image,
      cardPath: $options.pagePath
    })
  } : {}, {
    C: !$data.isShowCustomerBtn
  }, !$data.isShowCustomerBtn ? common_vendor.e({
    D: $data.cartTotal > 0
  }, $data.cartTotal > 0 ? {
    E: common_vendor.t($data.cartTotal > 99 ? "99+" : $data.cartTotal)
  } : {}, {
    F: common_vendor.o((...args) => $options.onTargetCart && $options.onTargetCart(...args))
  }) : {}, {
    G: $data.active.active_status == $data.GoodsStatusEnum.STATE_BEGIN.value
  }, $data.active.active_status == $data.GoodsStatusEnum.STATE_BEGIN.value ? {
    H: common_vendor.o(($event) => $options.onShowSkuPopup())
  } : {
    I: common_vendor.t($data.active.active_status == $data.GoodsStatusEnum.STATE_SOON.value ? "活动未开始" : "活动已结束")
  }, {
    J: common_vendor.o(($event) => $data.showShareSheet = $event),
    K: common_vendor.p({
      shareTitle: $data.goods.goods_name,
      shareImageUrl: $data.goods.goods_image,
      posterApiCall: $data.posterApiCall,
      posterApiParam: {
        activeTimeId: $data.activeTimeId,
        sharpGoodsId: $data.sharpGoodsId
      },
      modelValue: $data.showShareSheet
    }),
    L: !$data.isLoading,
    M: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-dbd30a2e"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/sharp/goods/index.js.map
