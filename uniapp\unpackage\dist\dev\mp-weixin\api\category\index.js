"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "category/list",
  listVip: "category/listVip",
  listPoints: "category/listPoints"
};
function listVip() {
  return utils_request_index.$http.get(api.listVip);
}
function listPoints() {
  return utils_request_index.$http.get(api.listPoints);
}
exports.listPoints = listPoints;
exports.listVip = listVip;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/category/index.js.map
