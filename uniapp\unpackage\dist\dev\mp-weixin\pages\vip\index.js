"use strict";
const common_vendor = require("../../common/vendor.js");
const core_mixins_wxofficial = require("../../core/mixins/wxofficial.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const api_xj_video = require("../../api/xj/video.js");
const api_article_category = require("../../api/article/category.js");
const api_user = require("../../api/user.js");
const api_user_grade = require("../../api/user/grade.js");
const api_page = require("../../api/page.js");
const api_xj_vip = require("../../api/xj/vip.js");
const utils_util = require("../../utils/util.js");
require("../../common/enum/Client.js");
require("../../common/enum/payment/Method.js");
const core_payment_wechat = require("../../core/payment/wechat.js");
const core_app = require("../../core/app.js");
const Page = () => "../../components/page/index.js";
const pageSize = 15;
const _sfc_main = {
  components: {
    Page
  },
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin, core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      content: "会员可每月获取一个会员礼包，请填写准确收货信息以便收货",
      addressShow: false,
      page: {},
      items: [],
      disabled: false,
      isChoose: false,
      levelid: 0,
      userInfo: {},
      pre_url: "https://dshsc.zhanyuankj.cn/xinjiang/",
      gradeList: [],
      // 文章相关数据
      categoryId: 0,
      articleList: core_app.getEmptyPaginateObj(),
      // 上拉加载配置
      upOption: {
        auto: true,
        page: { size: pageSize },
        noMoreSize: 3
      }
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const app = this;
    app.categoryId = options.categoryId || 0;
    app.getGradeList();
    this.getPageData();
  },
  onShow() {
    this.getUserInfo();
  },
  methods: {
    // 表单提交
    handleSubmit() {
      common_vendor.index.navigateTo({
        url: "/pages/address/create"
      });
    },
    // 关闭地址弹窗
    closeAddressModal() {
      this.addressShow = false;
    },
    // 表单提交
    handleSubmit() {
      common_vendor.index.navigateTo({
        url: "/pages/address/create"
      });
    },
    // 关闭地址弹窗
    closeAddressModal() {
      this.addressShow = false;
    },
    getPageData(callback) {
      const app = this;
      const pageId = 10005;
      api_page.detail(pageId).then((result) => {
        const {
          data: {
            pageData
          }
        } = result;
        app.page = pageData.page;
        app.items = pageData.items;
      }).finally(() => callback && callback());
    },
    getUserInfo() {
      return new Promise((resolve, reject) => {
        api_user.info().then((result) => {
          this.userInfo = result.data.userInfo;
          resolve(result.data.userInfo);
        }).catch((err) => {
          reject(err);
        });
      });
    },
    changelevel(e) {
      this.levelid = e.detail.value;
      this.isChoose = true;
    },
    tobuy() {
      const app = this;
      if (app.disabled)
        return;
      app.disabled = true;
      if (app.userInfo.address_id == 0) {
        app.disabled = false;
        app.addressShow = true;
        return;
      }
      const submitData = {
        levelid: app.levelid,
        planId: 0,
        customMoney: "10",
        method: "wechat",
        client: "MP-WEIXIN",
        extra: {}
      };
      api_xj_vip.submit(submitData).then((result) => {
        app.onSubmitCallback(result);
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/vip/index.vue:202", "订单创建失败:", err);
      }).finally(() => {
        setTimeout(() => app.disabled = false, 10);
      });
    },
    // 订单提交成功后回调
    onSubmitCallback(result) {
      const app = this;
      const paymentData = result.data.payment;
      core_payment_wechat.payment({
        orderKey: "vip",
        ...paymentData
      }).then((res) => app.onPaySuccess(res)).catch((err) => app.onPayFail(err));
    },
    // 订单支付成功的回调方法
    onPaySuccess({
      res,
      option: {
        isRequireQuery,
        outTradeNo,
        method
      }
    }) {
      const app = this;
      if (isRequireQuery) {
        app.onTradeQuery(outTradeNo, method);
        return true;
      }
      this.onShowSuccess(res);
    },
    // 显示支付成功信息并页面跳转
    onShowSuccess({
      message
    }) {
      this.$toast(message || "订单支付成功");
      this.onSuccessNav();
    },
    // 订单支付失败
    onPayFail(err) {
      const errMsg = err.message || "订单未支付";
      this.$error(errMsg);
    },
    // 已完成支付按钮事件: 请求后端查单
    onTradeQuery(outTradeNo, method) {
      const app = this;
      api_xj_vip.tradeQuery({
        outTradeNo,
        method,
        client: app.platform
      }).then((result) => {
        if (result.data.isPay) {
          app.onShowSuccess(result);
        } else {
          app.onPayFail(result);
        }
      }).catch((err) => {
        app.onShowSuccess({ message: "恭喜您，会员升级成功" });
      }).finally(() => {
        app.showConfirmModal = false;
      });
    },
    // 支付成功后的跳转
    onSuccessNav() {
      common_vendor.index.__f__("log", "at pages/vip/index.vue:279", "=== 支付成功，准备跳转 ===");
      this.getUserInfo().then(() => {
        common_vendor.index.__f__("log", "at pages/vip/index.vue:283", "用户信息刷新完成:", this.userInfo);
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/vip/index.vue:285", "刷新用户信息失败:", err);
      });
      const pages = getCurrentPages();
      const lastPage = pages.length < 2 ? null : pages[pages.length - 2];
      const backRoutes = ["pages/user/index"];
      common_vendor.index.__f__("log", "at pages/vip/index.vue:291", "当前页面栈:", pages.map((p) => p.route));
      common_vendor.index.__f__("log", "at pages/vip/index.vue:292", "上一页:", lastPage == null ? void 0 : lastPage.route);
      if (lastPage && utils_util.inArray(lastPage.route, backRoutes)) {
        common_vendor.index.__f__("log", "at pages/vip/index.vue:295", "返回上一页");
        setTimeout(() => common_vendor.index.navigateBack(), 1e3);
      } else {
        common_vendor.index.__f__("log", "at pages/vip/index.vue:298", "重新启动到用户中心");
        setTimeout(() => {
          common_vendor.index.reLaunch({
            url: "/pages/user/index"
          });
        }, 1200);
      }
    },
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getArticleList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 获取文章分类数据
    getCategoryList() {
      api_article_category.list().then((result) => {
        this.setTabList(result.data.list);
      });
    },
    getGradeList() {
      api_user_grade.list().then((result) => {
        this.gradeList = result.data.list;
      });
    },
    // 设置选项卡数据
    setTabList(categoryList) {
      const app = this;
      app.tabList = [{
        value: 0,
        name: "全部"
      }];
      categoryList.forEach((item) => {
        app.tabList.push({
          value: item.category_id,
          name: item.name
        });
      });
      if (app.categoryId > 0) {
        const findIndex = app.tabList.findIndex((item) => item.value == app.categoryId);
        app.curTab = findIndex > -1 ? findIndex : 0;
      }
    },
    /**
     * 获取文章列表
     * @param {Number} pageNo 页码
     */
    getArticleList(pageNo = 1) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_xj_video.list({
          categoryId: app.categoryId,
          page: pageNo
        }, {
          load: false
        }).then((result) => {
          const newList = result.data.list;
          app.articleList.data = core_app.getMoreListData(newList, app.articleList, pageNo);
          resolve(newList);
        }).catch(reject);
      });
    },
    // 切换标签项
    onChangeTab(index) {
      this.curTab = index;
      this.categoryId = this.tabList[index].value;
      this.onRefreshList();
    },
    // 刷新列表数据
    onRefreshList() {
      this.articleList = core_app.getEmptyPaginateObj();
      setTimeout(() => this.mescroll.resetUpScroll(), 120);
    },
    // 跳转文章详情页
    onTargetDetail(articleId) {
      this.$navTo("pages/video/detail", {
        articleId
      });
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      this.updateShareCardData({
        title: "文章首页"
      });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    return {
      title: "文章首页",
      path: "/pages/article/index?" + this.$getShareUrlParams()
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    return {
      title: "文章首页",
      path: "/pages/article/index?" + this.$getShareUrlParams()
    };
  }
};
if (!Array) {
  const _component_Page = common_vendor.resolveComponent("Page");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  (_component_Page + _easycom_u_modal2)();
}
const _easycom_u_modal = () => "../../uni_modules/vk-uview-ui/components/u-modal/u-modal.js";
if (!Math) {
  _easycom_u_modal();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.pre_url + "/static/lv-upbanner.png",
    b: $data.userInfo.grade
  }, $data.userInfo.grade ? {
    c: common_vendor.t($data.userInfo.grade.name),
    d: common_vendor.t($data.userInfo.end_time)
  } : {}, {
    e: common_vendor.f($data.gradeList, (item, idx, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.t(item.upgrade.price),
        c: item.grade_id + "",
        d: idx
      };
    }),
    f: common_vendor.o((...args) => $options.changelevel && $options.changelevel(...args)),
    g: $data.isChoose
  }, $data.isChoose ? {
    h: common_vendor.s("background:url(" + $data.pre_url + "/static/lv-upbtn.png) no-repeat;background-size:100%"),
    i: common_vendor.o((...args) => $options.tobuy && $options.tobuy(...args))
  } : {}, {
    j: common_vendor.p({
      items: $data.items
    }),
    k: common_vendor.o($options.handleSubmit),
    l: common_vendor.o($options.closeAddressModal),
    m: common_vendor.o(($event) => $data.addressShow = $event),
    n: common_vendor.p({
      content: $data.content,
      ["show-cancel-button"]: true,
      modelValue: $data.addressShow
    }),
    o: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-6073b5bd"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/vip/index.js.map
