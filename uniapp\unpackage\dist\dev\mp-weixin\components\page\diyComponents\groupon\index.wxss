/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.diy-groupon .goods-item--container.data-v-12bdf868 {
  margin-bottom: 20rpx;
}
.diy-groupon .goods-item--container.data-v-12bdf868:last-child {
  margin-bottom: 0 !important;
}
.diy-groupon .goods-item.data-v-12bdf868 {
  padding: 28rpx 24rpx;
  display: flex;
  background: #fff;
  box-sizing: border-box;
}
.diy-groupon .goods-item.display-card.data-v-12bdf868 {
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.07);
}
.diy-groupon .goods-item.border-round.data-v-12bdf868 {
  border-radius: 14rpx;
}
.diy-groupon .goods-item-left.data-v-12bdf868 {
  position: relative;
  background: #fff;
  margin-right: 20rpx;
}
.diy-groupon .goods-item-left .label.data-v-12bdf868 {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  background: linear-gradient(to right, #ffa600, #f5b914);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 8rpx;
  border-radius: 8rpx;
}
.diy-groupon .goods-item-left .image.data-v-12bdf868 {
  display: block;
  width: 220rpx;
  height: 220rpx;
  border-radius: 10rpx;
}
.diy-groupon .goods-item-right.data-v-12bdf868 {
  position: relative;
  flex: 1;
}
.diy-groupon .goods-item-right .goods-name.data-v-12bdf868 {
  display: block;
  width: 100%;
  font-size: 28rpx;
  color: #333;
  min-height: 68rpx;
  line-height: 1.3;
}
.diy-groupon .goods-item-desc.data-v-12bdf868 {
  margin-top: 20rpx;
}
.diy-groupon .goods-item-desc .desc_situation.data-v-12bdf868 {
  font-size: 26rpx;
  line-height: 1.3;
  color: var(--main-bg);
  margin-top: 20rpx;
}
.diy-groupon .goods-item-desc .state-tag.data-v-12bdf868 {
  display: inline-block;
  margin-right: 14rpx;
}
.diy-groupon .goods-item-desc .desc-footer.data-v-12bdf868 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  right: 0rpx;
  bottom: 0rpx;
  min-height: 44rpx;
}
.diy-groupon .goods-item-desc .desc-footer .item-status.data-v-12bdf868 {
  color: var(--main-bg);
}
.diy-groupon .goods-item-desc .desc-footer .item-prices.data-v-12bdf868 {
  padding-right: 6rpx;
}
.diy-groupon .goods-item-desc .desc-footer .item-prices .price-x.data-v-12bdf868 {
  margin-right: 14rpx;
  color: var(--main-bg);
  font-size: 28rpx;
}
.diy-groupon .goods-item-desc .desc-footer .item-prices .price-y.data-v-12bdf868 {
  color: #999;
  text-decoration: line-through;
  font-size: 24rpx;
}
.diy-groupon .goods-item-desc .desc-footer .settlement.data-v-12bdf868 {
  padding: 0 30rpx;
  line-height: 56rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 40rpx;
  color: #fff;
  background: var(--main-bg);
}