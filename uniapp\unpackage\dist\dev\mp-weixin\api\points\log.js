"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "points.log/list",
  getData: "points.log/getData",
  center: "points.log/center",
  submit: "points.log/submit"
};
const submit = (data) => {
  return utils_request_index.$http.post(api.submit, data);
};
const center = (param) => {
  return utils_request_index.$http.get(api.center, param);
};
const getData = (param) => {
  return utils_request_index.$http.get(api.getData, param);
};
const list = (param) => {
  return utils_request_index.$http.get(api.list, param);
};
exports.center = center;
exports.getData = getData;
exports.list = list;
exports.submit = submit;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/points/log.js.map
