"use strict";
const common_vendor = require("../../common/vendor.js");
const api_xj_video = require("../../api/xj/video.js");
const Shortcut = () => "../../components/shortcut/index.js";
const _sfc_main = {
  components: {
    Shortcut
  },
  data() {
    return {
      videoList: [],
      // 当前文章ID
      articleId: null,
      // 加载中
      isLoading: true,
      // 当前文章详情
      detail: null,
      autoplay: false,
      page: 1,
      length: 1,
      options: {},
      // 视频播放状态
      videoPlayingStates: {},
      // 视频结束状态
      videoEndedStates: {}
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.articleId = options.articleId;
    this.options = options;
    this.getList(options.categoryId, options.articleId);
  },
  methods: {
    playVideo(index) {
      this.videoList.forEach((_, i) => {
        if (i !== index) {
          const videoContext = common_vendor.index.createVideoContext("video" + i, this);
          videoContext.pause();
          this.$set(this.videoPlayingStates, i, false);
        }
      });
      const currentVideoContext = common_vendor.index.createVideoContext("video" + index, this);
      currentVideoContext.play();
      this.$set(this.videoPlayingStates, index, true);
    },
    // 处理视频点击事件
    handleVideoClick(index) {
      if (this.videoEndedStates[index]) {
        return;
      }
      const videoContext = common_vendor.index.createVideoContext("video" + index, this);
      const isPlaying = this.videoPlayingStates[index];
      if (isPlaying) {
        videoContext.pause();
        this.$set(this.videoPlayingStates, index, false);
      } else {
        videoContext.play();
        this.$set(this.videoPlayingStates, index, true);
      }
    },
    change(e) {
      var index = e.detail.current;
      this.articleId = this.videoList[index].id;
      if (index + 2 == this.length) {
        common_vendor.index.__f__("log", "at pages/video/detail.vue:109", index);
        common_vendor.index.__f__("log", "at pages/video/detail.vue:110", this.length);
        this.page = this.page + 1;
        this.getList(this.options.categoryId, this.options.articleId);
      }
      this.playVideo(index);
    },
    getList(categoryId, videoId) {
      let app = this;
      api_xj_video.listDetail(categoryId, videoId, app.page).then((result) => {
        var oldList = app.videoList;
        app.videoList = oldList.concat(result.data.list);
        app.length = app.videoList.length;
        if (app.page == 1) {
          app.playVideo(0);
        }
        common_vendor.index.__f__("log", "at pages/video/detail.vue:129", app.videoList);
      });
    },
    screenChange(e) {
      let fullScreen = e.detail.fullScreen;
      common_vendor.index.__f__("log", "at pages/video/detail.vue:136", e, "全屏");
      if (!fullScreen) {
        this.videoPlay = false;
        common_vendor.index.navigateBack();
      }
    },
    endChange() {
      const currentIndex = this.getCurrentVideoIndex();
      this.$set(this.videoEndedStates, currentIndex, true);
      this.$set(this.videoPlayingStates, currentIndex, false);
      const app = this;
      api_xj_video.addView(app.articleId).then((result) => {
        if (result.data.score > 0) {
          common_vendor.index.showToast({
            title: "获得" + result.data.score + "积分",
            icon: "none"
          });
        }
      });
    },
    // 获取当前播放视频的索引
    getCurrentVideoIndex() {
      for (let i = 0; i < this.videoList.length; i++) {
        if (this.videoPlayingStates[i]) {
          return i;
        }
      }
      return 0;
    },
    // 获取文章详情
    getArticleDetail() {
      const app = this;
      app.isLoading = true;
      api_xj_video.detail(app.articleId).then((result) => {
        app.detail = result.data.detail;
        common_vendor.index.setNavigationBarTitle({
          title: result.data.detail.title
        });
      }).finally(() => app.isLoading = false);
    },
    // 监听视频时间更新
    onTimeUpdate(e, index) {
      const currentTime = e.detail.currentTime;
      const duration = e.detail.duration;
      this.$set(this.videoList[index], "currentTime", currentTime);
      this.$set(this.videoList[index], "duration", duration);
    },
    // 格式化时间显示
    formatTime(seconds) {
      if (!seconds || seconds < 0)
        return "00:00";
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    const app = this;
    const params = app.$getShareUrlParams({
      articleId: app.articleId
    });
    return {
      title: app.detail.title,
      path: "/pages/article/detail?" + params
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    const app = this;
    const params = app.$getShareUrlParams({
      articleId: app.articleId
    });
    return {
      title: app.detail.title,
      path: "/pages/article/detail?" + params
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.videoList, (item, index, i0) => {
      return {
        a: common_vendor.t($options.formatTime(item.currentTime || 0)),
        b: common_vendor.t($options.formatTime(item.duration || 0)),
        c: "video" + index,
        d: item.video_url,
        e: item.autoplay,
        f: common_vendor.o(($event) => $options.playVideo(index), index),
        g: common_vendor.o((...args) => $options.endChange && $options.endChange(...args), index),
        h: common_vendor.o(($event) => $options.onTimeUpdate($event, index), index),
        i: common_vendor.o(($event) => $options.handleVideoClick(index), index),
        j: index
      };
    }),
    b: common_vendor.o((...args) => $options.change && $options.change(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-ade757f3"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/video/detail.js.map
