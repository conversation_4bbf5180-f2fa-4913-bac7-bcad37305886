"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  order: "bargain.checkout/order",
  submit: "bargain.checkout/submit"
};
const order = (mode, param) => {
  return utils_request_index.$http.get(api.order, { mode, ...param });
};
const submit = (mode, data) => {
  return utils_request_index.$http.post(api.submit, { mode, ...data }, { isPrompt: false });
};
const BargainCheckoutApi = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  order,
  submit
}, Symbol.toStringTag, { value: "Module" }));
exports.BargainCheckoutApi = BargainCheckoutApi;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/bargain/checkout.js.map
