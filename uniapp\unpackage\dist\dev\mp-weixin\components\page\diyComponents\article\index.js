"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  name: "Article",
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemIndex: String,
    params: Object,
    dataList: Array
  },
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {
    /**
     * 跳转文章详情页
     */
    onTargetDetail(id) {
      common_vendor.index.navigateTo({
        url: "/pages/article/detail?articleId=" + id
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.dataList, (item, index, i0) => {
      return common_vendor.e({
        a: item.show_type == 10
      }, item.show_type == 10 ? {
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.show_views),
        d: item.image_url
      } : {}, {
        e: item.show_type == 20
      }, item.show_type == 20 ? {
        f: common_vendor.t(item.title),
        g: item.image_url,
        h: common_vendor.t(item.show_views)
      } : {}, {
        i: common_vendor.n(`show-type__${item.show_type}`),
        j: index,
        k: common_vendor.o(($event) => $options.onTargetDetail(item.article_id), index)
      });
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-568c6024"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/components/page/diyComponents/article/index.js.map
