/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.i-card.data-v-b1133573 {
  background: #fff;
  padding: 24rpx 24rpx;
}
.express.data-v-b1133573 {
  margin-bottom: 20rpx;
}
.express .info-item.data-v-b1133573 {
  display: flex;
  margin-bottom: 24rpx;
}
.express .info-item.data-v-b1133573:last-child {
  margin-bottom: 0;
}
.express .info-item .item-lable.data-v-b1133573 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-right: 6rpx;
}
.express .info-item .item-content.data-v-b1133573 {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #333;
}
.express .info-item .item-content .act-copy.data-v-b1133573 {
  margin-left: 20rpx;
  padding: 2rpx 20rpx;
  font-size: 22rpx;
  color: #666;
  border: 1rpx solid #c1c1c1;
  border-radius: 16rpx;
}
.deliver-goods-list.data-v-b1133573 {
  margin-top: 20rpx;
  margin-bottom: -30rpx;
}
.deliver-goods-list .goods-item.data-v-b1133573 {
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
  width: 130rpx;
  height: 130rpx;
  float: left;
  margin-right: 30rpx;
  margin-bottom: 30rpx;
}
.deliver-goods-list .goods-img.data-v-b1133573 {
  display: block;
  width: 100%;
  height: 100%;
}
.deliver-goods-list .title.data-v-b1133573 {
  position: absolute;
  bottom: 0;
  width: 100%;
  text-align: center;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 4rpx 0;
  font-size: 24rpx;
}
.logis-detail.data-v-b1133573 {
  padding: 30rpx;
  background-color: #fff;
}
.logis-detail .logis-item.data-v-b1133573 {
  position: relative;
  padding: 10px 0 10px 25px;
  box-sizing: border-box;
  border-left: 2px solid #ccc;
}
.logis-detail .logis-item.first.data-v-b1133573 {
  border-left: 2px solid #f40;
}
.logis-detail .logis-item.first.data-v-b1133573:after {
  background: #f40;
}
.logis-detail .logis-item.first .logis-item-content.data-v-b1133573 {
  background: #ff6e39;
  color: #fff;
}
.logis-detail .logis-item.first .logis-item-content.data-v-b1133573:after {
  border-bottom-color: #ff6e39;
}
.logis-detail .logis-item.data-v-b1133573:after {
  content: " ";
  display: inline-block;
  position: absolute;
  left: -6px;
  top: 30px;
  width: 6px;
  height: 6px;
  border-radius: 10px;
  background: #bdbdbd;
  border: 2px solid #fff;
}
.logis-detail .logis-item .logis-item-content.data-v-b1133573 {
  position: relative;
  background: #f9f9f9;
  padding: 10rpx 20rpx;
  box-sizing: border-box;
  color: #666;
}
.logis-detail .logis-item .logis-item-content.data-v-b1133573:after {
  content: "";
  display: inline-block;
  position: absolute;
  left: -10px;
  top: 18px;
  border-left: 10px solid #fff;
  border-bottom: 10px solid #f3f3f3;
}