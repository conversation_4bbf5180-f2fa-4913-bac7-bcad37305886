"use strict";
const core_mixins_wxofficial = require("../../core/mixins/wxofficial.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const utils_color = require("../../utils/color.js");
const core_app = require("../../core/app.js");
const common_enum_sharp_ActiveStatus = require("../../common/enum/sharp/ActiveStatus.js");
require("../../common/enum/sharp/GoodsStatus.js");
const api_sharp_home = require("../../api/sharp/home.js");
const api_sharp_goods = require("../../api/sharp/goods.js");
const common_vendor = require("../../common/vendor.js");
const CountDown = () => "../../components/countdown/index.js";
const pageSize = 15;
const _sfc_main = {
  components: {
    CountDown
  },
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin, core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 是否正在加载中
      isLoading: true,
      // 当前tab索引
      curTabIndex: 0,
      // tab组件的左侧距离
      scrollLeft: 0,
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: false,
        // 每页数据的数量; 默认10
        page: { size: pageSize },
        // 数量要大于3条才显示无更多数据
        noMoreSize: 3
      },
      // 枚举类
      ActiveStatusEnum: common_enum_sharp_ActiveStatus.ActiveStatusEnum,
      // 秒杀活动场次
      tabbar: [],
      // 秒杀商品列表
      goodsList: []
    };
  },
  computed: {
    // 进度条背景颜色
    progressBackgroundColor() {
      return utils_color.hex2rgba(this.appTheme.mainBg, 0.2);
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.onRefreshPage();
    this.setWxofficialShareData();
  },
  methods: {
    // 加载页面数据
    onRefreshPage() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_sharp_home.data().then((result) => {
          app.tabbar = result.data.tabbar;
          app.goodsList = result.data.goodsList;
          app.curTabIndex = 0;
          app.scrollLeft = 0;
          if (!app.goodsList.data.length) {
            app.mescroll.showEmpty();
          }
          resolve();
        }).catch(reject);
      });
    },
    /**
     * 获取商品列表
     * @param {Number} pageNo 页码
     */
    getListData(pageNo = 1) {
      const app = this;
      const activeTimeId = app.getCurTabbarId();
      return new Promise((resolve, reject) => {
        api_sharp_goods.list(activeTimeId, { page: pageNo }, { load: false }).then((result) => {
          const newList = result.data.list;
          app.goodsList.data = core_app.getMoreListData(newList, app.goodsList, pageNo);
          resolve(newList);
        }).catch(reject);
      });
    },
    // 下拉刷新的回调
    downCallback() {
      this.onRefreshPage().finally(() => this.mescroll.endSuccess());
    },
    /**
     * 上拉加载的回调
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getListData(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 监听tab组件滚动
    scroll({ detail }) {
      this.scrollLeft = detail.scrollLeft;
    },
    // 点击切换标签(会场场次)
    handleTab(index) {
      const app = this;
      app.curTabIndex = index;
      app.goodsList = core_app.getEmptyPaginateObj();
      app.mescroll.resetUpScroll();
    },
    // 获取当前选择的会场
    getCurTabbar() {
      return this.tabbar[this.curTabIndex];
    },
    // 获取当前会场场次ID
    getCurTabbarId() {
      const curTabbar = this.getCurTabbar();
      return curTabbar ? curTabbar.active_time_id : 0;
    },
    // 跳转到秒杀商品详情
    handleTargetGoods(sharpGoodsId) {
      this.$navTo("pages/sharp/goods/index", {
        activeTimeId: this.getCurTabbarId(),
        sharpGoodsId
      });
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      this.updateShareCardData({ title: "整点秒杀会场" });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    const params = this.$getShareUrlParams();
    return {
      title: "整点秒杀会场",
      path: `/pages/sharp/index?${params}`
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    const params = this.$getShareUrlParams();
    return {
      title: "整点秒杀会场",
      path: `/pages/sharp/index?${params}`
    };
  }
};
if (!Array) {
  const _component_count_down = common_vendor.resolveComponent("count-down");
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  (_component_count_down + _easycom_mescroll_body2)();
}
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  _easycom_mescroll_body();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.tabbar, (item, index, i0) => {
      return common_vendor.e({
        a: item.status == $data.ActiveStatusEnum.STATE_NOTICE.value
      }, item.status == $data.ActiveStatusEnum.STATE_NOTICE.value ? {
        b: common_vendor.t(item.status_text)
      } : {
        c: common_vendor.t(item.active_time),
        d: common_vendor.t(item.status_text)
      }, {
        e: index,
        f: $data.curTabIndex == index ? 1 : "",
        g: common_vendor.o(($event) => $options.handleTab(index), index)
      });
    }),
    b: $data.scrollLeft,
    c: common_vendor.o((...args) => $options.scroll && $options.scroll(...args)),
    d: $data.tabbar.length
  }, $data.tabbar.length ? common_vendor.e({
    e: $data.tabbar[$data.curTabIndex].status != $data.ActiveStatusEnum.STATE_NOTICE.value
  }, $data.tabbar[$data.curTabIndex].status != $data.ActiveStatusEnum.STATE_NOTICE.value ? {
    f: common_vendor.t($data.tabbar[$data.curTabIndex].active_time)
  } : {}, {
    g: common_vendor.t($data.tabbar[$data.curTabIndex].status_text2),
    h: common_vendor.t($data.tabbar[$data.curTabIndex].status == $data.ActiveStatusEnum.STATE_BEGIN.value ? "距结束" : "距开始"),
    i: common_vendor.p({
      date: $data.tabbar[$data.curTabIndex].count_down_time,
      separator: "colon",
      theme: "custom"
    })
  }) : {}, {
    j: common_vendor.f($data.goodsList.data, (item, index, i0) => {
      return common_vendor.e({
        a: item.goods_image,
        b: common_vendor.t(item.goods_name),
        c: `${item.progress}%`,
        d: common_vendor.t(item.progress),
        e: common_vendor.t(item.sales_actual),
        f: common_vendor.t(item.seckill_price_min),
        g: common_vendor.t(item.original_price)
      }, $data.tabbar.length ? {
        h: common_vendor.t($data.tabbar[$data.curTabIndex].status == $data.ActiveStatusEnum.STATE_BEGIN.value ? "马上抢" : "查看商品")
      } : {}, {
        i: index,
        j: common_vendor.o(($event) => $options.handleTargetGoods(item.sharp_goods_id), index)
      });
    }),
    k: $options.progressBackgroundColor,
    l: $data.tabbar.length,
    m: common_vendor.sr("mescrollRef", "f4fbf777-0"),
    n: common_vendor.o(_ctx.mescrollInit),
    o: common_vendor.o($options.downCallback),
    p: common_vendor.o($options.upCallback),
    q: common_vendor.p({
      sticky: true,
      down: {
        native: true,
        auto: false
      },
      up: $data.upOption
    }),
    r: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f4fbf777"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/sharp/index.js.map
