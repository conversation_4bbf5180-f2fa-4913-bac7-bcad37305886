"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_verify = require("../../utils/verify.js");
const api_address = require("../../api/address.js");
const common_model_wxofficial_Setting = require("../../common/model/wxofficial/Setting.js");
const SelectRegion = () => "../../components/select-region/select-region.js";
const rules = {
  name: [{
    required: true,
    message: "请输入姓名",
    trigger: ["blur", "change"]
  }],
  phone: [{
    required: true,
    message: "请输入手机号",
    trigger: ["blur", "change"]
  }, {
    // 自定义验证函数
    validator: (rule, value, callback) => {
      return utils_verify.isMobile(value);
    },
    message: "手机号码不正确",
    // 触发器可以同时用blur和change
    trigger: ["blur"]
  }],
  region: [{
    required: true,
    message: "请选择省市区",
    trigger: ["blur", "change"],
    type: "array"
  }],
  detail: [{
    required: true,
    message: "请输入详细地址",
    trigger: ["blur", "change"]
  }]
};
const _sfc_main = {
  components: {
    SelectRegion
  },
  data() {
    return {
      form: {
        content: "",
        name: "",
        phone: "",
        region: [],
        detail: ""
      },
      rules,
      // 按钮禁用
      disabled: false
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },
  // 必须要在onReady生命周期，因为onLoad生命周期组件可能尚未创建完毕
  onReady() {
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    // 收货地址智能解析
    handleAnalysis() {
      const app2 = this;
      api_address.analysis(app2.form.content).then((result) => {
        const detail = result.data.detail;
        app2.createFormData(detail);
      });
    },
    // 选择微信收货地址（仅微信小程序端）
    chooseAddress() {
      const { form, $refs } = this;
      common_vendor.index.chooseAddress({
        success(res) {
          const names = $refs.sRegion.getOptionItemByNames(res);
          form.name = res.userName;
          form.phone = res.telNumber;
          form.detail = res.detailInfo;
          form.region = names.length > 0 ? names : [];
        },
        fail({ errMsg }) {
          app.$toast(errMsg);
          common_vendor.index.__f__("error", "at pages/address/create.vue:134", "获取微信收货地址失败：", errMsg);
        }
      });
    },
    // 选择微信收货地址（仅微信公众号端）
    openAddress() {
      const { form, $refs } = this;
      common_model_wxofficial_Setting.WxofficialSettingModel.openAddress().then((res) => {
        alert(JSON.stringify(res));
        const names = $refs.sRegion.getOptionItemByNames(res);
        form.name = res.userName;
        form.phone = res.telNumber;
        form.detail = res.detailInfo;
        form.region = names.length > 0 ? names : [];
      });
    },
    // 生成默认的表单数据
    createFormData(detail) {
      const { form } = this;
      form.name = detail.name;
      form.phone = detail.phone;
      form.detail = detail.detail;
      form.region = this.createRegion(detail);
    },
    // 格式化地区数据 (用于select-region组件)
    createRegion(detail) {
      if (detail.province_id == 0 || detail.city_id == 0 || detail.region_id == 0) {
        this.$toast("很抱歉，地区未能识别请手动选择", 2e3);
        return [];
      }
      return [{
        label: detail.region.province,
        value: detail.province_id
      }, {
        label: detail.region.city,
        value: detail.city_id
      }, {
        label: detail.region.region,
        value: detail.region_id
      }];
    },
    // 表单提交
    handleSubmit() {
      const app2 = this;
      if (app2.disabled) {
        return false;
      }
      app2.$refs.uForm.validate((valid) => {
        if (valid) {
          app2.disabled = true;
          api_address.add(app2.form).then((result) => {
            app2.$toast(result.message);
            common_vendor.index.navigateBack();
          }).finally(() => app2.disabled = false);
        }
      });
    }
  }
};
if (!Array) {
  const _easycom_u_input2 = common_vendor.resolveComponent("u-input");
  const _easycom_u_form_item2 = common_vendor.resolveComponent("u-form-item");
  const _easycom_select_region2 = common_vendor.resolveComponent("select-region");
  const _easycom_u_form2 = common_vendor.resolveComponent("u-form");
  (_easycom_u_input2 + _easycom_u_form_item2 + _easycom_select_region2 + _easycom_u_form2)();
}
const _easycom_u_input = () => "../../uni_modules/vk-uview-ui/components/u-input/u-input.js";
const _easycom_u_form_item = () => "../../uni_modules/vk-uview-ui/components/u-form-item/u-form-item.js";
const _easycom_select_region = () => "../../components/select-region/select-region.js";
const _easycom_u_form = () => "../../uni_modules/vk-uview-ui/components/u-form/u-form.js";
if (!Math) {
  (_easycom_u_input + _easycom_u_form_item + _easycom_select_region + _easycom_u_form)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o(($event) => $data.form.name = $event),
    b: common_vendor.p({
      placeholder: "请输入收货人姓名",
      modelValue: $data.form.name
    }),
    c: common_vendor.p({
      label: "姓名",
      prop: "name"
    }),
    d: common_vendor.o(($event) => $data.form.phone = $event),
    e: common_vendor.p({
      placeholder: "请输入收货人手机号",
      modelValue: $data.form.phone
    }),
    f: common_vendor.p({
      label: "电话",
      prop: "phone"
    }),
    g: common_vendor.sr("sRegion", "534f32ea-6,534f32ea-5"),
    h: common_vendor.o(($event) => $data.form.region = $event),
    i: common_vendor.p({
      modelValue: $data.form.region
    }),
    j: common_vendor.p({
      label: "地区",
      prop: "region"
    }),
    k: common_vendor.o(($event) => $data.form.detail = $event),
    l: common_vendor.p({
      placeholder: "街道门牌、楼层等信息",
      modelValue: $data.form.detail
    }),
    m: common_vendor.p({
      label: "详细地址",
      prop: "detail",
      ["border-bottom"]: false
    }),
    n: common_vendor.sr("uForm", "534f32ea-0"),
    o: common_vendor.p({
      model: $data.form,
      ["label-width"]: "140rpx"
    }),
    p: common_vendor.o(($event) => $options.chooseAddress()),
    q: $data.disabled ? 1 : "",
    r: common_vendor.o(($event) => $options.handleSubmit()),
    s: common_vendor.s(_ctx.appThemeStyle)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-534f32ea"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/address/create.js.map
