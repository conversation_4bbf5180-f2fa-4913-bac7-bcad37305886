/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.recommended.data-v-8e960390 {
  margin-top: 60rpx;
}
.recommended .header.data-v-8e960390 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}
.recommended .header .image.data-v-8e960390 {
  display: block;
  width: 44rpx;
  height: 44rpx;
}
.recommended .header .title.data-v-8e960390 {
  margin: 0 26rpx;
  color: #333333;
  letter-spacing: 4rpx;
}
.recommended .goods-list.data-v-8e960390 {
  padding: 4rpx;
  box-sizing: border-box;
}
.recommended .goods-list.data-v-8e960390::after {
  clear: both;
  content: " ";
  display: table;
}
.recommended .goods-list .goods-item.data-v-8e960390 {
  box-sizing: border-box;
  padding: 6rpx;
}
.recommended .goods-list .goods-item .goods-image.data-v-8e960390 {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  overflow: hidden;
  background: #fff;
}
.recommended .goods-list .goods-item .goods-image.data-v-8e960390:after {
  content: "";
  display: block;
  margin-top: 100%;
}
.recommended .goods-list .goods-item .goods-image .image.data-v-8e960390 {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  object-fit: cover;
}
.recommended .goods-list .goods-item .detail.data-v-8e960390 {
  padding: 8rpx;
  background: #fff;
}
.recommended .goods-list .goods-item .detail .goods-name.data-v-8e960390 {
  white-space: normal;
  color: #484848;
  font-size: 26rpx;
  min-height: 68rpx;
  line-height: 1.3;
  margin-bottom: 8rpx;
}
.recommended .goods-list .goods-item .detail .detail-price .goods-price.data-v-8e960390 {
  margin-right: 8rpx;
  font-size: 30rpx;
  color: var(--main-bg);
}
.recommended .goods-list .goods-item .detail .detail-price .line-price.data-v-8e960390 {
  text-decoration: line-through;
}
.recommended .goods-list.display__slide.data-v-8e960390 {
  white-space: nowrap;
  font-size: 0;
}
.recommended .goods-list.display__slide .goods-item.data-v-8e960390 {
  display: inline-block;
}
.recommended .goods-list.display__list .goods-item.data-v-8e960390 {
  float: left;
}
.recommended .goods-list.column__2 .goods-item.data-v-8e960390 {
  width: 50%;
}
.recommended .goods-list.column__3 .goods-item.data-v-8e960390 {
  width: 33.33333%;
}
.recommended .goods-list.column__1 .goods-item.data-v-8e960390 {
  width: 100%;
  height: 280rpx;
  margin-bottom: 12rpx;
  padding: 20rpx;
  box-sizing: border-box;
  background: #fff;
  line-height: 1.6;
}
.recommended .goods-list.column__1 .goods-item.data-v-8e960390:last-child {
  margin-bottom: 0;
}
.recommended .goods-list.column__1 .goods-item-left.data-v-8e960390 {
  display: flex;
  width: 40%;
  background: #fff;
  align-items: center;
}
.recommended .goods-list.column__1 .goods-item-left .image.data-v-8e960390 {
  display: block;
  width: 240rpx;
  height: 240rpx;
}
.recommended .goods-list.column__1 .goods-item-right.data-v-8e960390 {
  position: relative;
  width: 60%;
}
.recommended .goods-list.column__1 .goods-item-right .goods-name.data-v-8e960390 {
  white-space: normal;
  color: #484848;
  font-size: 26rpx;
  min-height: 68rpx;
  line-height: 1.3;
  margin-bottom: 8rpx;
}
.recommended .goods-list.column__1 .goods-item-desc.data-v-8e960390 {
  margin-top: 8rpx;
}
.recommended .goods-list.column__1 .desc-selling-point.data-v-8e960390 {
  width: 400rpx;
  font-size: 24rpx;
  color: #e49a3d;
}
.recommended .goods-list.column__1 .desc-goods-sales.data-v-8e960390 {
  color: #999;
  font-size: 24rpx;
}
.recommended .goods-list.column__1 .desc-footer.data-v-8e960390 {
  font-size: 24rpx;
}
.recommended .goods-list.column__1 .desc-footer .price-x.data-v-8e960390 {
  margin-right: 16rpx;
  color: var(--main-bg);
  font-size: 30rpx;
}
.recommended .goods-list.column__1 .desc-footer .price-y.data-v-8e960390 {
  text-decoration: line-through;
}