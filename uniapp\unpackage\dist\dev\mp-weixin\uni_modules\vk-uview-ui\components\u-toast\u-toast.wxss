/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.u-toast.data-v-dcb3ce67 {
  position: fixed;
  z-index: -1;
  transition: opacity 0.3s;
  text-align: center;
  color: #fff;
  border-radius: 8rpx;
  background: #585858;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  opacity: 0;
  pointer-events: none;
  padding: 18rpx 40rpx;
}
.u-toast.u-show.data-v-dcb3ce67 {
  opacity: 1;
}
.u-icon.data-v-dcb3ce67 {
  margin-right: 10rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  line-height: normal;
}
.u-position-center.data-v-dcb3ce67 {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  max-width: 70%;
}
.u-position-top.data-v-dcb3ce67 {
  left: 50%;
  top: 20%;
  transform: translate(-50%, -50%);
}
.u-position-bottom.data-v-dcb3ce67 {
  left: 50%;
  bottom: 20%;
  transform: translate(-50%, -50%);
}
.u-type-primary.data-v-dcb3ce67 {
  color: #2979ff;
  background-color: #ecf5ff;
  border: 1px solid #d7eafe;
}
.u-type-success.data-v-dcb3ce67 {
  color: #19be6b;
  background-color: #dbf1e1;
  border: 1px solid #BEF5C8;
}
.u-type-error.data-v-dcb3ce67 {
  color: #fa3534;
  background-color: #fef0f0;
  border: 1px solid #fde2e2;
}
.u-type-warning.data-v-dcb3ce67 {
  color: #ff9900;
  background-color: #fdf6ec;
  border: 1px solid #faecd8;
}
.u-type-info.data-v-dcb3ce67 {
  color: #909399;
  background-color: #f4f4f5;
  border: 1px solid #ebeef5;
}
.u-type-default.data-v-dcb3ce67 {
  color: #fff;
  background-color: #585858;
}