/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-c3b8e2dd {
  padding: 100rpx 60rpx;
  min-height: 100vh;
  background-color: #fff;
}
.header.data-v-c3b8e2dd {
  margin-bottom: 50rpx;
}
.header .title.data-v-c3b8e2dd {
  color: #191919;
  font-size: 50rpx;
}
.header .sub-title.data-v-c3b8e2dd {
  margin-top: 20rpx;
  color: #b3b3b3;
  font-size: 25rpx;
}
.form-item.data-v-c3b8e2dd {
  display: flex;
  padding: 18rpx;
  border-bottom: 1rpx solid #f3f1f2;
  margin-bottom: 25rpx;
  height: 96rpx;
}
.form-item--input.data-v-c3b8e2dd {
  font-size: 26rpx;
  letter-spacing: 1rpx;
  flex: 1;
  height: 100%;
}
.form-item--parts.data-v-c3b8e2dd {
  min-width: 100rpx;
  height: 100%;
}
.form-item .captcha.data-v-c3b8e2dd {
  height: 100%;
}
.form-item .captcha .image.data-v-c3b8e2dd {
  display: block;
  width: 192rpx;
  height: 100%;
}
.form-item .captcha-sms.data-v-c3b8e2dd {
  font-size: 22rpx;
  line-height: 50rpx;
  padding-right: 20rpx;
}
.form-item .captcha-sms .activate.data-v-c3b8e2dd {
  color: #cea26a;
}
.form-item .captcha-sms .un-activate.data-v-c3b8e2dd {
  color: #9e9e9e;
}
.submit-button.data-v-c3b8e2dd {
  width: 100%;
  height: 86rpx;
  margin-top: 70rpx;
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
  border-radius: 80rpx;
  box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.1);
  letter-spacing: 5rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}