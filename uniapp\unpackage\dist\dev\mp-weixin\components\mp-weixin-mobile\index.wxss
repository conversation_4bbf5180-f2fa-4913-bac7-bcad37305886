/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.login-pop.data-v-1f197b9c {
  position: fixed;
  z-index: 999;
  width: 100%;
  background-color: #FFF;
  bottom: 0;
  padding: 30rpx 30rpx;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}
.login-pop .animation-shake.data-v-1f197b9c {
  animation: shake-1f197b9c 1s !important;
}
@keyframes shake-1f197b9c {
0%, 100% {
    transform: translateX(0);
}
10% {
    transform: translateX(-9rpx);
}
20% {
    transform: translateX(8rpx);
}
30% {
    transform: translateX(-7rpx);
}
40% {
    transform: translateX(6rpx);
}
50% {
    transform: translateX(-5rpx);
}
60% {
    transform: translateX(4rpx);
}
70% {
    transform: translateX(-3rpx);
}
80% {
    transform: translateX(2rpx);
}
90% {
    transform: translateX(-1rpx);
}
}
.login-pop .checkbox.data-v-1f197b9c {
  display: flex;
  justify-content: center;
  height: 45rpx;
  align-items: center;
  margin-right: 10rpx;
}
.login-pop .cycle.data-v-1f197b9c {
  width: 35rpx;
  height: 35rpx;
  border: solid 1rpx #a8a8a8;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.login-pop .active.data-v-1f197b9c {
  border: solid 1rpx #fa2209;
  color: #fa2209;
}
.login-pop .logo image.data-v-1f197b9c {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}
.login-pop .shop-name.data-v-1f197b9c {
  font-weight: 900;
  font-size: 40rpx;
}
.login-pop .btn.data-v-1f197b9c {
  padding: 80rpx 0;
  padding-bottom: 90rpx;
}
.login-pop .btn .login-btn.data-v-1f197b9c {
  background: #fa2209;
  height: 90rpx;
  line-height: 90rpx;
  width: 80%;
  margin-left: 10%;
  border-radius: 50rpx;
  color: #FFF;
  text-align: center;
  font-size: 32rpx;
}
.login-pop .btn .cancel.data-v-1f197b9c {
  border: solid 1rpx #fa2209;
  color: #fa2209;
  height: 90rpx;
  line-height: 90rpx;
  width: 80%;
  margin-left: 10%;
  border-radius: 50rpx;
  text-align: center;
  font-size: 32rpx;
  margin-top: 20rpx;
}
.login-pop .xieyi-box.data-v-1f197b9c {
  display: flex;
  justify-content: center;
  line-height: 45rpx;
}
.login-pop .tips-box.data-v-1f197b9c {
  position: absolute;
  top: -70rpx;
  left: -10rpx;
  width: 250rpx;
  color: #FFF;
  height: 60rpx;
  background-color: #666666;
  border-radius: 10rpx;
}
.login-pop .tips-box .box.data-v-1f197b9c {
  position: relative;
}
.login-pop .tips-box .tips.data-v-1f197b9c {
  line-height: 60rpx;
  text-align: center;
}
.login-pop .tips-box .icon.data-v-1f197b9c {
  position: absolute;
  top: 45rpx;
  left: 15rpx;
}