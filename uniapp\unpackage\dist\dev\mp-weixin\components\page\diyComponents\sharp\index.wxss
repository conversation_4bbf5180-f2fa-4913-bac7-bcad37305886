/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.diy-sharp .sharp-top.data-v-75512425 {
  display: flex;
  align-items: center;
}
.diy-sharp .sharp-top .sharp-top--left.data-v-75512425 {
  flex: 1;
  display: flex;
  align-items: center;
}
.diy-sharp .sharp-top .sharp-modular.data-v-75512425 {
  font-size: 28rpx;
  color: #fff;
  background: #FB571D;
  padding: 10rpx 30rpx 10rpx 24rpx;
  border-bottom-right-radius: 30rpx;
  border-top-right-radius: 30rpx;
}
.diy-sharp .sharp-top .sharp-modular .modular-name.data-v-75512425 {
  margin-left: 10rpx;
}
.diy-sharp .sharp-top .sharp-active-status.data-v-75512425 {
  color: #616161;
  font-size: 28rpx;
  margin-left: 20rpx;
  margin-right: 16rpx;
}
.diy-sharp .sharp-top .sharp-more.data-v-75512425 {
  display: flex;
  align-items: center;
  padding-right: 24rpx;
  color: #616161;
  font-size: 26rpx;
}
.diy-sharp .sharp-top .sharp-more .sharp-more-arrow.data-v-75512425 {
  font-size: 24rpx;
}
.diy-sharp .goods-list.data-v-75512425 {
  padding: 4rpx;
}
.diy-sharp .goods-list.display__list .goods-item.data-v-75512425 {
  float: left;
}
.diy-sharp .goods-list.column__2 .goods-item.data-v-75512425 {
  width: 50%;
}
.diy-sharp .goods-list.column__3 .goods-item.data-v-75512425 {
  width: 33.33333%;
}
.diy-sharp .goods-list .goods-item.data-v-75512425 {
  padding: 6rpx;
}
.diy-sharp .goods-list .goods-item .goods-image.data-v-75512425 {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  overflow: hidden;
  background: #fff;
}
.diy-sharp .goods-list .goods-item .goods-image.data-v-75512425:after {
  content: "";
  display: block;
  margin-top: 100%;
}
.diy-sharp .goods-list .goods-item .goods-image .image.data-v-75512425 {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  object-fit: cover;
}
.diy-sharp .goods-list .goods-item .detail.data-v-75512425 {
  padding: 4rpx;
  background: #fff;
}
.diy-sharp .goods-list .goods-item .detail .goods-name.data-v-75512425 {
  font-size: 26rpx;
  overflow: hidden;
  min-height: 68rpx;
  line-height: 1.3;
}
.diy-sharp .goods-list .goods-item .detail .detail-price.data-v-75512425 {
  line-height: 40rpx;
}
.diy-sharp .goods-list .goods-item .detail .detail-price .goods-price.data-v-75512425 {
  color: red;
  font-size: 30rpx;
  margin-right: 8rpx;
}
.diy-sharp .goods-list .goods-item .detail .detail-price .line-price.data-v-75512425 {
  font-size: 24rpx;
  text-decoration: line-through;
  color: #999;
}
.diy-sharp .goods-list .goods-item .detail .small-unit.data-v-75512425 {
  font-size: 26rpx;
  margin-right: 4rpx;
}