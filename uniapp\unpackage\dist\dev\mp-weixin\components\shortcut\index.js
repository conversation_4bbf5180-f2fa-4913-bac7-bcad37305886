"use strict";
const core_app = require("../../core/app.js");
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  props: {
    right: {
      type: Number,
      default: 30
    },
    bottom: {
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      // 弹窗显示控制
      isShow: false,
      transparent: true
    };
  },
  methods: {
    // 导航菜单切换事件
    handleToggleShow() {
      const app = this;
      app.isShow = !app.isShow;
      app.transparent = false;
    },
    // 导航页面跳转
    handleItem(index = 0) {
      const tabLinks = core_app.getTabBarLinks();
      this.$navTo(tabLinks[index]);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.n($data.isShow ? "show_80" : $data.transparent ? "" : "hide_80"),
    b: common_vendor.o(($event) => $options.handleItem(0)),
    c: common_vendor.n($data.isShow ? "show_60" : $data.transparent ? "" : "hide_60"),
    d: common_vendor.o(($event) => $options.handleItem(1)),
    e: common_vendor.n($data.isShow ? "show_40" : $data.transparent ? "" : "hide_40"),
    f: common_vendor.o(($event) => $options.handleItem(2)),
    g: common_vendor.n($data.isShow ? "show_20" : $data.transparent ? "" : "hide_20"),
    h: common_vendor.o(($event) => $options.handleItem(3)),
    i: $data.isShow ? 1 : "",
    j: common_vendor.o(($event) => $options.handleToggleShow()),
    k: `${_ctx.rightPx}rpx`,
    l: `${_ctx.bottomPx}rpx`
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4227a94a"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/shortcut/index.js.map
