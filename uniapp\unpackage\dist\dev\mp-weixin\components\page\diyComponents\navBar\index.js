"use strict";
const components_page_diyComponents_mixin = require("../mixin.js");
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  name: "NavBar",
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object,
    dataList: Array
  },
  mixins: [components_page_diyComponents_mixin.mixin],
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {}
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.dataList, (dataItem, index, i0) => {
      return {
        a: dataItem.imgUrl,
        b: common_vendor.t(dataItem.text),
        c: common_vendor.o(($event) => _ctx.onLink(dataItem.link), index),
        d: index
      };
    }),
    b: common_vendor.n(`avg-sm-${$props.itemStyle.rowsNum}`),
    c: $props.itemStyle.background,
    d: $props.itemStyle.textColor
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-60941f3d"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/components/page/diyComponents/navBar/index.js.map
