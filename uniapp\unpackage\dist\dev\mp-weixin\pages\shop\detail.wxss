/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
page {
  background: #fff;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-e68d3657 {
  background: #fff;
  padding: 0 30rpx;
}
.header.data-v-e68d3657 {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f1f1f1;
}
.header .shop-logo.data-v-e68d3657,
.header .shop-name.data-v-e68d3657 {
  text-align: center;
}
.header .shop-logo .image.data-v-e68d3657 {
  width: 130rpx;
  height: 130rpx;
  border-radius: 50%;
  box-shadow: 0 0 30rpx rgba(0, 0, 0, 0.1);
}
.header .shop-name.data-v-e68d3657 {
  margin-top: 16rpx;
  font-size: 32rpx;
}
.header .shop-summary.data-v-e68d3657 {
  padding: 20rpx;
  margin-top: 30rpx;
  font-size: 26rpx;
  line-height: 1.6;
  background: #f9f9f9;
  border-radius: 6rpx;
}
.content.data-v-e68d3657 {
  margin-top: 30rpx;
}
.content .content-item.data-v-e68d3657 {
  padding: 12rpx 0;
}
.content .content-item .content-item__text.data-v-e68d3657 {
  padding: 0 20rpx;
}