
page {
		background: #e8e8e8;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.footer.data-v-fcd717e4 {
  position: fixed;
  bottom: calc(constant(safe-area-inset-bottom) + 0rpx);
  bottom: calc(env(safe-area-inset-bottom) + 0rpx);
  background-color: #fa2209;
  height: 80rpx;
  line-height: 80rpx;
  width: 100%;
  text-align: center;
  color: #FFF;
}
.head.data-v-fcd717e4 {
  position: fixed;
  width: 100%;
  height: 350rpx;
  text-align: center;
  color: #FFF;
  background-color: #fa2209;
  padding: 10rpx 0;
  padding-bottom: 30rpx;
  border-bottom-left-radius: 10rpx;
  border-bottom-right-radius: 10rpx;
  z-index: 998;
}
.head .name.data-v-fcd717e4 {
  font-size: 30rpx;
}
.head .points.data-v-fcd717e4 {
  font-size: 40rpx;
  font-weight: bold;
  padding: 20rpx 0;
}
.head .sign.data-v-fcd717e4 {
  font-size: 24rpx;
  background-color: #ffaa00;
  border-radius: 50rpx;
  width: 250rpx;
  text-align: center;
  margin-left: 250rpx;
  padding: 10rpx 0rpx;
}
.head .bottom.data-v-fcd717e4 {
  padding: 20rpx 0;
  display: flex;
  justify-content: space-between;
}
.head .bottom .item.data-v-fcd717e4 {
  width: 33.33%;
  padding: 10rpx 0;
}
.head .bottom .item .num.data-v-fcd717e4 {
  font-size: 32rpx;
}
.head .bottom .item .label.data-v-fcd717e4 {
  font-size: 20rpx;
  padding-top: 10rpx;
}
.head .tips.data-v-fcd717e4 {
  text-align: right;
  padding-right: 20rpx;
  position: absolute;
  font-size: 24rpx;
  top: 20rpx;
  right: 0rpx;
}
.mid.data-v-fcd717e4 {
  position: fixed;
  top: 310rpx;
  text-align: center;
  font-weight: bold;
  width: 96%;
  margin-left: 2%;
  background-color: #FFF;
  border-top-right-radius: 10rpx;
  border-top-left-radius: 10rpx;
  height: 80rpx;
  line-height: 80rpx;
  z-index: 999;
}
.mid .log.data-v-fcd717e4 {
  position: absolute;
  top: 0;
  right: 20rpx;
  font-size: 20rpx;
  color: #888888;
}
.box.data-v-fcd717e4 {
  padding: 0 30rpx;
  position: absolute;
  top: 390rpx;
  width: 96%;
  margin-left: 2%;
  background-color: #FFF;
}
.log-list.data-v-fcd717e4 {
  padding-bottom: 120rpx;
}
.log-item.data-v-fcd717e4 {
  font-size: 28rpx;
  padding: 20rpx 20rpx;
  line-height: 1.8;
  border-bottom: 1rpx solid #eeeeee;
  display: flex;
  justify-content: center;
  align-items: center;
}
.rec-status.data-v-fcd717e4 {
  color: #333;
}
.rec-status .rec-time.data-v-fcd717e4 {
  color: #a0a0a0;
  font-size: 26rpx;
}