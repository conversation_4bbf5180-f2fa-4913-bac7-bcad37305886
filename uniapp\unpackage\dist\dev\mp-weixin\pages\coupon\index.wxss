/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.coupon-list.data-v-59672890 {
  padding: 20rpx;
}
.coupon-item.data-v-59672890 {
  margin-bottom: 22rpx;
  font-size: 24rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 80rpx 0 rgba(0, 0, 0, 0.04);
  position: relative;
}
.item-wrapper.data-v-59672890 {
  display: flex;
  align-items: center;
}
.item-wrapper.disable .coupon-tag.data-v-59672890 {
  background: linear-gradient(-113deg, #bdbdbd, #a2a1a2);
}
.item-wrapper.disable .coupon-reduce.data-v-59672890 {
  color: #757575;
}
.item-wrapper.disable .state-text.data-v-59672890 {
  color: #757575;
}
.coupon-tag.data-v-59672890 {
  position: absolute;
  left: 0;
  top: 0;
  color: #fff;
  width: 96rpx;
  text-align: center;
  font-size: 22rpx;
  border-radius: 12rpx 0 12rpx 0;
  font-weight: 500;
  height: 34rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
}
.coupon-left.data-v-59672890 {
  width: 242rpx;
  text-align: center;
  display: flex;
  flex-flow: column;
  justify-content: center;
  position: relative;
}
.coupon-left .coupon-reduce.data-v-59672890 {
  color: var(--main-bg);
}
.coupon-left .coupon-reduce-unit.data-v-59672890 {
  display: inline-block;
  margin-right: -4rpx;
  font-size: 32rpx;
  font-weight: 600;
}
.coupon-left .coupon-reduce-amount.data-v-59672890 {
  display: inline-block;
}
.coupon-left .coupon-reduce-amount .value.data-v-59672890 {
  font-size: 48rpx;
}
.coupon-left .coupon-hint.data-v-59672890 {
  margin-top: 12rpx;
  color: #757575;
  font-size: 24rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.coupon-content.data-v-59672890 {
  flex: 1;
  padding: 32rpx 0;
  position: relative;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  align-items: flex-start;
  justify-content: space-between;
}
.coupon-content .coupon-name.data-v-59672890 {
  color: #212121;
  font-size: 28rpx;
  font-weight: 500;
}
.coupon-content .coupon-middle.data-v-59672890 {
  flex: 1;
  padding-top: 12rpx;
  margin-bottom: 26rpx;
}
.coupon-content .coupon-middle .coupon-expire.data-v-59672890 {
  color: #757575;
  font-size: 24rpx;
}
.coupon-content .coupon-expand.data-v-59672890 {
  display: flex;
  align-items: center;
  color: #9e9e9e;
  font-size: 24rpx;
}
.coupon-content .coupon-expand-arrow.data-v-59672890 {
  margin-top: 4rpx;
  font-size: 24rpx;
  display: inline-block;
  vertical-align: initial;
  transform: rotate(0);
  margin-left: 8rpx;
  transition: all 0.15s ease-in-out;
}
.coupon-content .coupon-expand-arrow.expand.data-v-59672890 {
  transform: rotate(180deg);
}
.coupon-right.data-v-59672890 {
  padding-right: 38rpx;
}
.coupon-right .btn-receive.data-v-59672890,
.coupon-right .state-text.data-v-59672890 {
  text-align: center;
  width: 100rpx;
  padding: 15rpx 0;
}
.coupon-right .btn-receive.data-v-59672890 {
  font-size: 23rpx;
  line-height: 1;
  font-weight: 500;
  border-radius: 8rpx;
  cursor: pointer;
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
}
.coupon-expand-rules.data-v-59672890 {
  display: none;
  position: relative;
}
.coupon-expand-rules.expand.data-v-59672890 {
  top: -30rpx;
  display: block;
}
.coupon-expand-rules-content.data-v-59672890 {
  padding: 8rpx 30rpx 8rpx 242rpx;
  font-weight: 400;
  color: #9e9e9e;
  line-height: 36rpx;
}
.coupon-expand-rules-content .pre.data-v-59672890 {
  font-family: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
}