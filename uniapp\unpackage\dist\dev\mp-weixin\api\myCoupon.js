"use strict";
const utils_request_index = require("../utils/request/index.js");
const api = {
  list: "myCoupon/list",
  receive: "myCoupon/receive"
};
const list = (param, option) => {
  const options = {
    isPrompt: true,
    //（默认 true 说明：本接口抛出的错误是否提示）
    load: true,
    //（默认 true 说明：本接口是否提示加载动画）
    ...option
  };
  return utils_request_index.$http.get(api.list, param, options);
};
const receive = (couponId, data, option) => {
  return utils_request_index.$http.post(api.receive, { couponId, ...data }, option);
};
exports.list = list;
exports.receive = receive;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/myCoupon.js.map
