
page {
    background: #F4F4F4;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-ddd5fcdf {
  background-color: #F4F4F4;
}
.order-info.data-v-ddd5fcdf {
  padding: 80rpx 0;
  text-align: center;
}
.order-info .order-countdown.data-v-ddd5fcdf {
  display: flex;
  justify-content: center;
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 20rpx;
}
.order-info .order-amount.data-v-ddd5fcdf {
  margin: 0 auto;
  max-width: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #111111;
}
.order-info .order-amount .unit.data-v-ddd5fcdf {
  font-size: 30rpx;
  margin-bottom: -18rpx;
}
.order-info .order-amount .amount.data-v-ddd5fcdf {
  font-size: 56rpx;
}
.payment-method.data-v-ddd5fcdf {
  width: 94%;
  margin: 0 auto 20rpx auto;
  padding: 0 40rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
}
.payment-method .pay-item.data-v-ddd5fcdf {
  padding: 26rpx 0;
  font-size: 28rpx;
  border-bottom: 1rpx solid #f8f8f8;
}
.payment-method .pay-item.data-v-ddd5fcdf:last-child {
  border-bottom: none;
}
.payment-method .pay-item .item-left_icon.data-v-ddd5fcdf {
  margin-right: 20rpx;
  font-size: 44rpx;
}
.payment-method .pay-item .item-left_icon.wechat.data-v-ddd5fcdf {
  color: #00c800;
}
.payment-method .pay-item .item-left_icon.alipay.data-v-ddd5fcdf {
  color: #009fe8;
}
.payment-method .pay-item .item-left_icon.balance.data-v-ddd5fcdf {
  color: #ff9700;
}
.payment-method .pay-item .item-left_icon.offline.data-v-ddd5fcdf {
  color: #ed6a9b;
}
.payment-method .pay-item .item-left_text.data-v-ddd5fcdf {
  font-size: 28rpx;
}
.payment-method .pay-item .item-right.data-v-ddd5fcdf {
  font-size: 32rpx;
}
.payment-method .pay-item .user-balance.data-v-ddd5fcdf {
  margin-left: 20rpx;
  font-size: 26rpx;
}
.modal-content.data-v-ddd5fcdf {
  padding: 40rpx 48rpx;
  font-size: 30rpx;
  line-height: 50rpx;
  text-align: left;
  color: #606266;
  box-sizing: border-box;
}
.footer-fixed.data-v-ddd5fcdf {
  position: fixed;
  bottom: var(--window-bottom);
  left: 0;
  right: 0;
  z-index: 11;
  box-shadow: 0 -4rpx 40rpx 0 rgba(151, 151, 151, 0.24);
  background: #fff;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.footer-fixed .btn-wrapper.data-v-ddd5fcdf {
  height: 120rpx;
  display: flex;
  align-items: center;
  padding: 0 40rpx;
}
.footer-fixed .btn-item.data-v-ddd5fcdf {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  color: #fff;
  border-radius: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.footer-fixed .btn-item-main.data-v-ddd5fcdf {
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
}
.footer-fixed .btn-item-main.disabled.data-v-ddd5fcdf {
  opacity: 0.6;
}