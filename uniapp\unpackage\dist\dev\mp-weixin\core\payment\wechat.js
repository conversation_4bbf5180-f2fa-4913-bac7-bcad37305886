"use strict";
const common_vendor = require("../../common/vendor.js");
const core_platform = require("../platform.js");
const utils_storage = require("../../utils/storage.js");
const common_enum_Client = require("../../common/enum/Client.js");
const common_enum_payment_Method = require("../../common/enum/payment/Method.js");
const paymentAsWxMp = (option) => {
  const options = {
    timeStamp: "",
    nonceStr: "",
    package: "",
    signType: "",
    paySign: "",
    ...option
  };
  return new Promise((resolve, reject) => {
    common_vendor.index.requestPayment({
      provider: "wxpay",
      timeStamp: options.timeStamp,
      nonceStr: options.nonceStr,
      package: options.package,
      signType: options.signType,
      paySign: options.paySign,
      success(res) {
        const option2 = {
          isRequireQuery: true,
          // 是否需要主动查单
          outTradeNo: options.out_trade_no,
          // 交易订单号
          method: "wechat"
        };
        resolve({ res, option: option2 });
      },
      fail: (res) => reject(res)
    });
  });
};
const paymentAsH5 = (option) => {
  const options = { orderKey: null, mweb_url: "", h5_url: "", ...option };
  utils_storage.storage.set("tempUnifyData_" + options.orderKey, {
    method: common_enum_payment_Method.PayMethodEnum.WECHAT.value,
    outTradeNo: options.out_trade_no
  }, 60 * 60);
  return new Promise((resolve, reject) => {
    const url = options.mweb_url || options.h5_url;
    if (url) {
      window.location.href = url;
    }
  });
};
const paymentAsOfficial = (option) => {
  const options = {
    appId: "",
    timeStamp: "",
    nonceStr: "",
    package: "",
    signType: "",
    paySign: "",
    ...option
  };
  return onBridgeReady(options);
};
const paymentAsApp = (options) => {
  return new Promise((resolve, reject) => {
    common_vendor.index.requestPayment({
      provider: "wxpay",
      orderInfo: {
        partnerid: options.partnerid,
        appid: options.appid,
        package: "Sign=WXPay",
        noncestr: options.noncestr,
        sign: options.sign,
        prepayid: options.prepayid,
        timestamp: options.timestamp
      },
      success(res) {
        resolve({ res, option: { isRequireQuery: true, outTradeNo: options.out_trade_no, method: "wechat" } });
      },
      fail: (res) => reject(res)
    });
  });
};
const payment = (option) => {
  const events = {
    [common_enum_Client.ClientEnum.H5.value]: paymentAsH5,
    [common_enum_Client.ClientEnum.MP_WEIXIN.value]: paymentAsWxMp,
    [common_enum_Client.ClientEnum.WXOFFICIAL.value]: paymentAsOfficial,
    [common_enum_Client.ClientEnum.APP.value]: paymentAsApp
  };
  return events[core_platform.platfrom](option);
};
const extraAsUnify = () => {
  return {};
};
exports.extraAsUnify = extraAsUnify;
exports.payment = payment;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/core/payment/wechat.js.map
