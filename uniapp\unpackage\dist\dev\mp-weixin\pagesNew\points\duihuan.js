"use strict";
const common_vendor = require("../../common/vendor.js");
const api_dealer_index = require("../../api/dealer/index.js");
const AvatarImage = () => "../../components/avatar-image/index.js";
const _sfc_main = {
  components: {
    AvatarImage
  },
  data() {
    return {
      points: 0,
      apply: {},
      // 正在加载
      isLoading: true,
      // 当前用户信息
      user: void 0,
      // 当前是否为分销商
      isDealer: false,
      // 当前分销商信息
      dealer: void 0,
      // 推荐人昵称
      refereeName: void 0,
      // 分销设置
      setting: {
        background: void 0,
        words: void 0
      }
    };
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getCenter();
  },
  methods: {
    // 获取分销商中心数据
    getCenter() {
      const app = this;
      app.isLoading = true;
      api_dealer_index.center().then((result) => {
        const data = result.data;
        app.isDealer = data.isDealer;
        app.user = data.user;
        app.dealer = data.dealer;
        app.refereeName = data.refereeName;
        app.setting = data.setting;
        app.points = data.points;
        app.apply = data.apply;
        app.isLoading = false;
      });
    },
    // 设置当前页面标题
    setPageTitle() {
      common_vendor.index.setNavigationBarTitle({
        title: this.setting.words.index.title.value
      });
    }
  }
};
if (!Array) {
  const _component_avatar_image = common_vendor.resolveComponent("avatar-image");
  _component_avatar_image();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? {
    b: $data.setting.background,
    c: common_vendor.p({
      url: $data.user.avatar_url,
      width: 150,
      borderWidth: 4,
      borderColor: `#fff`
    }),
    d: common_vendor.t($data.user.nick_name),
    e: common_vendor.t($data.setting.words.index.words.withdraw.value),
    f: common_vendor.o(($event) => _ctx.$navTo("pagesNew/points/apply")),
    g: common_vendor.t($data.setting.words.index.words.total_money.value),
    h: common_vendor.t($data.setting.words.withdraw_list.title.value),
    i: common_vendor.o(($event) => _ctx.$navTo("pages/dealer/withdraw/list"))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b7926412"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesNew/points/duihuan.js.map
