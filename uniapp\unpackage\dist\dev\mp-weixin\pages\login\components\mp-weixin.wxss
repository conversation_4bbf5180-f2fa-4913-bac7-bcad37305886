/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-f2695243 {
  padding: 0 60rpx;
  font-size: 32rpx;
  background: #fff;
  min-height: 100vh;
}
.personal .header.data-v-f2695243 {
  padding-top: 120rpx;
  margin-bottom: 60rpx;
}
.personal .header .title.data-v-f2695243 {
  margin-bottom: 20rpx;
  color: #191919;
  font-size: 44rpx;
}
.personal .header .sub-title.data-v-f2695243 {
  margin-bottom: 70rpx;
  color: #b3b3b3;
  font-size: 28rpx;
}
.personal .login-form.data-v-f2695243 {
  margin-bottom: 90rpx;
}
.personal .form-item.data-v-f2695243 {
  display: flex;
  align-items: center;
  padding: 18rpx;
  border-bottom: 1rpx solid #f3f1f2;
  margin-bottom: 30rpx;
  min-height: 96rpx;
}
.personal .form-item--label.data-v-f2695243 {
  min-width: 150rpx;
  font-size: 28rpx;
  line-height: 50rpx;
}
.personal .form-item--input.data-v-f2695243 {
  font-size: 28rpx;
  letter-spacing: 1rpx;
  flex: 1;
  height: 100%;
}
.personal .form-item--parts.data-v-f2695243 {
  min-width: 100rpx;
}
.authorize .store-info.data-v-f2695243 {
  padding: 80rpx 0 48rpx;
  border-bottom: 1rpx solid #e3e3e3;
  margin-bottom: 72rpx;
  text-align: center;
}
.authorize .store-info .header.data-v-f2695243 {
  width: 190rpx;
  height: 190rpx;
  border: 4rpx solid #fff;
  margin: 0 auto 0;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 2rpx 0 10rpx rgba(50, 50, 50, 0.3);
}
.authorize .store-info .header .image.data-v-f2695243 {
  display: block;
  width: 100%;
  height: 100%;
}
.authorize .auth-title.data-v-f2695243 {
  color: #585858;
  font-size: 34rpx;
  margin-bottom: 40rpx;
}
.authorize .auth-subtitle.data-v-f2695243 {
  color: #888;
  margin-bottom: 88rpx;
  font-size: 28rpx;
}
.login-btn.data-v-f2695243 {
  margin-bottom: 20rpx;
}
.login-btn .button.data-v-f2695243 {
  width: 100%;
  height: 86rpx;
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
  font-size: 30rpx;
  border-radius: 80rpx;
  letter-spacing: 5rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.login-btn .button.disabled.data-v-f2695243 {
  opacity: 0.6;
}
.no-login-btn .button.data-v-f2695243 {
  height: 86rpx;
  background: #dfdfdf;
  color: #fff;
  font-size: 30rpx;
  border-radius: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}