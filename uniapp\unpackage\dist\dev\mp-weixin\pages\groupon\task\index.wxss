/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-f6c73a28 {
  position: relative;
  overflow: hidden;
}
.icon-arrow.data-v-f6c73a28 {
  display: inline-block;
  width: 0;
  height: 0;
  border-width: 10rpx;
  border-style: solid;
  border-color: transparent transparent transparent #ccc;
}
.bg-layer.data-v-f6c73a28 {
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
  height: 250rpx;
  text-align: center;
  line-height: 100rpx;
}
.bg-layer.data-v-f6c73a28::after {
  width: 140%;
  height: 250rpx;
  position: absolute;
  left: -20%;
  top: 0;
  z-index: -1;
  content: "";
  border-radius: 0 0 50% 50%;
  background-image: linear-gradient(180deg, #ff5644, #fd7524);
}
.goods-info.data-v-f6c73a28 {
  position: relative;
  margin: auto;
  margin-top: 30rpx;
  padding: 28rpx;
  padding-bottom: 22rpx;
  width: 92%;
  background: #fff;
  border-radius: 10rpx;
  box-sizing: border-box;
  box-shadow: 0px 5px 12px rgba(226, 226, 226, 0.62);
  display: flex;
}
.goods-info .goods-image.data-v-f6c73a28 {
  position: relative;
  margin-right: 30rpx;
}
.goods-info .goods-image .label.data-v-f6c73a28 {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  background: linear-gradient(to right, #ffa600, #f5b914);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 8rpx;
  border-radius: 8rpx;
}
.goods-info .goods-image .image.data-v-f6c73a28 {
  display: block;
  width: 240rpx;
  height: 240rpx;
  border-radius: 10rpx;
}
.goods-info .goods-detail.data-v-f6c73a28 {
  background: #fff;
}
.goods-info .goods-detail .goods-name.data-v-f6c73a28 {
  display: block;
  font-size: 28rpx;
  min-height: 68rpx;
  line-height: 1.3;
}
.goods-info .goods-detail .goods-price.data-v-f6c73a28 {
  margin: 20rpx 0;
  color: #eb5841;
}
.goods-info .goods-detail .goods-price .m-price.data-v-f6c73a28 {
  display: inline-block;
  font-size: 40rpx;
}
.goods-info .goods-detail .goods-price .line-price.data-v-f6c73a28 {
  display: inline-block;
  margin: 10rpx;
  font-size: 24rpx;
  color: #8e8e8e;
  text-decoration: line-through;
}
.goods-info .goods-detail .goods-tag.data-v-f6c73a28 {
  margin-bottom: -10rpx;
}
.goods-info .goods-detail .goods-tag .tag-item.data-v-f6c73a28 {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  line-height: 36rpx;
  border-radius: 5rpx;
  color: #fa3534;
  background: #fef0f0;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  box-sizing: border-box;
}
.goods-info.data-v-f6c73a28:after {
  display: block;
  clear: both;
  content: "";
  visibility: hidden;
  height: 0;
}
.main.data-v-f6c73a28 {
  position: relative;
  margin: auto;
  margin-top: 30rpx;
  padding: 50rpx 20rpx;
  background: #fff;
  box-sizing: border-box;
}
.main .main-user.data-v-f6c73a28 {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: 0 auto;
  width: 600rpx;
  margin-bottom: 30rpx;
}
.main .main-user .user-item.data-v-f6c73a28 {
  width: 100rpx;
  height: 100rpx;
  margin: 0 25rpx 25rpx 0;
  position: relative;
  box-sizing: border-box;
}
.main .main-user .user-item.data-v-f6c73a28:nth-child(5n+0) {
  margin-right: 0;
}
.main .main-user .user-item .user-role.data-v-f6c73a28 {
  width: 100%;
  text-align: center;
  height: 40rpx;
  position: absolute;
  left: 0;
  bottom: -1rpx;
}
.main .main-user .user-item .user-role .role-name.data-v-f6c73a28 {
  background: #eb5841;
  border-radius: 15rpx;
  display: inline-block;
  line-height: 1.4;
  font-size: 24rpx;
  color: #fff;
  width: 78rpx;
}
.main .main-user .user-item.user-item__wait.data-v-f6c73a28 {
  border-radius: 50%;
  background: #f8f8f8;
  border: 1rpx dashed #dbdbdb;
  display: flex;
  justify-content: center;
  align-items: center;
}
.main .main-user .user-item.user-item__wait text.data-v-f6c73a28 {
  color: #dbdbdb;
  font-size: 38rpx;
}
.main .button.data-v-f6c73a28 {
  display: block;
  margin-top: 40rpx;
  width: 550rpx;
  line-height: 84rpx;
  font-size: 28rpx;
  background-image: linear-gradient(90deg, #fe5246, #fb265a);
  border: none;
  box-shadow: 0px 3px 8px rgba(255, 8, 15, 0.25);
  text-align: center;
  margin: 0 auto;
  color: #fff;
  border-radius: 40rpx;
  animation: btn_anim-f6c73a28 0.9s linear infinite;
  transform-origin: center;
}
@keyframes btn_anim-f6c73a28 {
0% {
    transform: scale(1);
}
40% {
    transform: scale(1.05);
}
}
/* 拼团状态 */
.main_status.data-v-f6c73a28 {
  margin-bottom: 40rpx;
  font-size: 35rpx;
  text-align: center;
}
.main_status .status-icon.data-v-f6c73a28 {
  margin-right: 15rpx;
}
.main_status__fail.data-v-f6c73a28 {
  color: var(--main-bg);
}
.main_status__success.data-v-f6c73a28 {
  color: #08b625;
}

/* 倒计时 */
.main_tiem.data-v-f6c73a28 {
  margin-bottom: 50rpx;
  font-size: 30rpx;
  text-align: center;
  display: flex;
  justify-content: center;
}
.main_tiem .main_timer_color.data-v-f6c73a28 {
  color: #fc8434;
  margin: 0 6rpx;
}
.notice.data-v-f6c73a28 {
  display: flex;
  margin: auto;
  margin-top: 30rpx;
  padding: 35rpx 20rpx;
  background: #fff;
  border-radius: 10rpx;
  box-sizing: border-box;
  justify-content: space-between;
  align-items: center;
}
.notice .t-brief.data-v-f6c73a28 {
  font-size: 26rpx;
  color: #a6a6a6;
}
.pops-content.data-v-f6c73a28 {
  padding: 30rpx 48rpx;
  font-size: 28rpx;
  line-height: 44rpx;
  text-align: left;
  color: #606266;
  min-height: 320rpx;
  max-height: 640rpx;
  box-sizing: border-box;
}