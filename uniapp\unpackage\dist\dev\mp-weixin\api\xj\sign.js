"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "xj.sign/list",
  detail: "xj.sign/detail",
  addView: "xj.sign/addView"
};
function list(param, option) {
  return utils_request_index.$http.get(api.list, param, option);
}
function addView() {
  return utils_request_index.$http.get(api.addView, {});
}
exports.addView = addView;
exports.list = list;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/xj/sign.js.map
