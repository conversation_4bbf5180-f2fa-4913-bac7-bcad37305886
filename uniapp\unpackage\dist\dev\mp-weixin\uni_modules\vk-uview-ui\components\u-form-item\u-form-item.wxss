/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.u-form-item.data-v-361fbc0d {
  display: flex;
  flex-direction: row;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #303133;
  box-sizing: border-box;
  line-height: 70rpx;
  flex-direction: column;
}
.u-form-item__border-bottom--error.data-v-361fbc0d:after {
  border-color: #fa3534;
}
.u-form-item__body.data-v-361fbc0d {
  display: flex;
  flex-direction: row;
}
.u-form-item--left.data-v-361fbc0d {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-form-item--left__content.data-v-361fbc0d {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-right: 10rpx;
  flex: 1;
}
.u-form-item--left__content__icon.data-v-361fbc0d {
  margin-right: 8rpx;
}
.u-form-item--left__content--required.data-v-361fbc0d {
  position: absolute;
  left: -16rpx;
  vertical-align: middle;
  color: #fa3534;
  padding-top: 6rpx;
}
.u-form-item--left__content__label.data-v-361fbc0d {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}
.u-form-item--right.data-v-361fbc0d {
  flex: 1;
}
.u-form-item--right__content.data-v-361fbc0d {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}
.u-form-item--right__content__slot.data-v-361fbc0d {
  flex: 1;
}
.u-form-item--right__content__icon.data-v-361fbc0d {
  margin-left: 10rpx;
  color: #c0c4cc;
  font-size: 30rpx;
}
.u-form-item__message.data-v-361fbc0d {
  font-size: 24rpx;
  line-height: 24rpx;
  color: #fa3534;
  margin-top: 12rpx;
}