"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_util = require("../../../utils/util.js");
const core_payment_alipay = require("../../../core/payment/alipay.js");
const core_payment_wechat = require("../../../core/payment/wechat.js");
const common_enum_payment_Method = require("../../../common/enum/payment/Method.js");
require("../../../common/enum/order/DeliveryStatus.js");
require("../../../common/enum/order/DeliveryType.js");
require("../../../common/enum/order/OrderSource.js");
require("../../../common/enum/order/OrderStatus.js");
const common_enum_order_PayStatus = require("../../../common/enum/order/PayStatus.js");
require("../../../common/enum/order/ReceiptStatus.js");
require("../../../common/enum/order/OrderType.js");
const api_cashier_index = require("../../../api/cashier/index.js");
const CountDown = () => "../../../components/countdown/index.js";
const PayMethodIconEnum = {
  [common_enum_payment_Method.PayMethodEnum.WECHAT.value]: "icon-wechat-pay",
  [common_enum_payment_Method.PayMethodEnum.ALIPAY.value]: "icon-alipay",
  [common_enum_payment_Method.PayMethodEnum.BALANCE.value]: "icon-balance-pay",
  [common_enum_payment_Method.PayMethodEnum.OFFLINE.value]: "icon-offline-pay"
};
const PayMethodClientNameEnum = {
  [common_enum_payment_Method.PayMethodEnum.WECHAT.value]: "微信",
  [common_enum_payment_Method.PayMethodEnum.ALIPAY.value]: "支付宝"
};
const _sfc_main = {
  components: {
    CountDown
  },
  data() {
    return {
      // 加载中
      isLoading: true,
      // 确认按钮禁用
      disabled: false,
      // 枚举类
      PayMethodEnum: common_enum_payment_Method.PayMethodEnum,
      PayMethodIconEnum,
      PayMethodClientNameEnum,
      // 当前选中的支付方式
      curPaymentItem: null,
      // 当前订单ID
      orderId: null,
      // 当前结算订单信息
      order: {},
      // 个人信息
      personal: { balance: "0.00" },
      // 当前客户端的支付方式列表（后端根据platform判断）
      methods: [],
      // 支付确认弹窗
      showConfirmModal: false
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad({ orderId }) {
    this.orderId = Number(orderId);
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getCashierInfo();
  },
  methods: {
    // 获取收银台信息
    getCashierInfo() {
      const app = this;
      app.isLoading = true;
      api_cashier_index.orderInfo(app.orderId, { client: app.platform }).then((result) => {
        app.order = result.data.order;
        app.personal = result.data.personal;
        app.methods = result.data.paymentMethods;
        app.isLoading = false;
        app.setDefaultPayType();
        app.checkOrderPayStatus();
      });
    },
    // 设置默认的支付方式
    setDefaultPayType() {
      const app = this;
      if (app.disabled)
        return;
      if (app.curPaymentItem)
        return;
      const defaultIndex = app.methods.findIndex((item) => item.is_default == true);
      defaultIndex > -1 && app.handleSelectPayType(defaultIndex);
    },
    // 判断当前订单是否为已支付
    checkOrderPayStatus() {
      const app = this;
      if (app.order.pay_status == common_enum_order_PayStatus.PayStatusEnum.SUCCESS.value) {
        app.$toast("恭喜您，订单已付款成功");
        app.onSuccessNav();
      }
    },
    // 选择支付方式
    handleSelectPayType(index) {
      this.curPaymentItem = this.methods[index];
    },
    // 判断当前页面来源于浏览器返回并提示手动查单
    // 确认支付
    handleSubmit() {
      const app = this;
      if (!app.curPaymentItem) {
        app.$toast("您还没有选择支付方式");
        return;
      }
      if (app.disabled)
        return;
      app.disabled = true;
      api_cashier_index.orderPay(app.orderId, {
        method: app.curPaymentItem.method,
        client: app.platform,
        extra: app.getExtraAsUnify(app.curPaymentItem.method)
      }).then((result) => app.onSubmitCallback(result)).finally((err) => setTimeout(() => app.disabled = false, 10));
    },
    // 获取第三方支付的扩展参数
    getExtraAsUnify(method) {
      if (method === common_enum_payment_Method.PayMethodEnum.ALIPAY.value) {
        return core_payment_alipay.extraAsUnify();
      }
      if (method === common_enum_payment_Method.PayMethodEnum.WECHAT.value) {
        return core_payment_wechat.extraAsUnify();
      }
      return {};
    },
    // 订单提交成功后回调
    onSubmitCallback(result) {
      const app = this;
      const method = app.curPaymentItem.method;
      const paymentData = result.data.payment;
      if (utils_util.inArray(method, [common_enum_payment_Method.PayMethodEnum.BALANCE.value, common_enum_payment_Method.PayMethodEnum.OFFLINE.value])) {
        app.onShowSuccess(result);
      }
      if (method === common_enum_payment_Method.PayMethodEnum.ALIPAY.value) {
        common_vendor.index.__f__("log", "at pages/checkout/cashier/index.vue:244", "paymentData", paymentData);
        core_payment_alipay.payment({ orderKey: app.orderId, ...paymentData }).then((res) => app.onPaySuccess(res)).catch((err) => app.onPayFail(err));
      }
      if (method === common_enum_payment_Method.PayMethodEnum.WECHAT.value) {
        common_vendor.index.__f__("log", "at pages/checkout/cashier/index.vue:251", "paymentData", paymentData);
        core_payment_wechat.payment({ orderKey: app.orderId, ...paymentData }).then((res) => app.onPaySuccess(res)).catch((err) => app.onPayFail(err));
      }
    },
    // 订单支付成功的回调方法
    // 这里只是前端支付api返回结果success,实际订单是否支付成功 以后端的查单和异步通知为准
    onPaySuccess({ res, option: { isRequireQuery, outTradeNo, method } }) {
      const app = this;
      if (isRequireQuery) {
        app.onTradeQuery(outTradeNo, method);
        return true;
      }
      this.onShowSuccess(res);
    },
    // 显示支付成功信息并页面跳转
    onShowSuccess({ message }) {
      this.$toast(message || "订单支付成功");
      this.onSuccessNav();
    },
    // 订单支付失败
    onPayFail(err) {
      common_vendor.index.__f__("log", "at pages/checkout/cashier/index.vue:279", "onPayFail", err);
      const errMsg = err.message || "订单未支付";
      this.$error(errMsg);
    },
    // 已完成支付按钮事件: 请求后端查单
    onTradeQuery(outTradeNo, method) {
      const app = this;
      api_cashier_index.tradeQuery({ outTradeNo, method, client: app.platform }).then((result) => result.data.isPay ? app.onShowSuccess(result) : app.onPayFail(result)).finally(() => app.showConfirmModal = false);
    },
    // 支付成功后的跳转
    onSuccessNav() {
      common_vendor.index.$emit("syncRefresh", true);
      const pages = getCurrentPages();
      const lastPage = pages.length < 2 ? null : pages[pages.length - 2];
      const backRoutes = [
        "pages/order/index",
        "pages/order/detail"
      ];
      setTimeout(() => {
        if (lastPage && utils_util.inArray(lastPage.route, backRoutes)) {
          common_vendor.index.navigateBack();
        } else {
          this.$navTo("pages/order/index", {}, "redirectTo");
        }
      }, 1200);
    }
  }
};
if (!Array) {
  const _component_count_down = common_vendor.resolveComponent("count-down");
  _component_count_down();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    b: $data.order.showExpiration
  }, $data.order.showExpiration ? {
    c: common_vendor.p({
      date: $data.order.expirationTime,
      separator: "zh",
      theme: "text"
    })
  } : {}, {
    d: common_vendor.t($data.order.pay_price),
    e: common_vendor.f($data.methods, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.n($data.PayMethodIconEnum[item.method]),
        b: common_vendor.n(item.method),
        c: common_vendor.t($data.PayMethodEnum[item.method].name),
        d: item.method === $data.PayMethodEnum.BALANCE.value
      }, item.method === $data.PayMethodEnum.BALANCE.value ? {
        e: common_vendor.t($data.personal.balance)
      } : {}, {
        f: $data.curPaymentItem && $data.curPaymentItem.method == item.method
      }, $data.curPaymentItem && $data.curPaymentItem.method == item.method ? {} : {}, {
        g: index,
        h: common_vendor.o(($event) => $options.handleSelectPayType(index), index)
      });
    }),
    f: $data.disabled ? 1 : "",
    g: common_vendor.o(($event) => $options.handleSubmit()),
    h: common_vendor.s(_ctx.appThemeStyle)
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-ddd5fcdf"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/checkout/cashier/index.js.map
