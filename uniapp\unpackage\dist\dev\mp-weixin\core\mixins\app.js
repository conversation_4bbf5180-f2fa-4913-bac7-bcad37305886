"use strict";
require("../../common/vendor.js");
const store_index = require("../../store/index.js");
const core_platform = require("../platform.js");
const formatToLine = (value) => {
  return value.replace(/([A-Z])/g, "-$1").toLowerCase();
};
const appTheme2Str = (appTheme) => {
  let str = "";
  for (const index in appTheme) {
    const name = formatToLine(index);
    str += `--${name}:${appTheme[index]};`;
  }
  return str;
};
const mixin = {
  data() {
    return {
      platform: core_platform.platfrom
    };
  },
  computed: {
    appTheme: () => store_index.store.getters.appTheme,
    appThemeStyle: () => appTheme2Str(store_index.store.getters.appTheme)
  },
  mounted() {
  },
  methods: {}
};
exports.mixin = mixin;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/core/mixins/app.js.map
