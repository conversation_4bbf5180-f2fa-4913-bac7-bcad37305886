"use strict";
const components_page_diyComponents_mixin = require("../mixin.js");
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  name: "Window",
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object,
    dataList: Array
  },
  mixins: [components_page_diyComponents_mixin.mixin],
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {}
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.itemStyle.layout > -1
  }, $props.itemStyle.layout > -1 ? {
    b: common_vendor.f($props.dataList, (dataItem, index, i0) => {
      return {
        a: dataItem.imgUrl,
        b: common_vendor.o(($event) => _ctx.onLink(dataItem.link), index),
        c: index
      };
    }),
    c: `${$props.itemStyle.paddingTop * 2}rpx ${$props.itemStyle.paddingLeft * 2}rpx`,
    d: common_vendor.n(`avg-sm-${$props.itemStyle.layout}`)
  } : common_vendor.e({
    e: common_vendor.o(($event) => _ctx.onLink($props.dataList[0].link)),
    f: $props.dataList[0].imgUrl,
    g: `${$props.itemStyle.paddingTop * 2}rpx ${$props.itemStyle.paddingLeft * 2}rpx`,
    h: $props.dataList.length >= 2
  }, $props.dataList.length >= 2 ? {
    i: common_vendor.o(($event) => _ctx.onLink($props.dataList[1].link)),
    j: $props.dataList[1].imgUrl,
    k: `${$props.itemStyle.paddingTop * 2}rpx ${$props.itemStyle.paddingLeft * 2}rpx`
  } : {}, {
    l: $props.dataList.length >= 3
  }, $props.dataList.length >= 3 ? {
    m: common_vendor.o(($event) => _ctx.onLink($props.dataList[2].link)),
    n: $props.dataList[2].imgUrl,
    o: `${$props.itemStyle.paddingTop * 2}rpx ${$props.itemStyle.paddingLeft * 2}rpx`
  } : {}, {
    p: $props.dataList.length >= 4
  }, $props.dataList.length >= 4 ? {
    q: common_vendor.o(($event) => _ctx.onLink($props.dataList[3].link)),
    r: $props.dataList[3].imgUrl,
    s: `${$props.itemStyle.paddingTop * 2}rpx ${$props.itemStyle.paddingLeft * 2}rpx`
  } : {}), {
    t: $props.itemStyle.background,
    v: `${$props.itemStyle.paddingTop * 2}rpx ${$props.itemStyle.paddingLeft * 2}rpx`
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-84e67c85"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/components/page/diyComponents/window/index.js.map
