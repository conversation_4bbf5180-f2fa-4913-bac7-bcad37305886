"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_dealer_orderVip = require("../../../api/dealer/orderVip.js");
require("../../../utils/request/index.js");
const common_model_dealer_Setting = require("../../../common/model/dealer/Setting.js");
const common_enum_dealer_withdraw_PayType = require("../../../common/enum/dealer/withdraw/PayType.js");
require("../../../common/enum/dealer/withdraw/ApplyStatus.js");
const _sfc_main = {
  data() {
    return {
      options: {},
      // 枚举类
      PayTypeEnum: common_enum_dealer_withdraw_PayType.PayTypeEnum,
      // 正在加载
      isLoading: true,
      // 分销商用户信息
      dealer: void 0,
      // 当前提现方式(选中的)
      payment: void 0,
      // 分销结算设置
      settlement: void 0,
      // 文字设置
      words: void 0,
      // 背景图
      background: void 0,
      // 按钮禁用
      disabled: false
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.options = options;
    this.getSetting();
  },
  methods: {
    // 获取分销设置
    getSetting() {
      const app = this;
      common_model_dealer_Setting.SettingModel.data().then((setting) => {
        app.payment = setting.settlement.pay_type[0];
        app.settlement = setting.settlement;
        app.words = setting.words.withdraw_apply.words;
        app.background = setting.background.withdraw_apply;
      });
    },
    // 获取分销商
    getDealer() {
      const app = this;
      app.isLoading = true;
      (void 0)().then((result) => app.dealer = result.data.dealer).finally(() => app.isLoading = false);
    },
    // 设置当前页面标题
    setPageTitle(title) {
      common_vendor.index.setNavigationBarTitle({
        title: title.value
      });
    },
    // 切换支付选项
    handleChecked(value) {
      this.payment = value;
    },
    // 表单提交
    handleSubmit({
      detail
    }) {
      const app = this;
      if (!app.onValidation(detail.value)) {
        return false;
      }
      common_vendor.index.showModal({
        title: "友情提示",
        content: "确定提交提现申请吗？请确认填写无误",
        showCancel: true,
        success(res) {
          if (res.confirm) {
            app.onSubmit(detail.value);
          } else if (res.cancel) {
            app.disabled = false;
          }
        }
      });
    },
    // 提交提现申请
    onSubmit(data) {
      const app = this;
      app.disabled = true;
      data.pay_type = app.payment;
      common_vendor.index.requestSubscribeMessage({
        tmplIds: ["wEN5C0saXrKVBDG62C6mdFCxEGFJ6fK49KxArEv3NAg"],
        success(res) {
          common_vendor.index.__f__("log", "at pages/dealer/vip/apply.vue:213", res);
          api_dealer_orderVip.submit({
            orderId: app.options.orderId,
            form: data
          }).then((result) => {
            app.$toast(result.message);
            setTimeout(() => {
              let pages = getCurrentPages();
              pages[pages.length - 1];
              let prevPage = pages[pages.length - 2];
              prevPage.$vm.canReset = true;
              common_vendor.index.navigateBack({
                delta: 1
                //返回层数，2则上上页
              });
            }, 1200);
          }).finally(() => app.disabled = false);
        }
      });
    },
    // 表单验证
    onValidation(data) {
      const app = this;
      const words = app.words;
      if (app.options.money <= 0) {
        app.$error("当前没有" + words.capital.value);
        return false;
      }
      return true;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.background,
    b: common_vendor.t($data.options.money),
    c: common_vendor.f($data.settlement.pay_type, (item, index, i0) => {
      return common_vendor.e({
        a: item == $data.PayTypeEnum.WECHAT.value
      }, item == $data.PayTypeEnum.WECHAT.value ? {
        b: common_vendor.n($data.payment == $data.PayTypeEnum.WECHAT.value ? "c-violet" : "col-bb"),
        c: common_vendor.t($data.PayTypeEnum.WECHAT.name),
        d: common_vendor.o(($event) => $options.handleChecked($data.PayTypeEnum.WECHAT.value), index)
      } : {}, {
        e: item == $data.PayTypeEnum.ALIPAY.value
      }, item == $data.PayTypeEnum.ALIPAY.value ? common_vendor.e({
        f: common_vendor.n($data.payment == $data.PayTypeEnum.ALIPAY.value ? "c-violet" : "col-bb"),
        g: common_vendor.t($data.PayTypeEnum.ALIPAY.name),
        h: common_vendor.o(($event) => $options.handleChecked($data.PayTypeEnum.ALIPAY.value), index),
        i: $data.payment == $data.PayTypeEnum.ALIPAY.value
      }, $data.payment == $data.PayTypeEnum.ALIPAY.value ? {} : {}) : {}, {
        j: item == $data.PayTypeEnum.BANK_CARD.value
      }, item == $data.PayTypeEnum.BANK_CARD.value ? common_vendor.e({
        k: common_vendor.n($data.payment == $data.PayTypeEnum.BANK_CARD.value ? "c-violet" : "col-bb"),
        l: common_vendor.t($data.PayTypeEnum.BANK_CARD.name),
        m: common_vendor.o(($event) => $options.handleChecked($data.PayTypeEnum.BANK_CARD.value), index),
        n: $data.payment == $data.PayTypeEnum.BANK_CARD.value
      }, $data.payment == $data.PayTypeEnum.BANK_CARD.value ? {} : {}) : {}, {
        o: index
      });
    }),
    d: common_vendor.t($data.words.submit.value),
    e: $data.disabled,
    f: common_vendor.o((...args) => $options.handleSubmit && $options.handleSubmit(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-ba78718c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/dealer/vip/apply.js.map
