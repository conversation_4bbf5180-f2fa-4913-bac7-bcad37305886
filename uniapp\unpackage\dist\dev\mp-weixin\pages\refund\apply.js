"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../common/enum/order/refund/AuditStatus.js");
require("../../common/enum/order/refund/RefundStatus.js");
const common_enum_order_refund_RefundType = require("../../common/enum/order/refund/RefundType.js");
const api_upload = require("../../api/upload.js");
const api_refund = require("../../api/refund.js");
const maxImageLength = 6;
const _sfc_main = {
  data() {
    return {
      type: 0,
      // 枚举类
      RefundTypeEnum: common_enum_order_refund_RefundType.RefundTypeEnum,
      // 正在加载
      isLoading: true,
      // 订单商品id
      orderGoodsId: null,
      // 订单商品详情
      goods: {},
      // 表单数据
      formData: {
        // 图片上传成功的文件ID集
        images: [],
        // 服务类型
        type: 10,
        // 申请原因
        content: ""
      },
      // 用户选择的图片列表
      imageList: [],
      // 最大图片数量
      maxImageLength,
      // 按钮禁用
      disabled: false
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad({ orderGoodsId, type }) {
    this.orderGoodsId = orderGoodsId;
    this.type = type;
    this.getGoodsDetail();
  },
  methods: {
    // 获取订单商品详情
    getGoodsDetail() {
      const app = this;
      app.isLoading = true;
      api_refund.goods(app.orderGoodsId).then((result) => {
        app.goods = result.data.goods;
        app.isLoading = false;
      });
    },
    // 切换类型
    onSwitchService(value) {
      this.formData.type = value;
    },
    // 选择图片
    chooseImage() {
      const app = this;
      const oldImageList = app.imageList;
      common_vendor.index.chooseImage({
        count: maxImageLength - oldImageList.length,
        sizeType: ["original", "compressed"],
        // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"],
        // 可以指定来源是相册还是相机，默认二者都有
        success({ tempFiles }) {
          app.imageList = oldImageList.concat(tempFiles);
        }
      });
    },
    // 删除图片
    deleteImage(imageIndex) {
      this.imageList.splice(imageIndex, 1);
    },
    // 表单提交
    handleSubmit() {
      const app = this;
      const { imageList } = app;
      if (app.disabled === true)
        return false;
      if (!app.formData.content.trim().length) {
        app.$toast("请填写申请原因");
        return false;
      }
      app.disabled = true;
      if (imageList.length > 0) {
        app.uploadFile().then(() => app.onSubmit()).catch((err) => {
          app.disabled = false;
          if (err.statusCode !== 0) {
            app.$toast(err.errMsg);
          }
          common_vendor.index.__f__("log", "at pages/refund/apply.vue:189", "err", err);
        });
      } else {
        app.onSubmit();
      }
    },
    // 提交到后端
    onSubmit() {
      const app = this;
      api_refund.apply(app.orderGoodsId, app.formData).then((result) => {
        app.$toast(result.message);
        setTimeout(() => {
          app.disabled = false;
          common_vendor.index.navigateBack();
        }, 1500);
      }).catch((err) => app.disabled = false);
    },
    // 上传图片
    uploadFile() {
      const app = this;
      const { imageList } = app;
      return new Promise((resolve, reject) => {
        if (imageList.length > 0) {
          api_upload.image(imageList).then((fileIds) => {
            app.formData.images = fileIds;
            resolve(fileIds);
          }).catch(reject);
        } else {
          resolve();
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    b: $data.goods.goods_image,
    c: common_vendor.t($data.goods.goods_name),
    d: common_vendor.f($data.goods.goods_props, (props, idx, i0) => {
      return {
        a: common_vendor.t(props.value.name),
        b: idx
      };
    }),
    e: common_vendor.t($data.goods.total_num),
    f: common_vendor.f($data.RefundTypeEnum.data, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: index,
        c: $data.formData.type == item.value ? 1 : "",
        d: common_vendor.o(($event) => $options.onSwitchService(item.value), index)
      };
    }),
    g: $data.formData.content,
    h: common_vendor.o(($event) => $data.formData.content = $event.detail.value),
    i: $data.formData.type == $data.RefundTypeEnum.RETURN.value && $data.type != 2
  }, $data.formData.type == $data.RefundTypeEnum.RETURN.value && $data.type != 2 ? {
    j: common_vendor.t($data.goods.total_pay_price)
  } : {}, {
    k: $data.formData.type == $data.RefundTypeEnum.RETURN.value && $data.type == 2
  }, $data.formData.type == $data.RefundTypeEnum.RETURN.value && $data.type == 2 ? {
    l: common_vendor.t(Number($data.goods.total_pay_price))
  } : {}, {
    m: common_vendor.f($data.imageList, (image, imageIndex, i0) => {
      return {
        a: common_vendor.o(($event) => $options.deleteImage(imageIndex), imageIndex),
        b: image.path,
        c: imageIndex
      };
    }),
    n: $data.imageList.length < $data.maxImageLength
  }, $data.imageList.length < $data.maxImageLength ? {
    o: common_vendor.o(($event) => $options.chooseImage())
  } : {}, {
    p: $data.disabled ? 1 : "",
    q: common_vendor.o(($event) => $options.handleSubmit()),
    r: common_vendor.s(_ctx.appThemeStyle)
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7116fa66"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/refund/apply.js.map
