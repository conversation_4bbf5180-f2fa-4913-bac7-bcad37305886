"use strict";
const common_vendor = require("../../common/vendor.js");
const api_dealer_index = require("../../api/dealer/index.js");
const common_assets = require("../../common/assets.js");
const AvatarImage = () => "../../components/avatar-image/index.js";
const _sfc_main = {
  components: {
    AvatarImage
  },
  data() {
    return {
      points: 0,
      apply: {},
      // 正在加载
      isLoading: true,
      // 当前用户信息
      user: void 0,
      // 当前是否为分销商
      isDealer: false,
      // 当前分销商信息
      dealer: void 0,
      // 推荐人昵称
      refereeName: void 0,
      // 分销设置
      setting: {
        background: void 0,
        words: void 0
      }
    };
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getCenter();
  },
  methods: {
    // 获取分销商中心数据
    getCenter() {
      const app = this;
      app.isLoading = true;
      api_dealer_index.center().then((result) => {
        const data = result.data;
        app.isDealer = data.isDealer;
        app.user = data.user;
        app.dealer = data.dealer;
        app.refereeName = data.refereeName;
        app.setting = data.setting;
        app.points = data.points;
        app.apply = data.apply;
        app.setPageTitle();
        app.isLoading = false;
      });
    },
    // 设置当前页面标题
    setPageTitle() {
      common_vendor.index.setNavigationBarTitle({
        title: this.setting.words.index.title.value
      });
    }
  }
};
if (!Array) {
  const _component_avatar_image = common_vendor.resolveComponent("avatar-image");
  _component_avatar_image();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    b: $data.isDealer
  }, $data.isDealer ? {
    c: $data.setting.background,
    d: common_vendor.p({
      url: $data.user.avatar_url,
      width: 150,
      borderWidth: 4,
      borderColor: `#fff`
    }),
    e: common_vendor.t($data.user.nick_name),
    f: common_vendor.t($data.setting.words.index.words.referee.value),
    g: common_vendor.t($data.refereeName),
    h: common_vendor.t($data.setting.words.index.words.money.value),
    i: common_vendor.t($data.dealer.money),
    j: common_vendor.t($data.setting.words.index.words.freeze_money.value),
    k: common_vendor.t($data.dealer.freeze_money),
    l: common_vendor.t($data.setting.words.index.words.withdraw.value),
    m: common_vendor.o(($event) => _ctx.$navTo("pages/dealer/withdraw/apply")),
    n: common_vendor.t($data.setting.words.index.words.total_money.value),
    o: common_vendor.t($data.dealer.total_money),
    p: common_vendor.t($data.setting.words.withdraw_list.title.value),
    q: common_vendor.o(($event) => _ctx.$navTo("pages/dealer/withdraw/list")),
    r: common_vendor.t($data.setting.words.order.title.value),
    s: common_vendor.o(($event) => _ctx.$navTo("pages/dealer/order")),
    t: common_vendor.t($data.setting.words.team.title.value),
    v: common_vendor.o(($event) => _ctx.$navTo("pages/dealer/team")),
    w: common_vendor.t($data.setting.words.poster.title.value),
    x: common_vendor.o(($event) => _ctx.$navTo("pages/dealer/poster")),
    y: common_vendor.o(($event) => _ctx.$navTo("pages/dealer/orderVip"))
  } : {}, {
    z: !$data.isDealer
  }, !$data.isDealer ? common_vendor.e({
    A: common_assets._imports_0$4,
    B: common_vendor.t($data.setting.words.index.words.not_dealer.value),
    C: $data.user.is_vip == 1
  }, $data.user.is_vip == 1 ? {
    D: common_vendor.t($data.setting.words.index.words.apply_now.value),
    E: common_vendor.o(($event) => _ctx.$navTo("pages/dealer/apply"))
  } : {}, {
    F: $data.apply && $data.apply.apply_status == 30
  }, $data.apply && $data.apply.apply_status == 30 ? {
    G: common_vendor.t($data.apply.reject_reason)
  } : {}) : {}) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f7c6d5d7"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/dealer/index.js.map
