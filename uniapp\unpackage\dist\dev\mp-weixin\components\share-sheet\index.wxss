/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.sharesheet.data-v-5dc7a25c {
  background-color: #f8f8f8;
  font-size: 28rpx;
}
.sharesheet__mask.data-v-5dc7a25c {
  position: fixed;
  top: 0;
  left: var(--window-left);
  right: var(--window-right);
  bottom: var(--window-bottom);
  z-index: 12;
  background: rgba(0, 0, 0, 0.7);
  display: none;
}
.sharesheet__container.data-v-5dc7a25c {
  position: fixed;
  left: var(--window-left);
  right: var(--window-right);
  bottom: var(--window-bottom);
  background: #ffffff;
  transform: translate3d(0, 50%, 0);
  transform-origin: center;
  transition: all 0.2s ease;
  z-index: 13;
  opacity: 0;
  visibility: hidden;
  border-top-left-radius: 26rpx;
  border-top-right-radius: 26rpx;
  padding: 50rpx 30rpx 0 30rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 30rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 30rpx);
}
.sharesheet__list.data-v-5dc7a25c {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  margin-bottom: -35rpx;
}
.sharesheet__list .share-item.data-v-5dc7a25c {
  flex: 0 0 25%;
  margin-bottom: 40rpx;
}
.sharesheet__list .share-item .item-name.data-v-5dc7a25c,
.sharesheet__list .share-item .item-image.data-v-5dc7a25c {
  width: 140rpx;
  margin: 0 auto;
}
.sharesheet__list .share-item .item-image.data-v-5dc7a25c {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 86rpx;
  height: 86rpx;
  border-radius: 50%;
  color: #fff;
  font-size: 38rpx;
}
.sharesheet__list .share-item .item-name.data-v-5dc7a25c {
  margin-top: 12rpx;
  text-align: center;
  font-size: 26rpx;
}
.sharesheet__footer.data-v-5dc7a25c {
  background: #fff;
  margin-top: 40rpx;
}
.sharesheet__footer .btn-cancel.data-v-5dc7a25c {
  font-size: 28rpx;
  text-align: center;
}
.show .sharesheet__mask.data-v-5dc7a25c {
  display: block;
}
.show .sharesheet__container.data-v-5dc7a25c {
  opacity: 1;
  transform: translate3d(0, 0, 0);
  visibility: visible;
}