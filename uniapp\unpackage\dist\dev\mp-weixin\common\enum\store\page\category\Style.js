"use strict";
const common_enum_enum = require("../../../enum.js");
const PageCategoryStyleEnum = new common_enum_enum.Enum([
  { key: "ONE_LEVEL_BIG", name: "一级分类[大图]", value: 10 },
  { key: "ONE_LEVEL_SMALL", name: "一级分类[小图]", value: 11 },
  { key: "TWO_LEVEL", name: "二级分类", value: 20 },
  { key: "COMMODITY", name: "一级分类+商品", value: 30 }
]);
exports.PageCategoryStyleEnum = PageCategoryStyleEnum;
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/common/enum/store/page/category/Style.js.map
