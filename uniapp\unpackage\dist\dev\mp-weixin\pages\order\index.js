"use strict";
const common_vendor = require("../../common/vendor.js");
const common_enum_order_DeliveryStatus = require("../../common/enum/order/DeliveryStatus.js");
const common_enum_order_DeliveryType = require("../../common/enum/order/DeliveryType.js");
require("../../common/enum/order/OrderSource.js");
const common_enum_order_OrderStatus = require("../../common/enum/order/OrderStatus.js");
const common_enum_order_PayStatus = require("../../common/enum/order/PayStatus.js");
const common_enum_order_ReceiptStatus = require("../../common/enum/order/ReceiptStatus.js");
require("../../common/enum/order/OrderType.js");
const common_enum_Client = require("../../common/enum/Client.js");
const common_enum_payment_Method = require("../../common/enum/payment/Method.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const core_app = require("../../core/app.js");
const api_order = require("../../api/order.js");
const pageSize = 15;
const tabs = [{
  name: `全部`,
  value: "all"
}, {
  name: `待支付`,
  value: "payment"
}, {
  name: `待发货`,
  value: "delivery"
}, {
  name: `待收货`,
  value: "received"
}, {
  name: `待评价`,
  value: "comment"
}];
let listener = false;
const _sfc_main = {
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin],
  data() {
    return {
      // 枚举类
      DeliveryStatusEnum: common_enum_order_DeliveryStatus.DeliveryStatusEnum,
      DeliveryTypeEnum: common_enum_order_DeliveryType.DeliveryTypeEnum,
      OrderStatusEnum: common_enum_order_OrderStatus.OrderStatusEnum,
      PayStatusEnum: common_enum_order_PayStatus.PayStatusEnum,
      ReceiptStatusEnum: common_enum_order_ReceiptStatus.ReceiptStatusEnum,
      PayMethodEnum: common_enum_payment_Method.PayMethodEnum,
      // 当前页面参数
      options: { dataType: "all" },
      // tab栏数据
      tabs,
      // 当前标签索引
      curTab: 0,
      // 订单列表数据
      list: core_app.getEmptyPaginateObj(),
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: { size: pageSize },
        // 数量要大于4条才显示无更多数据
        noMoreSize: 4,
        // 空布局
        empty: { tip: "亲，暂无订单记录" }
      },
      // 控制onShow事件是否刷新订单列表
      canReset: false,
      // 核销二维码弹窗
      showQRCodePopup: false,
      // 核销二维码图片url (通过后端获取)
      qrcodeImage: ""
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initCurTab(options);
    common_vendor.index.$on("syncRefresh", (canReset) => {
      this.canReset = canReset;
    });
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    common_vendor.wx$1.offAppShow(listener);
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.canReset && this.onRefreshList();
    this.canReset = false;
  },
  /**
   * 生命周期函数--监听页面的卸载
   */
  onUnload() {
    common_vendor.index.$off("syncRefresh");
  },
  methods: {
    // 初始化当前选中的标签
    initCurTab(options) {
      const app = this;
      if (options.dataType) {
        const index = app.tabs.findIndex((item) => item.value == options.dataType);
        app.curTab = index > -1 ? index : 0;
      }
    },
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getOrderList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 获取订单列表
    getOrderList(pageNo = 1) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_order.list({ dataType: app.getTabValue(), page: pageNo }, { load: false }).then((result) => {
          const newList = app.initList(result.data.list);
          app.list.data = core_app.getMoreListData(newList, app.list, pageNo);
          resolve(newList);
        });
      });
    },
    // 初始化订单列表数据
    initList(newList) {
      newList.data.forEach((item) => {
        item.total_num = 0;
        item.goods.forEach((goods) => item.total_num += goods.total_num);
      });
      return newList;
    },
    // 获取当前标签项的值
    getTabValue() {
      return this.tabs[this.curTab].value;
    },
    // 切换标签项
    onChangeTab(index) {
      const app = this;
      app.curTab = index;
      app.onRefreshList();
    },
    // 刷新订单列表
    onRefreshList() {
      this.list = core_app.getEmptyPaginateObj();
      setTimeout(() => this.mescroll.resetUpScroll(), 120);
    },
    // 取消订单
    onCancel(orderId) {
      const app = this;
      common_vendor.index.showModal({
        title: "友情提示",
        content: "确认要取消该订单吗？",
        success(o) {
          if (o.confirm) {
            api_order.cancel(orderId).then((result) => {
              app.$toast(result.message);
              app.onRefreshList();
            });
          }
        }
      });
    },
    // 确认收货
    async onReceipt(orderIndex) {
      const app = this;
      const orderItem = app.list.data[orderIndex];
      if (app.platform === common_enum_Client.ClientEnum.MP_WEIXIN.value && orderItem.sync_weixin_shipping && orderItem.pay_method === common_enum_payment_Method.PayMethodEnum.WECHAT.value && orderItem.platform === common_enum_Client.ClientEnum.MP_WEIXIN.value) {
        this.listenerBusinessView(orderItem.order_id);
        app.openBusinessView(orderItem);
      } else {
        app.receiptModal(orderItem.order_id);
      }
    },
    // 确认收货弹窗 (微信小程序提供的用于同步发货信息管理)
    openBusinessView(orderItem) {
      return new Promise((resolve, reject) => {
        if (!common_vendor.wx$1.openBusinessView) {
          common_vendor.index.__f__("log", "at pages/order/index.vue:358", "不支持 wx.openBusinessView");
          resolve(false);
        }
        common_vendor.wx$1.openBusinessView({
          businessType: "weappOrderConfirm",
          extraData: { transaction_id: orderItem.trade.trade_no },
          success() {
            common_vendor.index.__f__("log", "at pages/order/index.vue:367", "拉起确认收货组件 success");
            resolve(true);
          },
          fail(err) {
            common_vendor.index.__f__("error", "at pages/order/index.vue:372", "拉起确认收货组件 fail", err);
            resolve(false);
          }
        });
      });
    },
    // 微信小程序确认收货组件 - 回调监听
    listenerBusinessView(orderId) {
      if (listener !== false) {
        common_vendor.wx$1.offAppShow(listener);
      }
      listener = common_vendor.wx$1.onAppShow(({ referrerInfo }) => {
        common_vendor.index.__f__("log", "at pages/order/index.vue:387", "wx.onAppShow", orderId, referrerInfo.extraData);
        if (referrerInfo.extraData && referrerInfo.extraData.status && referrerInfo.extraData.status === "success") {
          common_vendor.index.__f__("log", "at pages/order/index.vue:391", "success receiptEvent", orderId);
          this.receiptEvent(orderId);
        }
      });
    },
    // 确认收货弹窗 (系统默认)
    receiptModal(orderId) {
      const app = this;
      common_vendor.index.showModal({
        title: "友情提示",
        content: "确认收到商品了吗？",
        success(o) {
          o.confirm && app.receiptEvent(orderId);
        }
      });
    },
    // 确认收货事件
    receiptEvent(orderId) {
      const app = this;
      api_order.receipt(orderId).then((result) => {
        app.$success(result.message);
        app.onRefreshList();
      });
    },
    // 获取核销二维码
    onExtractQRCode(orderId) {
      const app = this;
      api_order.extractQrcode(orderId, { channel: app.platform }).then((result) => {
        app.qrcodeImage = result.data.qrcode;
        app.showQRCodePopup = true;
      });
    },
    // 点击去支付
    onPay(orderId) {
      this.$navTo("pages/checkout/cashier/index", { orderId });
    },
    // 跳转到订单详情页
    handleTargetDetail(orderId) {
      this.$navTo("pages/order/detail", { orderId });
    },
    // 跳转到订单评价页
    handleTargetComment(orderId) {
      this.$navTo("pages/order/comment/index", { orderId });
    }
  }
};
if (!Array) {
  const _easycom_u_tabs2 = common_vendor.resolveComponent("u-tabs");
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  (_easycom_u_tabs2 + _easycom_mescroll_body2 + _easycom_u_popup2)();
}
const _easycom_u_tabs = () => "../../uni_modules/vk-uview-ui/components/u-tabs/u-tabs.js";
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
const _easycom_u_popup = () => "../../uni_modules/vk-uview-ui/components/u-popup/u-popup.js";
if (!Math) {
  (_easycom_u_tabs + _easycom_mescroll_body + _easycom_u_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o($options.onChangeTab),
    b: common_vendor.o(($event) => $data.curTab = $event),
    c: common_vendor.p({
      list: $data.tabs,
      ["is-scroll"]: false,
      ["active-color"]: _ctx.appTheme.mainBg,
      duration: 0.2,
      modelValue: $data.curTab
    }),
    d: common_vendor.f($data.list.data, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.create_time),
        b: common_vendor.t(item.state_text),
        c: common_vendor.f(item.goods, (goods, idx, i1) => {
          return common_vendor.e({
            a: goods.goods_image,
            b: common_vendor.t(goods.goods_name),
            c: common_vendor.f(goods.goods_props, (props, idx2, i2) => {
              return {
                a: common_vendor.t(props.value.name),
                b: idx2
              };
            })
          }, item.type == 1 ? {} : {}, item.type == 2 ? {} : {}, item.type == 0 ? {
            d: common_vendor.t(goods.is_user_grade ? goods.grade_goods_price : goods.goods_price)
          } : {}, item.type == 1 ? {} : {}, item.type == 2 ? {
            e: common_vendor.t(Number(goods.goods_price))
          } : {}, {
            f: common_vendor.t(goods.total_num),
            g: idx
          });
        }),
        d: item.type == 1,
        e: item.type == 2,
        f: item.type == 0,
        g: item.type == 1,
        h: item.type == 2,
        i: common_vendor.o(($event) => $options.handleTargetDetail(item.order_id), index),
        j: item.type == 2
      }, item.type == 2 ? {
        k: common_vendor.t(item.total_num),
        l: common_vendor.t(Number(item.points_money))
      } : {
        m: common_vendor.t(item.total_num),
        n: common_vendor.t(item.pay_price)
      }, {
        o: item.order_status != $data.OrderStatusEnum.CANCELLED.value
      }, item.order_status != $data.OrderStatusEnum.CANCELLED.value ? common_vendor.e({
        p: item.pay_status == $data.PayStatusEnum.PENDING.value
      }, item.pay_status == $data.PayStatusEnum.PENDING.value ? {
        q: common_vendor.o(($event) => $options.onCancel(item.order_id), index)
      } : {}, {
        r: item.order_status != $data.OrderStatusEnum.APPLY_CANCEL.value
      }, item.order_status != $data.OrderStatusEnum.APPLY_CANCEL.value ? common_vendor.e({
        s: item.type != 2 && item.pay_status == $data.PayStatusEnum.SUCCESS.value && item.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value
      }, item.type != 2 && item.pay_status == $data.PayStatusEnum.SUCCESS.value && item.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value ? {
        t: common_vendor.o(($event) => $options.onCancel(item.order_id), index)
      } : {}, {
        v: item.pay_status == $data.PayStatusEnum.SUCCESS.value && item.delivery_type == $data.DeliveryTypeEnum.EXTRACT.value && item.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value
      }, item.pay_status == $data.PayStatusEnum.SUCCESS.value && item.delivery_type == $data.DeliveryTypeEnum.EXTRACT.value && item.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value ? {
        w: common_vendor.o(($event) => $options.onExtractQRCode(item.order_id), index)
      } : {}) : {}, {
        x: item.pay_status == $data.PayStatusEnum.PENDING.value && item.pay_method != $data.PayMethodEnum.OFFLINE.value
      }, item.pay_status == $data.PayStatusEnum.PENDING.value && item.pay_method != $data.PayMethodEnum.OFFLINE.value ? {
        y: common_vendor.o(($event) => $options.onPay(item.order_id), index)
      } : {}, {
        z: item.delivery_status == $data.DeliveryStatusEnum.DELIVERED.value && item.receipt_status == $data.ReceiptStatusEnum.NOT_RECEIVED.value
      }, item.delivery_status == $data.DeliveryStatusEnum.DELIVERED.value && item.receipt_status == $data.ReceiptStatusEnum.NOT_RECEIVED.value ? {
        A: common_vendor.o(($event) => $options.onReceipt(index), index)
      } : {}, {
        B: item.order_status == $data.OrderStatusEnum.COMPLETED.value && item.is_comment == 0
      }, item.order_status == $data.OrderStatusEnum.COMPLETED.value && item.is_comment == 0 ? {
        C: common_vendor.o(($event) => $options.handleTargetComment(item.order_id), index)
      } : {}) : {}, {
        D: index
      });
    }),
    e: common_vendor.sr("mescrollRef", "17a44f9d-0"),
    f: common_vendor.o(_ctx.mescrollInit),
    g: common_vendor.o(_ctx.downCallback),
    h: common_vendor.o($options.upCallback),
    i: common_vendor.p({
      sticky: true,
      down: {
        native: true
      },
      up: $data.upOption
    }),
    j: $data.qrcodeImage
  }, $data.qrcodeImage ? {
    k: $data.qrcodeImage
  } : {}, {
    l: common_vendor.o(($event) => $data.showQRCodePopup = $event),
    m: common_vendor.p({
      mode: "center",
      ["border-radius"]: "26",
      closeable: true,
      modelValue: $data.showQRCodePopup
    }),
    n: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-17a44f9d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/index.js.map
