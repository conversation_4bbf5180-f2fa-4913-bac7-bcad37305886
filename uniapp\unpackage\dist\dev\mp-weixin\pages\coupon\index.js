"use strict";
const api_coupon = require("../../api/coupon.js");
const api_myCoupon = require("../../api/myCoupon.js");
require("../../common/enum/coupon/ApplyRange.js");
require("../../common/enum/coupon/ExpireType.js");
const common_enum_coupon_CouponType = require("../../common/enum/coupon/CouponType.js");
const common_vendor = require("../../common/vendor.js");
const Empty = () => "../../components/empty/index.js";
const _sfc_main = {
  components: {
    Empty
  },
  data() {
    return {
      // 枚举类
      CouponTypeEnum: common_enum_coupon_CouponType.CouponTypeEnum,
      // 优惠券列表
      list: [],
      // 正在加载中
      isLoading: true
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getCouponList();
  },
  methods: {
    /**
     * 获取优惠券列表
     * @param {bool} load 是否显示loading弹窗
     */
    getCouponList(load = true) {
      const app = this;
      app.isLoading = true;
      api_coupon.list({}, { load }).then((result) => app.list = result.data.list).finally(() => app.isLoading = false);
    },
    // 立即领取
    handleReceive(couponId) {
      const app = this;
      api_myCoupon.receive(couponId).then((result) => {
        app.$success(result.message);
        app.getCouponList(false);
      });
    },
    // 展开优惠券描述
    handleDescribe(index) {
      this.list[index].expand = !this.list[index].expand;
    }
  }
};
if (!Array) {
  const _component_empty = common_vendor.resolveComponent("empty");
  _component_empty();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.list.length
  }, $data.list.length ? {
    b: common_vendor.f($data.list, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($data.CouponTypeEnum[item.coupon_type].name),
        b: item.coupon_type == $data.CouponTypeEnum.FULL_DISCOUNT.value
      }, item.coupon_type == $data.CouponTypeEnum.FULL_DISCOUNT.value ? {
        c: common_vendor.t(item.reduce_price)
      } : {}, {
        d: item.coupon_type == $data.CouponTypeEnum.DISCOUNT.value
      }, item.coupon_type == $data.CouponTypeEnum.DISCOUNT.value ? {
        e: common_vendor.t(item.discount)
      } : {}, {
        f: common_vendor.t(item.min_price),
        g: common_vendor.t(item.name),
        h: item.expire_type == $data.CouponTypeEnum.FULL_DISCOUNT.value
      }, item.expire_type == $data.CouponTypeEnum.FULL_DISCOUNT.value ? {
        i: common_vendor.t(item.expire_day)
      } : {}, {
        j: item.expire_type == $data.CouponTypeEnum.DISCOUNT.value
      }, item.expire_type == $data.CouponTypeEnum.DISCOUNT.value ? common_vendor.e({
        k: item.start_time === item.end_time
      }, item.start_time === item.end_time ? {
        l: common_vendor.t(item.start_time)
      } : {
        m: common_vendor.t(item.start_time),
        n: common_vendor.t(item.end_time)
      }) : {}, {
        o: item.describe
      }, item.describe ? {
        p: common_vendor.n(item.expand ? "expand" : ""),
        q: common_vendor.o(($event) => $options.handleDescribe(index), index)
      } : {}, {
        r: _ctx.$checkModule("market-coupon") && item.state.value
      }, _ctx.$checkModule("market-coupon") && item.state.value ? {
        s: common_vendor.o(($event) => $options.handleReceive(item.coupon_id), index)
      } : {}, {
        t: !item.state.value
      }, !item.state.value ? {
        v: common_vendor.t(item.state.text)
      } : {}, {
        w: common_vendor.n(!item.state.value ? "disable" : ""),
        x: common_vendor.t(item.describe),
        y: common_vendor.n(item.expand ? "expand" : ""),
        z: index
      });
    })
  } : {}, {
    c: !$data.list.length
  }, !$data.list.length ? {
    d: common_vendor.p({
      isLoading: $data.isLoading
    })
  } : {}, {
    e: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-59672890"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/coupon/index.js.map
