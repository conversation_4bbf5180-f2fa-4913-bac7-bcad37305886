"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_verify = require("../../utils/verify.js");
const api_points_checkout = require("../../api/points/checkout.js");
const api_sharp_checkout = require("../../api/sharp/checkout.js");
const api_bargain_checkout = require("../../api/bargain/checkout.js");
const api_groupon_checkout = require("../../api/groupon/checkout.js");
require("../../common/enum/coupon/ApplyRange.js");
require("../../common/enum/coupon/ExpireType.js");
const common_enum_coupon_CouponType = require("../../common/enum/coupon/CouponType.js");
require("../../common/enum/order/DeliveryStatus.js");
const common_enum_order_DeliveryType = require("../../common/enum/order/DeliveryType.js");
require("../../common/enum/order/OrderSource.js");
require("../../common/enum/order/OrderStatus.js");
require("../../common/enum/order/PayStatus.js");
require("../../common/enum/order/ReceiptStatus.js");
const common_enum_order_OrderType = require("../../common/enum/order/OrderType.js");
const CouponColors = ["red", "blue", "violet", "yellow"];
const getCheckoutApi = (mode) => {
  const apiEnum = {
    buyNow: api_points_checkout.CheckoutApi,
    cart: api_points_checkout.CheckoutApi,
    bargain: api_bargain_checkout.BargainCheckoutApi,
    sharp: api_sharp_checkout.SharpCheckoutApi,
    groupon: api_groupon_checkout.GrouponCheckoutApi
  };
  return apiEnum[mode];
};
const getModeParam = (mode, options) => {
  const param = {};
  if (mode === "buyNow") {
    param.goodsId = options.goodsId;
    param.goodsNum = options.goodsNum;
    param.goodsSkuId = options.goodsSkuId;
  }
  if (mode === "cart") {
    param.cartIds = options.cartIds;
  }
  if (mode === "bargain") {
    param.taskId = options.taskId;
    param.goodsSkuId = options.goodsSkuId;
  }
  if (mode === "sharp") {
    param.activeTimeId = options.activeTimeId;
    param.sharpGoodsId = options.sharpGoodsId;
    param.goodsSkuId = options.goodsSkuId;
    param.goodsNum = options.goodsNum;
  }
  if (mode === "groupon") {
    param.grouponGoodsId = options.grouponGoodsId;
    param.taskId = options.taskId;
    param.goodsSkuId = options.goodsSkuId;
    param.goodsNum = options.goodsNum;
    param.stepPeople = options.stepPeople;
  }
  return param;
};
const _sfc_main = {
  data() {
    return {
      // 枚举类
      OrderTypeEnum: common_enum_order_OrderType.OrderTypeEnum,
      DeliveryTypeEnum: common_enum_order_DeliveryType.DeliveryTypeEnum,
      CouponTypeEnum: common_enum_coupon_CouponType.CouponTypeEnum,
      // 当前页面参数
      options: {},
      // 配送方式
      isShowTab: false,
      DeliveryTypeEnum: common_enum_order_DeliveryType.DeliveryTypeEnum,
      curDelivery: null,
      // 自提信息
      selectedShopId: 0,
      // 选择的门店ID
      linkman: "",
      // 自提联系人
      phone: "",
      // 自提联系电话
      // 优惠券颜色组
      CouponColors,
      // 选择的优惠券
      selectCouponId: 0,
      // 是否使用积分抵扣
      isUsePoints: true,
      // 买家留言
      remark: "",
      // 禁用submit按钮
      disabled: false,
      // 是否显示积分说明
      showPoints: false,
      // 是否显示优惠券弹窗
      showPopup: false,
      // 订单信息 (从后端api中获取)
      order: {
        // 商品列表
        goodsList: [],
        // 优惠券列表
        couponList: [],
        // 是否存在收货地址
        existAddress: false,
        // 默认收货地址
        address: null,
        // 是否存在收货地址
        existAddress: false,
        // 当前用户收货城市是否存在配送规则中
        isIntraRegion: true,
        // 是否存在错误
        hasError: false,
        // 错误信息
        errorMsg: ""
      },
      // 个人信息
      personal: {},
      // 商城设置
      setting: {}
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.options = options;
    common_vendor.index.$on("syncSelectedId", (selectedId) => {
      this.selectedShopId = selectedId;
    });
  },
  /**
   * 生命周期函数--监听页面的卸载
   */
  onUnload() {
    common_vendor.index.$off("syncSelectedId");
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getOrderData();
  },
  methods: {
    // 获取订单数据
    getOrderData() {
      const app = this;
      const {
        options: {
          mode
        }
      } = app;
      const params = app.getRequestParam();
      getCheckoutApi(mode).order(mode, params).then((result) => app.initData(result.data)).catch((err) => err);
    },
    // 初始化数据
    initData({
      order,
      setting,
      personal
    }) {
      const app = this;
      app.order = order;
      app.personal = personal;
      app.setting = setting;
      if (order.hasError) {
        app.showToast(order.errorMsg, 3e3);
      }
      app.curDelivery = order.delivery;
      app.isShowTab = setting.deliveryType.length > 1;
      if (app.linkman === "") {
        app.linkman = order.lastExtract.linkman;
      }
      if (app.phone === "") {
        app.phone = order.lastExtract.phone;
      }
      app.isUsePoints = order.isUsePoints;
      app.selectCouponId = order.couponId;
    },
    // 获取api请求的参数
    getRequestParam() {
      const app = this;
      const {
        options
      } = app;
      const modeParam = getModeParam(options.mode, options);
      const orderParam = {
        delivery: app.curDelivery || 0,
        shopId: app.selectedShopId || 0,
        couponId: app.selectCouponId || 0,
        isUsePoints: app.isUsePoints ? 1 : 0
      };
      return {
        ...orderParam,
        ...modeParam
      };
    },
    // 切换配送方式
    handleSwichDelivery(key) {
      this.curDelivery = key;
      this.getOrderData();
    },
    // 显示积分说明
    handleShowPoints() {
      this.showPoints = true;
    },
    // 显示优惠券弹窗
    handleShowPopup() {
      this.showPopup = true;
    },
    // 展开优惠券描述
    handleDescribe(index) {
      const {
        couponList
      } = this.order;
      couponList[index].expand = !couponList[index].expand;
    },
    // 选择优惠券
    handleSelectCoupon(index) {
      const app = this;
      const {
        couponList
      } = app.order;
      const couponItem = couponList[index];
      if (!couponItem.is_apply) {
        app.showToast(couponItem.not_apply_info);
        return;
      }
      app.selectCouponId = app.selectCouponId == couponItem.user_coupon_id ? 0 : couponItem.user_coupon_id;
      app.getOrderData();
      app.showPopup = false;
    },
    // 不使用优惠券
    handleNotUseCoupon() {
      this.selectCouponId = 0;
      this.getOrderData();
      this.showPopup = false;
    },
    // 快递配送：选择收货地址
    onSelectAddress() {
      this.$navTo("pages/address/index", {
        from: "checkout"
      });
    },
    // 上门自提：选择自提点
    onSelectExtractPoint() {
      this.$navTo("pages/shop/extract", {
        selectedId: this.selectedShopId
      });
    },
    // 跳转到商品详情页
    onTargetGoods(goodsId) {
      this.$navTo("pagesNew/points/detail", {
        goodsId
      });
    },
    // 订单提交
    onSubmitOrder() {
      const app = this;
      if (app.disabled) {
        return false;
      }
      if (!app.onVerifyFrom()) {
        return false;
      }
      if (Number(app.order.orderTotalPrice) > Number(app.personal.points)) {
        app.showToast("积分不足");
        return false;
      }
      app.disabled = true;
      getCheckoutApi(app.options.mode).submit(app.options.mode, app.getFormData()).then((result) => {
        const orderId = result.data.orderId;
        app.showToast("兑换成功");
        setTimeout(() => {
          this.$navTo("pages/order/index", {
            orderId
          }, "redirectTo");
        }, 1e3);
      }).catch((res) => app.showToast(res.errMsg, 3e3)).finally(() => setTimeout(() => app.disabled = false, 800));
    },
    // 跳转到我的订单(等待1秒)
    navToMyOrder() {
      setTimeout(() => {
        this.$navTo("pages/order/index", {}, "redirectTo");
      }, 1e3);
    },
    // 表单提交的数据
    getFormData() {
      const app = this;
      const {
        options
      } = app;
      const form = {
        delivery: app.curDelivery,
        couponId: app.selectCouponId || 0,
        shopId: app.selectedShopId || 0,
        linkman: app.linkman,
        phone: app.phone,
        isUsePoints: app.isUsePoints ? 1 : 0,
        remark: app.remark || "",
        payPrice: app.order.orderPayPrice
      };
      const modeParam = getModeParam(options.mode, options);
      return {
        ...form,
        ...modeParam
      };
    },
    // 表单验证
    onVerifyFrom() {
      const app = this;
      if (app.hasError) {
        app.showToast(app.errorMsg, 3e3);
        return false;
      }
      if (app.curDelivery == common_enum_order_DeliveryType.DeliveryTypeEnum.EXTRACT.value) {
        app.linkman = app.linkman.trim();
        app.phone = app.phone.trim();
        if (app.selectedShopId <= 0) {
          app.showToast("请选择自提的门店");
          return false;
        }
        if (utils_verify.isEmpty(app.linkman)) {
          app.showToast("请填写自提联系人");
          return false;
        }
        if (utils_verify.isEmpty(app.phone)) {
          app.showToast("请填写自提联系电话");
          return false;
        }
        if (!utils_verify.isPhone(app.phone)) {
          app.showToast("请输入正确的联系电话");
          return false;
        }
      }
      return true;
    },
    // 显示toast信息
    showToast(title, duration = 2e3) {
      this.$refs.uToast.show({
        title,
        duration
      });
    }
  }
};
if (!Array) {
  const _easycom_u_toast2 = common_vendor.resolveComponent("u-toast");
  _easycom_u_toast2();
}
const _easycom_u_toast = () => "../../uni_modules/vk-uview-ui/components/u-toast/u-toast.js";
if (!Math) {
  _easycom_u_toast();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.order.goodsList.length
  }, $data.order.goodsList.length ? common_vendor.e({
    b: $data.order.orderType == $data.OrderTypeEnum.PHYSICAL.value
  }, $data.order.orderType == $data.OrderTypeEnum.PHYSICAL.value ? common_vendor.e({
    c: $data.isShowTab
  }, $data.isShowTab ? {
    d: $data.curDelivery == $data.DeliveryTypeEnum.EXPRESS.value ? 1 : "",
    e: common_vendor.o(($event) => $options.handleSwichDelivery($data.DeliveryTypeEnum.EXPRESS.value)),
    f: $data.curDelivery == $data.DeliveryTypeEnum.EXTRACT.value ? 1 : "",
    g: common_vendor.o(($event) => $options.handleSwichDelivery($data.DeliveryTypeEnum.EXTRACT.value))
  } : {}, {
    h: $data.curDelivery == $data.DeliveryTypeEnum.EXPRESS.value
  }, $data.curDelivery == $data.DeliveryTypeEnum.EXPRESS.value ? common_vendor.e({
    i: $data.order.address
  }, $data.order.address ? {
    j: common_vendor.t($data.order.address.name),
    k: common_vendor.t($data.order.address.phone),
    l: common_vendor.f($data.order.address.region, (region, idx, i0) => {
      return {
        a: common_vendor.t(region),
        b: idx
      };
    }),
    m: common_vendor.t($data.order.address.detail)
  } : {}, {
    n: common_vendor.o((...args) => $options.onSelectAddress && $options.onSelectAddress(...args))
  }) : {}, {
    o: $data.curDelivery == $data.DeliveryTypeEnum.EXTRACT.value
  }, $data.curDelivery == $data.DeliveryTypeEnum.EXTRACT.value ? common_vendor.e({
    p: $data.order.extractShop.shop_id
  }, $data.order.extractShop.shop_id ? {
    q: common_vendor.t($data.order.extractShop.shop_name),
    r: common_vendor.t($data.order.extractShop.region.province),
    s: common_vendor.t($data.order.extractShop.region.city),
    t: common_vendor.t($data.order.extractShop.region.region),
    v: common_vendor.t($data.order.extractShop.address)
  } : {}, {
    w: common_vendor.o(($event) => $options.onSelectExtractPoint()),
    x: $data.linkman,
    y: common_vendor.o(($event) => $data.linkman = $event.detail.value),
    z: $data.phone,
    A: common_vendor.o(($event) => $data.phone = $event.detail.value)
  }) : {}) : {}, {
    B: common_vendor.f($data.order.goodsList, (item, index, i0) => {
      return {
        a: item.goods_image,
        b: common_vendor.t(item.goods_name),
        c: common_vendor.f(item.skuInfo.goods_props, (props, idx, i1) => {
          return {
            a: common_vendor.t(props.group.name),
            b: common_vendor.t(props.value.name),
            c: idx
          };
        }),
        d: common_vendor.t(item.total_num),
        e: common_vendor.o(($event) => $options.onTargetGoods(item.goods_id), index),
        f: index
      };
    }),
    C: common_vendor.t($data.order.orderTotalNum),
    D: common_vendor.t(Number($data.order.orderTotalPrice)),
    E: $data.remark,
    F: common_vendor.o(($event) => $data.remark = $event.detail.value),
    G: common_vendor.t(Number($data.order.orderTotalPrice)),
    H: $data.disabled ? 1 : "",
    I: common_vendor.o(($event) => $options.onSubmitOrder())
  }) : {}, {
    J: common_vendor.sr("uToast", "bd01f1a2-0"),
    K: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-bd01f1a2"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesNew/checkout-points/index.js.map
