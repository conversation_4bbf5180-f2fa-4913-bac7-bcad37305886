"use strict";
const components_page_diyComponents_mixin = require("../mixin.js");
const api_myCoupon = require("../../../../api/myCoupon.js");
const utils_util = require("../../../../utils/util.js");
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object,
    dataList: Array
  },
  data() {
    return {
      // 优惠券列表
      couponList: [],
      // 防止重复提交
      disable: false
    };
  },
  watch: {
    // 这里监听dataList并写入到data中, 因为领取事件不能直接修改props中的属性
    dataList: {
      handler(data) {
        this.couponList = utils_util.cloneObj(data);
      },
      immediate: true,
      deep: true
    }
  },
  mixins: [components_page_diyComponents_mixin.mixin],
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {
    // 立即领取事件
    handleReceive(index, item) {
      const app = this;
      if (app.disable || !item.state.value) {
        return;
      }
      app.disable = true;
      api_myCoupon.receive(item.coupon_id, {}, { load: false }).then((result) => {
        app.$success(result.message);
        app.setReceived(index, item);
      }).finally(() => app.disable = false);
    },
    // 将优惠券设置为已领取
    setReceived(index, item) {
      const app = this;
      app.couponList[index] = {
        ...item,
        state: { value: 0, text: "已领取" }
      };
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.couponList.length
  }, $data.couponList.length ? {
    b: common_vendor.f($data.couponList, (dataItem, index, i0) => {
      return common_vendor.e({
        a: dataItem.coupon_type == 10
      }, dataItem.coupon_type == 10 ? {
        b: common_vendor.t(dataItem.reduce_price)
      } : {}, {
        c: dataItem.coupon_type == 20
      }, dataItem.coupon_type == 20 ? {
        d: common_vendor.t(dataItem.discount)
      } : {}, {
        e: common_vendor.t(dataItem.min_price),
        f: dataItem.state.value
      }, dataItem.state.value ? {} : {
        g: common_vendor.t(dataItem.state.text)
      }, {
        h: common_vendor.o(($event) => $options.handleReceive(index, dataItem), index),
        i: !dataItem.state.value ? 1 : "",
        j: index
      });
    }),
    c: $props.itemStyle.background,
    d: $props.itemStyle.couponBgColor,
    e: $props.itemStyle.couponTextColor,
    f: $props.itemStyle.receiveBgColor,
    g: $props.itemStyle.receiveTextColor,
    h: `${$props.itemStyle.marginRight * 2}rpx`,
    i: `${$props.itemStyle.paddingTop * 2}rpx 0`,
    j: $props.itemStyle.background
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1a1beb79"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/components/page/diyComponents/coupon/index.js.map
