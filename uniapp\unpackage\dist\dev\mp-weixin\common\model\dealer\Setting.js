"use strict";
const api_dealer_setting = require("../../../api/dealer/setting.js");
const utils_storage = require("../../../utils/storage.js");
const CACHE_KEY = "Dealer-Setting";
const setStorage = (data2) => {
  const expireTime = 30 * 60;
  utils_storage.storage.set(CACHE_KEY, data2, expireTime);
};
const getStorage = () => {
  return utils_storage.storage.get(CACHE_KEY);
};
const getApiData = () => {
  return new Promise((resolve, reject) => {
    api_dealer_setting.data().then((result) => {
      resolve(result.data.setting);
    });
  });
};
const data = (isCache = false) => {
  return new Promise((resolve, reject) => {
    const cacheData = getStorage();
    if (isCache && cacheData) {
      resolve(cacheData);
    } else {
      getApiData().then((data2) => {
        setStorage(data2);
        resolve(data2);
      });
    }
  });
};
const item = (key, isCache = false) => {
  return new Promise((resolve, reject) => {
    data(isCache).then((setting) => {
      resolve(setting[key]);
    });
  });
};
const SettingModel = {
  data,
  item
};
exports.SettingModel = SettingModel;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/common/model/dealer/Setting.js.map
