
page {
    height: 100%;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-1c08b8df {
  background: url("https://si.geilicdn.com/img-310900000167962321710a026860-unadjust_750_686.png") top no-repeat, linear-gradient(90deg, #fea044, #f9565d 63%, #e63378);
  background-size: 100% auto;
  min-height: 100%;
}

/* 头部区域 */
.header.data-v-1c08b8df {
  padding: 30rpx 30rpx;
}
.header .item-touch.data-v-1c08b8df {
  color: #fff;
  font-size: 24rpx;
  padding: 7rpx 20rpx;
  background: rgba(0, 0, 0, 0.17);
  border-radius: 22rpx;
}

/* 内容区域 */
.content.data-v-1c08b8df {
  position: relative;
  box-sizing: border-box;
  padding: 10rpx 30rpx 60rpx 30rpx;
}

/* 砍价信息 */
.infos-wrap.data-v-1c08b8df {
  background: #fff;
  box-shadow: 0 4rpx 40rpx 0 rgba(151, 151, 151, 0.24);
  padding: 0 30rpx 40rpx;
  border-radius: 16rpx;
}
.infos-top.data-v-1c08b8df {
  position: relative;
  top: -42rpx;
  margin-bottom: -22rpx;
}
.infos-img.data-v-1c08b8df {
  width: 120rpx;
  height: 120rpx;
  padding: 8rpx;
  background: #fff;
  overflow: hidden;
  margin: 0 auto;
  border-radius: 50%;
}
.infos-name.data-v-1c08b8df {
  margin: 8rpx auto 0;
  width: 80%;
  font-size: 26rpx;
  color: #9a9a9a;
  text-align: center;
  line-height: 32rpx;
}
.infos-prompt.data-v-1c08b8df {
  text-align: center;
  font-size: 30rpx;
  color: #222;
  line-height: 48rpx;
  margin-bottom: 30rpx;
}
.infos-item.data-v-1c08b8df {
  margin-top: 40rpx;
  display: flex;
}
.infos-item-img.data-v-1c08b8df {
  flex: none;
  width: 180rpx;
  height: 180rpx;
}
.infos-item-img .image.data-v-1c08b8df {
  width: 100%;
  height: 100%;
}
.infos-item-info.data-v-1c08b8df {
  margin-left: 25rpx;
  flex: auto;
}
.infos-item-name.data-v-1c08b8df {
  font-size: 28rpx;
  color: #404040;
  line-height: 40rpx;
  height: 80rpx;
}
.infos-item-stock .stock-widget.data-v-1c08b8df {
  display: inline-block;
  min-width: 100rpx;
  padding: 0 20rpx;
  background-image: linear-gradient(-90deg, #fe9c3f, #fb6253 99%);
  border-radius: 40rpx;
  height: 40rpx;
  font-size: 24rpx;
  color: #fff;
  line-height: 40rpx;
  margin-top: 6rpx;
}
.infos-item-stock .stock-widget .stock-num.data-v-1c08b8df {
  margin: 0 6rpx;
}
.infos-item-price.data-v-1c08b8df {
  font-size: 0;
  margin-top: 8rpx;
}
.infos-item-price .price1.data-v-1c08b8df {
  font-size: 24rpx;
  line-height: 32rpx;
}
.infos-item-price .price2.data-v-1c08b8df {
  margin-left: 4rpx;
  font-size: 36rpx;
  line-height: 40rpx;
}
.infos-item-price .price3.data-v-1c08b8df {
  margin-left: 10rpx;
  font-size: 24rpx;
  color: #9a9a9a;
  line-height: 32rpx;
  text-decoration: line-through;
}

/* 分割线 */
.connect.data-v-1c08b8df {
  position: relative;
  height: 20rpx;
}
.connect-ring.data-v-1c08b8df {
  position: absolute;
  top: -28rpx;
  height: 76rpx;
  width: 20rpx;
  padding: 8rpx 6rpx;
  box-sizing: border-box;
}
.connect-ring.data-v-1c08b8df:after, .connect-ring.data-v-1c08b8df:before {
  content: "";
  position: absolute;
  z-index: 6;
  left: 0;
  height: 20rpx;
  width: 20rpx;
  border-radius: 20rpx;
}
.connect-ring .line.data-v-1c08b8df {
  z-index: 8;
  display: block;
  height: 100%;
  width: 100%;
  background: #fff;
  border-radius: 20rpx;
}
.connect-ring.bgf-ring--left.data-v-1c08b8df {
  left: 20rpx;
}
.connect-ring.bgf-ring--left.data-v-1c08b8df:before {
  top: 0;
  background: #f4914e;
}
.connect-ring.bgf-ring--left.data-v-1c08b8df:after {
  bottom: 0;
  background: #f4914e;
}
.connect-ring.bgf-ring--right.data-v-1c08b8df {
  right: 20rpx;
}
.connect-ring.bgf-ring--right.data-v-1c08b8df:before {
  top: 0;
  background: #e03e71;
}
.connect-ring.bgf-ring--right.data-v-1c08b8df:after {
  bottom: 0;
  background: #e03e71;
}

/* 砍价进度 */
.bargain-wrap.data-v-1c08b8df {
  position: relative;
  background: #fff;
  padding: 40rpx 30rpx 30rpx;
  box-shadow: 0 4rpx 40rpx 0 rgba(144, 52, 52, 0.1);
  border-radius: 16rpx;
}
.bargain-info.data-v-1c08b8df {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #404040;
  line-height: 40rpx;
}
.bargain-info .focal.data-v-1c08b8df {
  margin: 0 5rpx;
}
.bargain-p.data-v-1c08b8df {
  margin-top: 40rpx;
  min-height: 32rpx;
}
.bargain-people.data-v-1c08b8df {
  font-size: 24rpx;
  color: #9a9a9a;
  text-align: center;
  line-height: 32rpx;
}

/* 进度条 */
.bgn__process.data-v-1c08b8df {
  position: relative;
  padding: 30rpx 0;
}
.bgn__process-bottom.data-v-1c08b8df {
  z-index: 1;
  overflow: hidden;
  background-image: linear-gradient(0deg, #f0f2f7, #e8ebf3);
}
.bgn__process-bottom.data-v-1c08b8df,
.bgn__process-process.data-v-1c08b8df {
  position: relative;
  height: 30rpx;
  border-radius: 30rpx;
}
.bgn__process-process.data-v-1c08b8df {
  background-image: linear-gradient(90deg, #ffc108, #fde586);
  background: #ffc108;
}
.bgn__process-process.process--ani.data-v-1c08b8df:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 32rpx;
  top: 50%;
  left: 0;
  margin-top: -16rpx;
}

/* 操作按钮 */
.btn-container .btn-item.data-v-1c08b8df {
  color: #fff;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 15rpx;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.btn-item__buy.data-v-1c08b8df {
  width: 280rpx;
  margin-right: 30rpx;
  background-image: linear-gradient(90deg, #fa9e1b, #fe5b1b);
  box-shadow: #fe5b1b 0 22rpx 48rpx -22rpx;
}
.btn-item__buy.complete.data-v-1c08b8df {
  width: 360rpx;
  animation: btn_anim-1c08b8df 0.9s linear infinite;
  transform-origin: center;
}
.btn-item__main.data-v-1c08b8df {
  width: 320rpx;
  background-image: linear-gradient(90deg, #fe316c, #fd584e);
  box-shadow: #fd584e 0 22rpx 48rpx -22rpx;
  animation: btn_anim-1c08b8df 0.9s linear infinite;
  transform-origin: center;
}
.btn-item-long.data-v-1c08b8df {
  max-width: 400rpx;
}

/* 按钮动画 */
@keyframes btn_anim-1c08b8df {
0% {
    transform: scale(1);
}
40% {
    transform: scale(1.05);
}
}
/* 好友助力榜 */
.records-container.data-v-1c08b8df {
  margin-top: 44rpx;
}
.records.data-v-1c08b8df {
  position: relative;
  color: #404040;
  box-shadow: 0 4rpx 40rpx 0 rgba(0, 0, 0, 0.08);
}
.records-back.data-v-1c08b8df {
  position: absolute;
  left: -14rpx;
  right: -14rpx;
  top: -14rpx;
  height: 28rpx;
  border-radius: 28rpx;
  z-index: 1;
  background: #cb272d;
}
.records-content.data-v-1c08b8df {
  position: relative;
  z-index: 2;
  background: #fff;
  padding: 40rpx 30rpx;
}
.records-h2.data-v-1c08b8df {
  display: flex;
  justify-content: space-between;
  height: 60rpx;
  align-items: center;
  font-weight: 700;
  font-size: 34rpx;
  line-height: 48rpx;
}
.friend-help.data-v-1c08b8df {
  overflow: hidden;
  padding: 40rpx 0 20rpx;
  transition: max-height 0.6s ease-out;
}
.records-left.data-v-1c08b8df,
.records-item.data-v-1c08b8df {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.records-left .nick-name.data-v-1c08b8df {
  display: inline-block;
  margin-left: 14rpx;
}
.records-left .nick-name.data-v-1c08b8df,
.records-right.data-v-1c08b8df {
  font-size: 28rpx;
  line-height: 40rpx;
}
.records-right.data-v-1c08b8df {
  display: flex;
  justify-content: center;
  align-items: center;
}
.records-right .red.data-v-1c08b8df {
  color: #e53a40;
}
.pops-content.data-v-1c08b8df {
  padding: 30rpx 48rpx;
  font-size: 28rpx;
  line-height: 44rpx;
  text-align: left;
  color: #606266;
  min-height: 320rpx;
  max-height: 640rpx;
  box-sizing: border-box;
}