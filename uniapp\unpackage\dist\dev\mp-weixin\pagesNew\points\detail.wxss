
page {
		background: #fafafa;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-b1ca85eb {
  padding-bottom: calc(constant(safe-area-inset-bottom) + 106rpx + 6rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 106rpx + 6rpx);
}
.goods-info.data-v-b1ca85eb {
  background: #fff;
  padding: 25rpx 30rpx;
}
.info-item__top.data-v-b1ca85eb {
  min-height: 40rpx;
  margin-bottom: 20rpx;
  line-height: 1;
}
.floor-price__samll.data-v-b1ca85eb {
  font-size: 26rpx;
  line-height: 1;
  color: var(--main-bg);
  margin-bottom: -10rpx;
}
.floor-price.data-v-b1ca85eb {
  color: var(--main-bg);
  margin-right: 15rpx;
  font-size: 42rpx;
}
.original-price.data-v-b1ca85eb {
  font-size: 26rpx;
  text-decoration: line-through;
  color: #959595;
  margin-right: 15rpx;
  margin-bottom: -6rpx;
}
.user-grade.data-v-b1ca85eb {
  background: #3c3c3c;
  border-radius: 6rpx;
  padding: 8rpx 14rpx;
  margin-right: 15rpx;
  font-size: 24rpx;
  color: #eee0c3;
}
.goods-sales.data-v-b1ca85eb {
  font-size: 24rpx;
  color: #959595;
}
.info-item__name .goods-name.data-v-b1ca85eb {
  font-size: 30rpx;
}
/* 商品分享 */
.goods-share__line.data-v-b1ca85eb {
  border-left: 1rpx solid #f4f4f4;
  height: 60rpx;
  margin: 0 30rpx;
}
.goods-share .share-btn.data-v-b1ca85eb {
  line-height: normal;
  padding: 0;
  background: none;
  border-radius: 0;
  box-shadow: none;
  font-size: 8pt;
  border: none;
  color: #191919;
}
.goods-share .share-btn.data-v-b1ca85eb::after {
  border: none;
}
.goods-share .share__icon.data-v-b1ca85eb {
  font-size: 40rpx;
  margin-bottom: 5rpx;
}
.info-item_selling-point.data-v-b1ca85eb {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #808080;
}
.goods-choice.data-v-b1ca85eb {
  padding: 26rpx 30rpx;
  font-size: 26rpx;
}
.goods-choice .spec-list.data-v-b1ca85eb {
  display: flex;
  align-items: center;
}
.goods-choice .spec-list .spec-name.data-v-b1ca85eb {
  margin-right: 10rpx;
}
.goods-content .item-title.data-v-b1ca85eb {
  padding: 26rpx 30rpx;
  font-size: 26rpx;
}
.footer-fixed.data-v-b1ca85eb {
  position: fixed;
  bottom: var(--window-bottom);
  left: 0;
  right: 0;
  display: flex;
  z-index: 11;
  box-shadow: 0 -4rpx 40rpx 0 rgba(151, 151, 151, 0.24);
  background: #fff;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.footer-container.data-v-b1ca85eb {
  width: 100%;
  display: flex;
  height: 106rpx;
}
.foo-item-fast.data-v-b1ca85eb {
  box-sizing: border-box;
  min-width: 214rpx;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  margin-right: 12rpx;
}
.foo-item-fast .fast-item.data-v-b1ca85eb {
  position: relative;
  padding: 4rpx 0;
  line-height: 1;
  text-align: center;
  width: 84rpx;
}
.foo-item-fast .fast-item--cart.data-v-b1ca85eb {
  margin-left: 6rpx;
}
.foo-item-fast .fast-item--cart .fast-icon.data-v-b1ca85eb {
  margin-left: -12rpx;
}
.foo-item-fast .fast-item .fast-badge.data-v-b1ca85eb {
  display: inline-block;
  box-sizing: border-box;
  min-width: 16px;
  padding: 0 3px;
  color: #fff;
  font-weight: 500;
  font-size: 12px;
  font-family: -apple-system-font, Helvetica Neue, Arial, sans-serif;
  line-height: 1.2;
  text-align: center;
  background-color: #ee0a24;
  border: 1px solid #fff;
  border-radius: 999px;
}
.foo-item-fast .fast-item .fast-badge--fixed.data-v-b1ca85eb {
  position: absolute;
  top: 0;
  right: 0;
  transform-origin: 100%;
}
.foo-item-fast .fast-item .fast-icon.data-v-b1ca85eb {
  font-size: 44rpx;
  margin-bottom: 8rpx;
}
.foo-item-fast .fast-item .fast-text.data-v-b1ca85eb {
  font-size: 22rpx;
}
.foo-item-btn.data-v-b1ca85eb {
  flex: 1;
}
.foo-item-btn .btn-wrapper.data-v-b1ca85eb {
  height: 100%;
  display: flex;
  align-items: center;
}
.foo-item-btn .btn-item.data-v-b1ca85eb {
  flex: 1;
  font-size: 28rpx;
  height: 72rpx;
  margin-right: 16rpx;
  color: #fff;
  border-radius: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.foo-item-btn .btn-item-main.data-v-b1ca85eb {
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
}
.foo-item-btn .btn-item-deputy.data-v-b1ca85eb {
  background: linear-gradient(to right, var(--vice-bg), var(--vice-bg2));
  color: var(--vice-text);
}