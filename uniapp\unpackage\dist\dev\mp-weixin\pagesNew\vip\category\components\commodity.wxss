/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.address-pop.data-v-184e0e3f {
  padding: 20rpx 30rpx;
}
.address-pop .tips.data-v-184e0e3f {
  padding: 10rpx 0;
  font-size: 24rpx;
  color: #ff0000;
}
.address-pop .title.data-v-184e0e3f {
  text-align: center;
  font-weight: bold;
}
.address-pop .footer.data-v-184e0e3f {
  height: 80rpx;
  line-height: 80rpx;
  width: 300rpx;
  text-align: center;
  background-color: #F2D8B2;
  margin: 0 auto;
  margin-top: 30rpx;
  border-radius: 50rpx;
}
.address-box.data-v-184e0e3f {
  display: flex;
  justify-content: space-between;
  padding: 0rpx 30rpx;
  padding-top: 20rpx;
  font-weight: bold;
}
.container.data-v-184e0e3f {
  padding-left: 173rpx;
}
.cate-content.data-v-184e0e3f {
  z-index: 1;
  background: #fff;
  min-height: 300rpx;
}
.cate-left.data-v-184e0e3f {
  position: fixed;
  top: calc(var(--window-top));
  left: var(--window-left);
  bottom: var(--window-bottom);
  width: 173rpx;
  height: calc(100% - var(--window-top) - var(--window-bottom) - 90rpx) !important;
  background: #f8f8f8;
  color: #444;
}
.type-nav.data-v-184e0e3f {
  position: relative;
  height: 90rpx;
  z-index: 10;
  display: block;
  font-size: 26rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.type-nav.selected.data-v-184e0e3f {
  background: #fff;
  border-right: none;
  font-size: 28rpx;
  color: var(--main-bg);
}
.goods-list.data-v-184e0e3f {
  background: #fff;
  position: relative;
}
.goods-item.data-v-184e0e3f {
  padding: 28rpx 22rpx;
  display: flex;
}
.goods-item-left.data-v-184e0e3f {
  position: relative;
  background: #fff;
  margin-right: 20rpx;
}
.goods-item-left .image.data-v-184e0e3f {
  display: block;
  width: 180rpx;
  height: 180rpx;
}
.goods-item-right.data-v-184e0e3f {
  position: relative;
  flex: 1;
}
.goods-item-right .goods-name.data-v-184e0e3f {
  display: block;
  width: 100%;
  min-height: 68rpx;
  font-size: 28rpx;
  line-height: 1.3;
  color: #333;
}
.goods-item-desc.data-v-184e0e3f {
  margin-top: 20rpx;
}
.goods-item-desc .people.data-v-184e0e3f {
  margin-right: 14rpx;
}
.goods-item-desc .desc-footer.data-v-184e0e3f {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  right: 0rpx;
  bottom: 0rpx;
  min-height: 44rpx;
}
.goods-item-desc .desc-footer .item-prices.data-v-184e0e3f {
  padding-right: 6rpx;
}
.goods-item-desc .desc-footer .item-prices .price-x.data-v-184e0e3f {
  margin-right: 14rpx;
  color: var(--main-bg);
  font-size: 28rpx;
}
.goods-item-desc .desc-footer .item-prices .price-y.data-v-184e0e3f {
  color: #999;
  text-decoration: line-through;
  font-size: 24rpx;
}
.sub-cate-list.data-v-184e0e3f {
  background-color: #fff;
  width: 100%;
  z-index: 9;
  padding: 8rpx 40rpx 0 14rpx;
  overflow: hidden;
  position: -webkit-sticky;
  position: sticky;
  top: calc(var(--window-top));
}
.sub-cate-list.display-fold.data-v-184e0e3f {
  height: 86rpx;
}
.sub-cate-list .nav-icon.data-v-184e0e3f {
  position: absolute;
  right: 16rpx;
  top: 12rpx;
  font-size: 32rpx;
}
.sub-cate-list .sub-cate-item.data-v-184e0e3f {
  float: left;
  background: #f8f8f8;
  padding: 10rpx 30rpx;
  margin-right: 22rpx;
  margin-bottom: 24rpx;
  font-size: 26rpx;
  border-radius: 14rpx;
  border: 1rpx solid #f8f8f8;
}
.sub-cate-list .sub-cate-item.selected.data-v-184e0e3f {
  color: var(--main-bg);
  border: 1rpx solid var(--main-bg);
}
.mask.data-v-184e0e3f {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 8;
  background-color: rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease-in-out 0s;
}