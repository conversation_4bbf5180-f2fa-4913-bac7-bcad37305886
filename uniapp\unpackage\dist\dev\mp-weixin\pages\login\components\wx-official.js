"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_queryStringify = require("../../../utils/queryStringify.js");
const store_index = require("../../../store/index.js");
const utils_util = require("../../../utils/util.js");
const api_wxofficial = require("../../../api/wxofficial.js");
const _sfc_main = {
  data() {
    return {
      // 页面来源是否为微信回调
      isCallback: false
    };
  },
  created() {
    this.onCallback();
    this.redirectUrl();
  },
  methods: {
    // 处理微信回调
    onCallback() {
      const wxParam = utils_queryStringify.queryStringify.parse(window.location.search);
      if (!utils_util.isEmpty(wxParam)) {
        const url = window.location.href.replace(window.location.search, "");
        window.location.href = url + "?" + utils_util.urlEncode(wxParam);
        return;
      }
      const query = this.$route.query;
      if (utils_util.isEmpty(query) || !query.code) {
        return;
      }
      this.isCallback = true;
      api_wxofficial.Api.oauthUserInfo(query.code).then(({ data }) => {
        common_vendor.index.__f__("log", "at pages/login/components/wx-official.vue:47", "用户同意了授权");
        common_vendor.index.__f__("log", "at pages/login/components/wx-official.vue:48", "userInfo：", data);
        this.onAuthSuccess(data);
      });
    },
    // 授权成功事件
    // 这里分为两个逻辑:
    // 1.将openid和userInfo提交到后端，如果存在该用户 则实现自动登录，无需再填写手机号
    // 2.如果不存在该用户, 则显示注册页面, 需填写手机号
    // 3.如果后端报错了, 则显示错误信息
    async onAuthSuccess({ userInfo, encryptedData, iv }) {
      const app = this;
      store_index.store.dispatch("LoginWxOfficial", {
        partyData: { oauth: "WXOFFICIAL", userInfo, encryptedData, iv },
        refereeId: store_index.store.getters.refereeId
      }).then((result) => {
        app.$toast(result.message);
        common_vendor.index.$emit("syncRefresh", true);
        setTimeout(() => app.onNavigateBack(), 2e3);
      }).catch((err) => {
        const resultData = err.result.data;
        if (utils_util.isEmpty(resultData)) {
          app.$toast(err.result.message);
        }
        if (resultData.isBindMobile) {
          app.onEmitSuccess({ userInfo, encryptedData, iv });
        }
      });
    },
    // 跳转到微信授权
    redirectUrl() {
      if (this.isCallback) {
        return;
      }
      const callbackUrl = window.location.href;
      api_wxofficial.Api.oauthUrl(callbackUrl).then((result) => {
        const url = result.data.redirectUrl;
        window.location.href = url;
      });
    },
    // 将oauth提交给父级
    async onEmitSuccess({ userInfo, encryptedData, iv }) {
      this.$emit("success", {
        oauth: "WXOFFICIAL",
        // 第三方登录类型: WXOFFICIAL
        userInfo,
        encryptedData,
        iv
      });
    },
    /**
     * 登录成功-跳转回原页面
     */
    onNavigateBack(delta = 1) {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        common_vendor.index.navigateBack({
          delta: Number(delta || 1)
        });
      } else {
        this.$navTo("pages/index/index");
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {};
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/login/components/wx-official.js.map
