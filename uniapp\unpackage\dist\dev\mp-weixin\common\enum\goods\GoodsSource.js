"use strict";
const common_enum_enum = require("../enum.js");
const GoodsSourceEnum = new common_enum_enum.Enum([
  { key: "MAIN", name: "普通商品", value: 10 },
  { key: "BARGAIN", name: "砍价商品", value: 20 },
  { key: "SHARP", name: "秒杀商品", value: 30 },
  { key: "GROUPON", name: "拼团商品", value: 40 }
]);
exports.GoodsSourceEnum = GoodsSourceEnum;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/common/enum/goods/GoodsSource.js.map
