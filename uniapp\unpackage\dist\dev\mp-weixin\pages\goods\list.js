"use strict";
const common_vendor = require("../../common/vendor.js");
const core_mixins_wxofficial = require("../../core/mixins/wxofficial.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const api_goods_index = require("../../api/goods/index.js");
const core_app = require("../../core/app.js");
const Search = () => "../../components/search/index.js";
const pageSize = 15;
const showViewKey = "GoodsList-ShowView";
const _sfc_main = {
  components: {
    Search
  },
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin, core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      showView: false,
      // 列表显示方式 (true列表、false平铺)
      sortType: "all",
      // 排序类型
      sortPrice: false,
      // 价格排序 (true高到低 false低到高)
      options: {},
      // 当前页面参数
      list: core_app.getEmptyPaginateObj(),
      // 商品列表数据
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: {
          size: pageSize
        },
        // 数量要大于4条才显示无更多数据
        noMoreSize: 4
      }
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.options = options;
    this.setShowView();
    this.setWxofficialShareData();
  },
  methods: {
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getGoodsList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 设置默认列表显示方式
    setShowView() {
      this.showView = common_vendor.index.getStorageSync(showViewKey) || false;
    },
    /**
     * 获取商品列表
     * @param {number} pageNo 页码
     */
    getGoodsList(pageNo = 1) {
      const app = this;
      common_vendor.index.__f__("log", "at pages/goods/list.vue:177", app.options);
      const param = {
        sortType: app.sortType,
        sortPrice: Number(app.sortPrice),
        categoryId: app.options.categoryId || 0,
        type: 0,
        goodsName: app.options.search || "",
        page: pageNo
      };
      return new Promise((resolve, reject) => {
        api_goods_index.list(param).then((result) => {
          const newList = result.data.list;
          app.list.data = core_app.getMoreListData(newList, app.list, pageNo);
          resolve(newList);
        }).catch(reject);
      });
    },
    // 切换排序方式
    handleSortType(newSortType) {
      const app = this;
      const newSortPrice = newSortType === "price" ? !app.sortPrice : true;
      app.sortType = newSortType;
      app.sortPrice = newSortPrice;
      app.list = core_app.getEmptyPaginateObj();
      app.mescroll.resetUpScroll();
    },
    // 切换列表显示方式
    handleShowView() {
      const app = this;
      app.showView = !app.showView;
      common_vendor.index.setStorageSync(showViewKey, app.showView);
    },
    // 跳转商品详情页
    onTargetDetail(goodsId) {
      this.$navTo("pages/goods/detail", {
        goodsId
      });
    },
    /**
     * 商品搜索
     */
    handleSearch() {
      const searchPageUrl = "pages/search/index";
      let pages = getCurrentPages();
      if (pages.length > 1 && pages[pages.length - 2].route === searchPageUrl) {
        common_vendor.index.navigateBack();
        return;
      }
      this.$navTo(searchPageUrl);
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      this.updateShareCardData({
        title: "商品列表"
      });
    }
  },
  /**
   * 设置分享内容
   */
  onShareAppMessage() {
    const app = this;
    return {
      title: "商品列表",
      path: "/pages/goods/list?" + this.$getShareUrlParams(app.options)
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    const app = this;
    return {
      title: "商品列表",
      path: "/pages/goods/list?" + this.$getShareUrlParams(app.options)
    };
  }
};
if (!Array) {
  const _component_search = common_vendor.resolveComponent("search");
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  (_component_search + _easycom_mescroll_body2)();
}
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  _easycom_mescroll_body();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o($options.handleSearch),
    b: common_vendor.p({
      tips: $data.options.search ? $data.options.search : "搜索商品"
    }),
    c: $data.showView
  }, $data.showView ? {} : {}, {
    d: common_vendor.o((...args) => $options.handleShowView && $options.handleShowView(...args)),
    e: $data.sortType === "all" ? 1 : "",
    f: common_vendor.o(($event) => $options.handleSortType("all")),
    g: $data.sortType === "sales" ? 1 : "",
    h: common_vendor.o(($event) => $options.handleSortType("sales")),
    i: $data.sortType === "price" && !$data.sortPrice ? 1 : "",
    j: $data.sortType === "price" && $data.sortPrice ? 1 : "",
    k: $data.sortType === "price" ? 1 : "",
    l: common_vendor.o(($event) => $options.handleSortType("price")),
    m: common_vendor.f($data.list.data, (item, index, i0) => {
      return common_vendor.e($data.showView ? common_vendor.e({
        a: item.goods_image,
        b: common_vendor.t(item.goods_name),
        c: common_vendor.t(item.selling_point),
        d: common_vendor.t(item.goods_sales),
        e: common_vendor.t(item.goods_price_min),
        f: item.line_price_min > 0
      }, item.line_price_min > 0 ? {
        g: common_vendor.t(item.line_price_min)
      } : {}) : common_vendor.e({
        h: item.goods_image,
        i: common_vendor.t(item.goods_name),
        j: common_vendor.t(item.goods_price_min),
        k: item.line_price_min > 0
      }, item.line_price_min > 0 ? {
        l: common_vendor.t(item.line_price_min)
      } : {}), {
        m: index,
        n: common_vendor.o(($event) => $options.onTargetDetail(item.goods_id), index)
      });
    }),
    n: $data.showView,
    o: common_vendor.n("column-" + ($data.showView ? "1" : "2")),
    p: common_vendor.sr("mescrollRef", "7f2f18c6-0"),
    q: common_vendor.o(_ctx.mescrollInit),
    r: common_vendor.o(_ctx.downCallback),
    s: common_vendor.o($options.upCallback),
    t: common_vendor.p({
      sticky: true,
      down: {
        native: true
      },
      up: $data.upOption
    }),
    v: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7f2f18c6"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/goods/list.js.map
