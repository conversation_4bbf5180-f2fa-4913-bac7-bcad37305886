/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.bred.data-v-30818bab {
  border: 1px solid #f88;
  box-sizing: border-box;
}
.content.data-v-30818bab {
  padding: 26rpx;
  min-width: 500rpx;
}
.top-box.data-v-30818bab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ccc;
  padding-bottom: 20rpx;
}
.top-box .qiandao.data-v-30818bab {
  background: #f55;
  color: #eee;
  padding: 5rpx 20rpx;
  border-radius: 20rpx;
}
.weeks-box.data-v-30818bab {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #ccc;
}
.weeks-box .week-item.data-v-30818bab {
  text-align: center;
  width: 98rpx;
}
.day-box.data-v-30818bab {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #ccc;
  padding-bottom: 30rpx;
  padding-top: 30rpx;
}
.day-box .tian.data-v-30818bab {
  width: 14.2%;
  text-align: center;
  height: 90rpx;
  line-height: 90rpx;
}
.day-box .jinri.data-v-30818bab {
  position: relative;
  border: 1px solid #f88;
  background: #fee;
  transform: scale(0.8);
  border-radius: 10rpx;
  box-sizing: border-box;
  font-size: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 90rpx;
  line-height: 60rpx;
}
.day-box .jinri.data-v-30818bab::after {
  content: "今日";
  font-size: 10rpx;
  position: absolute;
  top: 40rpx;
}
.day-box .isactive.data-v-30818bab {
  border: 3px double #f88;
  line-height: 82rpx;
  transform: scale(0.8);
  border-radius: 10rpx;
  box-sizing: border-box;
  font-size: 40rpx;
}
.day-box .isqiandao.data-v-30818bab {
  color: #fff;
  background: #f77;
  transform: scale(0.8);
  font-size: 40rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 90rpx;
  line-height: 60rpx;
}
.day-box .isqiandao.data-v-30818bab::after {
  content: "已签到";
  font-size: 10rpx;
  position: absolute;
  top: 40rpx;
}
.day-box .downday.data-v-30818bab, .day-box .upday.data-v-30818bab {
  color: #b1b1b1;
}