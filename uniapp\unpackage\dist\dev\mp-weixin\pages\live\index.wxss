/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.live-room-item.data-v-810271e5 {
  width: 710rpx;
  margin: 0 auto 20rpx auto;
  padding: 25rpx 24rpx;
  background: #fff;
  border-radius: 5rpx;
  box-sizing: border-box;
  box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.05);
}
.live-room-item.data-v-810271e5:first-child {
  margin-top: 20rpx;
}
.room-head.data-v-810271e5 {
  color: #b6b6b6;
  line-height: 40rpx;
}
.room-head .live-status_icon.data-v-810271e5 {
  margin-right: 15rpx;
  font-size: 34rpx;
}
.room-head .live-status_text.data-v-810271e5 {
  font-size: 26rpx;
}
.live-status__101 .room-head.data-v-810271e5 {
  color: #db384b;
}
.live-status__102 .room-head.data-v-810271e5 {
  color: #db384b;
}
.room-name.data-v-810271e5 {
  margin-top: 10rpx;
  font-size: 28rpx;
}
.room-cover.data-v-810271e5 {
  margin-top: 15rpx;
}
.room-cover .image.data-v-810271e5 {
  display: block;
  width: 100%;
  height: 371rpx;
}
.room-anchor.data-v-810271e5 {
  margin-top: 20rpx;
}
.room-anchor .anchor-avatar.data-v-810271e5 {
  margin-right: 12rpx;
}
.room-anchor .anchor-avatar .image.data-v-810271e5 {
  display: block;
  width: 45rpx;
  height: 45rpx;
  border-radius: 50%;
}
.room-anchor .anchor-name.data-v-810271e5 {
  font-size: 26rpx;
}
.live-status_text2.data-v-810271e5 {
  color: #b6b6b6;
  font-size: 26rpx;
}
.live-status__101 .live-status_text2.data-v-810271e5 {
  color: #db384b;
}
.live-status__102 .live-status_text2.data-v-810271e5 {
  color: #db384b;
}