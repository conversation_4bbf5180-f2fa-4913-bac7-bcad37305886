"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  index: "xj.exchange/index",
  exchange: "xj.exchange/exchange"
};
const index = (param) => {
  return utils_request_index.$http.get(api.index, param);
};
const exchange = (data) => {
  return utils_request_index.$http.post(api.exchange, data);
};
exports.exchange = exchange;
exports.index = index;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/xj/exchange.js.map
