/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.shortcut.data-v-4227a94a {
  position: fixed;
  right: calc(var(--window-right) + var(--right));
  bottom: calc(var(--window-bottom) + var(--bottom));
  width: 76rpx;
  height: 76rpx;
  line-height: 1;
  z-index: 5;
  border-radius: 50%;
}
.nav-item.data-v-4227a94a {
  position: absolute;
  bottom: 0;
  padding: 0;
  width: 76rpx;
  height: 76rpx;
  color: #fff;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  transform: rotate(0deg);
  opacity: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.nav-item .iconfont.data-v-4227a94a {
  font-size: 40rpx;
}
.nav-item__switch.data-v-4227a94a {
  opacity: 1;
}
.shortcut_click_show.data-v-4227a94a {
  margin-bottom: 0;
  background: #ff5454;
}
.show_80.data-v-4227a94a {
  bottom: 384rpx;
  animation: show_80-4227a94a 0.3s forwards;
}
.show_60.data-v-4227a94a {
  bottom: 288rpx;
  animation: show_60-4227a94a 0.3s forwards;
}
.show_40.data-v-4227a94a {
  bottom: 192rpx;
  animation: show_40-4227a94a 0.3s forwards;
}
.show_20.data-v-4227a94a {
  bottom: 96rpx;
  animation: show_20-4227a94a 0.3s forwards;
}
@keyframes show_20-4227a94a {
from {
    bottom: 0;
    transform: rotate(0deg);
    opacity: 0;
}
to {
    bottom: 96rpx;
    transform: rotate(360deg);
    opacity: 1;
}
}
@keyframes show_40-4227a94a {
from {
    bottom: 0;
    transform: rotate(0deg);
    opacity: 0;
}
to {
    bottom: 192rpx;
    transform: rotate(360deg);
    opacity: 1;
}
}
@keyframes show_60-4227a94a {
from {
    bottom: 0;
    transform: rotate(0deg);
    opacity: 0;
}
to {
    bottom: 288rpx;
    transform: rotate(360deg);
    opacity: 1;
}
}
@keyframes show_80-4227a94a {
from {
    bottom: 0;
    transform: rotate(0deg);
    opacity: 0;
}
to {
    bottom: 384rpx;
    transform: rotate(360deg);
    opacity: 1;
}
}
.hide_80.data-v-4227a94a {
  bottom: 0;
  animation: hide_80-4227a94a 0.3s;
  opacity: 0;
}
.hide_60.data-v-4227a94a {
  bottom: 0;
  animation: hide_60-4227a94a 0.3s;
  opacity: 0;
}
.hide_40.data-v-4227a94a {
  bottom: 0;
  animation: hide_40-4227a94a 0.3s;
  opacity: 0;
}
.hide_20.data-v-4227a94a {
  bottom: 0;
  animation: hide_20-4227a94a 0.3s;
  opacity: 0;
}
@keyframes hide_20-4227a94a {
from {
    bottom: 96rpx;
    transform: rotate(360deg);
    opacity: 1;
}
to {
    bottom: 0;
    transform: rotate(0deg);
    opacity: 0;
}
}
@keyframes hide_40-4227a94a {
from {
    bottom: 192rpx;
    transform: rotate(360deg);
    opacity: 1;
}
to {
    bottom: 0;
    transform: rotate(0deg);
    opacity: 0;
}
}
@keyframes hide_60-4227a94a {
from {
    bottom: 288rpx;
    transform: rotate(360deg);
    opacity: 1;
}
to {
    bottom: 0;
    transform: rotate(0deg);
    opacity: 0;
}
}
@keyframes hide_80-4227a94a {
from {
    bottom: 384rpx;
    transform: rotate(360deg);
    opacity: 1;
}
to {
    bottom: 0;
    transform: rotate(0deg);
    opacity: 0;
}
}