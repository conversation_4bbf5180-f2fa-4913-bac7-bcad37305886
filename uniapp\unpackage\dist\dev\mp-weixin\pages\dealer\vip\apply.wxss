
page {
		background: #fff;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.input.data-v-ba78718c {
  display: block;
}
.c-violet.data-v-ba78718c {
  color: #786cff;
}
.col-bb.data-v-ba78718c {
  color: #bbb;
}
.dealer-bg .image.data-v-ba78718c {
  width: 100%;
}
.widget-body.data-v-ba78718c {
  position: relative;
  width: 88%;
  margin: 0 auto;
}
.widget-body .widget.data-v-ba78718c {
  box-sizing: border-box;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.11);
  border-radius: 12rpx;
}
.widget__capital.data-v-ba78718c {
  padding: 10rpx 0;
  margin-top: -60rpx;
}
.widget__capital .capital__item.data-v-ba78718c {
  height: 80rpx;
  padding: 10rpx 35rpx;
  font-size: 28rpx;
  border-bottom: 1rpx solid #e7e7e7;
  box-sizing: border-box;
}
.widget__capital .capital__item.data-v-ba78718c:last-child {
  border-bottom: none;
}
.widget__capital .capital__item .item__left.data-v-ba78718c {
  margin-right: 20rpx;
}
.widget__capital .capital__item .item__right .input.data-v-ba78718c {
  font-size: 28rpx;
  text-align: right;
}
.capital__lowest.data-v-ba78718c {
  padding-right: 20rpx;
  box-sizing: border-box;
  font-size: 26rpx;
}
.widget__form.data-v-ba78718c {
  padding: 10rpx 0 20rpx 0;
}
.widget__form .form__title.data-v-ba78718c {
  padding: 16rpx 35rpx;
  border-bottom: 1rpx solid #f3f3f3;
}
.widget__form .form__box.data-v-ba78718c {
  padding: 20rpx 35rpx;
}
.widget__form .form__field.data-v-ba78718c {
  height: 80rpx;
  padding: 0;
  box-sizing: border-box;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}
.widget__form .form__field .radio__icon.data-v-ba78718c {
  font-size: 38rpx;
  margin-right: 12rpx;
}
.widget__form .form__field .field-input .input.data-v-ba78718c {
  background-color: #f9f9f9;
  padding: 16rpx 20rpx;
  font-size: 28rpx;
}
.form-submit.data-v-ba78718c {
  margin-top: 40rpx;
}
.form-submit button.data-v-ba78718c {
  font-size: 30rpx;
  background: #786cff;
  border: 1rpx solid #786cff;
  color: white;
  border-radius: 50rpx;
  padding: 0 120rpx;
}
.form-submit button[disabled].data-v-ba78718c {
  background: #8e84fc;
  border-color: #8e84fc;
  color: white;
}