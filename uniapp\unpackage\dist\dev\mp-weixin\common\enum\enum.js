"use strict";
class Enum {
  constructor(param) {
    const keyArr = [];
    const valueArr = [];
    if (!Array.isArray(param)) {
      throw new Error("param is not an array!");
    }
    param.map((element) => {
      if (!element.key || !element.name) {
        return;
      }
      keyArr.push(element.key);
      valueArr.push(element.value);
      this[element.key] = element;
      if (element.key !== element.value) {
        this[element.value] = element;
      }
    });
    this.data = param;
    this.keyArr = keyArr;
    this.valueArr = valueArr;
  }
  // 根据key得到对象
  keyOf(key) {
    return this.data[this.keyArr.indexOf(key)];
  }
  // 根据key得到对象
  valueOf(key) {
    return this.data[this.valueArr.indexOf(key)];
  }
  // 根据key获取name值
  getNameByKey(key) {
    const prop = this.keyOf(key);
    if (!prop) {
      throw new Error("No enum constant" + key);
    }
    return prop.name;
  }
  // 根据value获取name值
  getNameByValue(value) {
    const prop = this.valueOf(value);
    if (!prop) {
      throw new Error("No enum constant" + value);
    }
    return prop.name;
  }
  // 根据key获取value值
  getValueByKey(key) {
    const prop = this.keyOf(key);
    if (!prop) {
      throw new Error("No enum constant" + key);
    }
    return prop.key;
  }
  // 返回源数组
  getData() {
    return this.data;
  }
}
exports.Enum = Enum;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/common/enum/enum.js.map
