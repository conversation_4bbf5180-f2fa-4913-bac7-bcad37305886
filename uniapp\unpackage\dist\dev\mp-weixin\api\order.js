"use strict";
const utils_request_index = require("../utils/request/index.js");
const api = {
  todoCounts: "order/todoCounts",
  list: "order/list",
  detail: "order/detail",
  express: "order/express",
  cancel: "order/cancel",
  extractQrcode: "order/extractQrcode",
  receipt: "order/receipt",
  pay: "order/pay"
};
function todoCounts(param, option) {
  return utils_request_index.$http.get(api.todoCounts, param, option);
}
function list(param, option) {
  return utils_request_index.$http.get(api.list, param, option);
}
function detail(orderId, param) {
  return utils_request_index.$http.get(api.detail, { orderId, ...param });
}
function express(orderId, param) {
  return utils_request_index.$http.get(api.express, { orderId, ...param });
}
function cancel(orderId, data) {
  return utils_request_index.$http.post(api.cancel, { orderId, ...data });
}
function receipt(orderId, data) {
  return utils_request_index.$http.post(api.receipt, { orderId, ...data });
}
function extractQrcode(orderId, param) {
  return utils_request_index.$http.get(api.extractQrcode, { orderId, ...param });
}
exports.cancel = cancel;
exports.detail = detail;
exports.express = express;
exports.extractQrcode = extractQrcode;
exports.list = list;
exports.receipt = receipt;
exports.todoCounts = todoCounts;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/order.js.map
