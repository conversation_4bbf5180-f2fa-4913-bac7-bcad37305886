"use strict";
const common_vendor = require("../../common/vendor.js");
const api_address = require("../../api/address.js");
const Empty = () => "../../components/empty/index.js";
const _sfc_main = {
  components: {
    Empty
  },
  data() {
    return {
      //当前页面参数
      options: {},
      // 正在加载
      isLoading: true,
      // 收货地址列表
      list: [],
      // 默认收货地址
      defaultId: null
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.options = options;
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getPageData();
  },
  methods: {
    // 获取页面数据
    getPageData() {
      const app = this;
      app.isLoading = true;
      Promise.all([app.getDefaultId(), app.getAddressList()]).then(() => {
        app.onReorder();
      }).finally(() => app.isLoading = false);
    },
    // 获取收货地址列表
    getAddressList() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_address.list().then((result) => {
          app.list = result.data.list;
          resolve(result);
        }).catch(reject);
      });
    },
    // 获取默认的收货地址
    getDefaultId() {
      return new Promise((resolve, reject) => {
        const app = this;
        api_address.defaultId().then((result) => {
          app.defaultId = result.data.defaultId;
          resolve(result);
        }).catch(reject);
      });
    },
    // 列表排序把默认收货地址放到最前
    onReorder() {
      const app = this;
      app.list.sort((item) => {
        return item.address_id == app.defaultId ? -1 : 1;
      });
    },
    /**
     * 添加新地址
     */
    handleCreate() {
      this.$navTo("pages/address/create");
    },
    /**
     * 编辑地址
     * @param {int} addressId 收货地址ID
     */
    handleUpdate(addressId) {
      this.$navTo("pages/address/update", { addressId });
    },
    /**
     * 删除收货地址
     * @param {int} addressId 收货地址ID
     */
    handleRemove(addressId) {
      const app = this;
      common_vendor.index.showModal({
        title: "提示",
        content: "您确定要删除当前收货地址吗?",
        success({ confirm }) {
          confirm && app.onRemove(addressId);
        }
      });
    },
    /**
     * 确认删除收货地址
     * @param {int} addressId 收货地址ID
     */
    onRemove(addressId) {
      const app = this;
      api_address.remove(addressId).then((result) => {
        app.getPageData();
      });
    },
    /**
     * 设置为默认地址
     * @param {Object} addressId
     */
    handleSetDefault(addressId) {
      const app = this;
      api_address.setDefault(addressId).then((result) => {
        app.options.from === "checkout" && common_vendor.index.navigateBack();
      });
    }
  }
};
if (!Array) {
  const _easycom_u_radio2 = common_vendor.resolveComponent("u-radio");
  const _easycom_u_radio_group2 = common_vendor.resolveComponent("u-radio-group");
  const _component_empty = common_vendor.resolveComponent("empty");
  (_easycom_u_radio2 + _easycom_u_radio_group2 + _component_empty)();
}
const _easycom_u_radio = () => "../../uni_modules/vk-uview-ui/components/u-radio/u-radio.js";
const _easycom_u_radio_group = () => "../../uni_modules/vk-uview-ui/components/u-radio-group/u-radio-group.js";
if (!Math) {
  (_easycom_u_radio + _easycom_u_radio_group)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.list, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.t(item.phone),
        c: common_vendor.f(item.region, (region, idx, i1) => {
          return {
            a: common_vendor.t(region),
            b: idx
          };
        }),
        d: common_vendor.t(item.detail),
        e: common_vendor.t(item.address_id == $data.defaultId ? "默认" : "选择"),
        f: "c47feaaa-1-" + i0 + "," + ("c47feaaa-0-" + i0),
        g: common_vendor.p({
          name: item.address_id,
          ["active-color"]: _ctx.appTheme.mainBg
        }),
        h: common_vendor.o(($event) => $options.handleSetDefault(item.address_id), index),
        i: "c47feaaa-0-" + i0,
        j: common_vendor.o(($event) => $data.defaultId = $event, index),
        k: common_vendor.o(($event) => $options.handleUpdate(item.address_id), index),
        l: common_vendor.o(($event) => $options.handleRemove(item.address_id), index),
        m: index
      };
    }),
    b: common_vendor.p({
      modelValue: $data.defaultId
    }),
    c: !$data.list.length
  }, !$data.list.length ? {
    d: common_vendor.p({
      isLoading: $data.isLoading,
      tips: "亲，暂无收货地址"
    })
  } : {}, {
    e: common_vendor.o(($event) => $options.handleCreate()),
    f: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c47feaaa"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/address/index.js.map
