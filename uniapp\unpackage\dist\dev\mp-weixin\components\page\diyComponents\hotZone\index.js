"use strict";
const components_page_diyComponents_mixin = require("../mixin.js");
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object,
    data: Object
  },
  mixins: [components_page_diyComponents_mixin.mixin],
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {}
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $props.data.imgUrl,
    b: `${$props.itemStyle.paddingTop * 2}rpx ${$props.itemStyle.paddingLeft * 2}rpx 0`,
    c: common_vendor.f($props.data.maps, (item, index, i0) => {
      return {
        a: index,
        b: `${item.width}rpx`,
        c: `${item.height}rpx`,
        d: `${item.left}rpx`,
        e: `${item.top}rpx`,
        f: common_vendor.o(($event) => _ctx.onLink(item.link), index)
      };
    }),
    d: `${$props.itemStyle.paddingTop * 2}rpx`,
    e: $props.itemStyle.background
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-2220ea1b"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/components/page/diyComponents/hotZone/index.js.map
