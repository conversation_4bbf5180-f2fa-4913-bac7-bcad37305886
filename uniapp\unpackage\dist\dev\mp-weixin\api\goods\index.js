"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "goods/list",
  detail: "goods/detail",
  basic: "goods/basic",
  specData: "goods/specData",
  skuInfo: "goods/skuInfo",
  recommended: "goods/recommended",
  poster: "goods/poster"
};
const list = (param, option) => {
  return utils_request_index.$http.get(api.list, param, option);
};
const detail = (goodsId, verifyStatus = true, param = {}) => {
  verifyStatus = Number(verifyStatus);
  return utils_request_index.$http.get(api.detail, { goodsId, verifyStatus, ...param });
};
const basic = (goodsId, verifyStatus = true, param = {}) => {
  verifyStatus = Number(verifyStatus);
  return utils_request_index.$http.get(api.basic, { goodsId, verifyStatus, ...param });
};
const specData = (goodsId) => {
  return utils_request_index.$http.get(api.specData, { goodsId });
};
const recommended = () => {
  return utils_request_index.$http.get(api.recommended);
};
const skuInfo = (goodsId, goodsSkuId, param) => {
  return utils_request_index.$http.get(api.skuInfo, { goodsId, goodsSkuId, ...param });
};
const poster = (param) => {
  return utils_request_index.$http.get(api.poster, param);
};
exports.basic = basic;
exports.detail = detail;
exports.list = list;
exports.poster = poster;
exports.recommended = recommended;
exports.skuInfo = skuInfo;
exports.specData = specData;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/goods/index.js.map
