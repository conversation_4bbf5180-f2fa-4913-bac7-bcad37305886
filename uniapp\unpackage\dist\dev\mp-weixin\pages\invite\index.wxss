/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.p-vip.data-v-e54fd6d8 {
  height: 100vh;
  padding: 20rpx 0rpx;
}
.p-vip .p-title.data-v-e54fd6d8 {
  text-align: center;
  font-weight: bold;
}
.p-vip .p-content.data-v-e54fd6d8 {
  padding: 20rpx 30rpx;
  padding-bottom: 150rpx;
}
.p-vip .p-footer.data-v-e54fd6d8 {
  position: fixed;
  bottom: 0;
  background: #FFF;
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
  color: #FFF;
}
.p-vip .p-footer .cancel.data-v-e54fd6d8 {
  background-color: #ff0000;
  padding: 10rpx 30rpx;
  margin: 0 30rpx;
  border-radius: 10rpx;
}
.p-vip .p-footer .queu.data-v-e54fd6d8 {
  background-color: #cd8c0c;
  padding: 10rpx 30rpx;
  border-radius: 10rpx;
  margin: 0 30rpx;
}
.container.data-v-e54fd6d8 {
  background: #fff;
}
.info-pop.data-v-e54fd6d8 {
  padding: 0rpx 20rpx;
  padding-bottom: 20rpx;
}
.info-pop .title.data-v-e54fd6d8 {
  text-align: center;
  font-weight: bold;
  padding: 20rpx 0;
}
.info-pop .footer.data-v-e54fd6d8 {
  width: 50%;
  margin-left: 25%;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  background-color: #55aa00;
  color: #FFF;
  border-radius: 50rpx;
  margin-top: 30rpx;
}
.footer2.data-v-e54fd6d8 {
  position: fixed;
  bottom: 0;
  width: 100%;
}
.footer2 .btn.data-v-e54fd6d8 {
  background: #55aa00;
  text-align: center;
  padding: 20rpx 0;
  font-weight: bold;
  color: #FFF;
}