/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
page {
  background: #efeff4;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-f4fbf777 {
  background: #efeff4;
}
.sharp-tabs.data-v-f4fbf777 {
  background: #fff;
}
.sharp-tabs .sharp-tabs--container.data-v-f4fbf777 {
  background: #30353c;
}
.sharp-tabs .tabs-item.data-v-f4fbf777 {
  position: relative;
  min-width: 170rpx;
  height: 110rpx;
  background: #30353c;
  color: #fff;
  padding: 15rpx 45rpx;
  text-align: center;
  box-sizing: border-box;
  white-space: nowrap;
}
.sharp-tabs .tabs-item .item-time.data-v-f4fbf777 {
  font-size: 32rpx;
}
.sharp-tabs .tabs-item .item-status.data-v-f4fbf777 {
  font-size: 24rpx;
}
.sharp-tabs .tabs-item .item-title.data-v-f4fbf777 {
  font-size: 30rpx;
}
.sharp-tabs .tabs-item.active.data-v-f4fbf777 {
  background: var(--main-bg);
}
.sharp-tabs .tabs-item.active.data-v-f4fbf777::after {
  content: "";
  display: block;
  position: absolute;
  z-index: 999;
  bottom: -15rpx;
  left: 50%;
  margin-left: -12rpx;
  width: 0;
  height: 0;
  border: 20rpx solid var(--main-bg);
  border-left-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-bottom-width: 0;
}
.sharp-active.data-v-f4fbf777 {
  background: #fff;
  padding: 26rpx 0;
}
.sharp-active .active-status.data-v-f4fbf777 {
  font-size: 32rpx;
  color: var(--main-bg);
  margin-bottom: 20rpx;
}
.sharp-active .active-status .active-status--icon.data-v-f4fbf777 {
  margin-right: 10rpx;
}
.sharp-active .active-status .active-status--time.data-v-f4fbf777 {
  margin-right: 10rpx;
}
.active--count-down.data-v-f4fbf777 {
  font-size: 26rpx;
  height: 40rpx;
}
.goods-hall.data-v-f4fbf777 {
  padding-top: 20rpx;
}
.goods-hall .goods-item.data-v-f4fbf777 {
  background: #fff;
  padding: 30rpx 16rpx;
  border-bottom: 1rpx solid #e7e7e7;
}
.goods-hall .goods-item .goods-image image.data-v-f4fbf777 {
  display: block;
  width: 220rpx;
  height: 220rpx;
}
.goods-hall .goods-item .goods-info.data-v-f4fbf777 {
  width: 498rpx;
  padding-top: 8rpx;
  margin-left: 15rpx;
  position: relative;
}
.goods-hall .goods-item .goods-info .goods-name.data-v-f4fbf777 {
  font-size: 28rpx;
  margin-bottom: 20rpx;
  min-height: 68rpx;
  line-height: 1.3;
}
.yoo-progress.data-v-f4fbf777 {
  position: relative;
  width: 70%;
  height: 28rpx;
  border-radius: 12rpx;
  background: #f8b6b6;
}
.yoo-progress--portion.data-v-f4fbf777 {
  width: 0%;
  height: 100%;
  border-radius: 12rpx;
  background: linear-gradient(to right, var(--main-bg2), var(--main-bg));
}
.yoo-progress--text.data-v-f4fbf777 {
  color: #fff;
  font-size: 24rpx;
  line-height: 1.6;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.sharp-sales.data-v-f4fbf777 {
  margin-left: 30rpx;
  font-size: 24rpx;
  color: var(--main-bg);
  white-space: nowrap;
}
.sharp-price.data-v-f4fbf777 {
  margin-top: 40rpx;
  line-height: 1;
}
.sharp-price .seckill-price.data-v-f4fbf777 {
  font-size: 32rpx;
  color: var(--main-bg);
  margin-bottom: -2rpx;
}
.sharp-price .original-price.data-v-f4fbf777 {
  margin-left: 5rpx;
  font-size: 24rpx;
  color: #818181;
  text-decoration: line-through;
}
.opt-touch.data-v-f4fbf777 {
  position: absolute;
  bottom: 0;
  right: 10rpx;
}
.opt-touch .touch-btn.data-v-f4fbf777 {
  font-size: 26rpx;
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
  border-radius: 30rpx;
  padding: 10rpx 30rpx;
}