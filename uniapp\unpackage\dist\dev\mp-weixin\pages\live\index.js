"use strict";
const common_vendor = require("../../common/vendor.js");
const core_mixins_wxofficial = require("../../core/mixins/wxofficial.js");
const core_app = require("../../core/app.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const api_live_room = require("../../api/live/room.js");
const pageSize = 15;
const _sfc_main = {
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin, core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: { size: pageSize },
        // 数量要大于3条才显示无更多数据
        noMoreSize: 3
      },
      // 直播间列表
      list: core_app.getEmptyPaginateObj()
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getLiveRoomList();
    this.setWxofficialShareData();
  },
  methods: {
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getLiveRoomList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    /**
     * 获取直播间列表
     * @param {Number} pageNo 页码
     */
    getLiveRoomList(pageNo = 1) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_live_room.list({ page: pageNo }).then((result) => {
          const newList = result.data.list;
          app.list.data = core_app.getMoreListData(newList, app.list, pageNo);
          resolve(newList);
        }).catch(reject);
      });
    },
    // 进入直播间
    onTargetLiveRoom(roomId) {
      const { platform, $toast } = this;
      if (platform !== "MP-WEIXIN") {
        $toast("很抱歉，直播间仅支持微信小程序，请前往微信小程序端");
        return;
      }
      const customParams = core_app.getShareParams({
        path: "pages/index/index"
      });
      common_vendor.wx$1.navigateTo({
        url: `plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${roomId}&custom_params=${encodeURIComponent(JSON.stringify(customParams))}`
      });
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      this.updateShareCardData({ title: "直播列表" });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    return {
      title: "直播列表",
      path: "/pages/live/index?" + this.$getShareUrlParams()
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    return {
      title: "直播列表",
      path: "/pages/live/index?" + this.$getShareUrlParams()
    };
  }
};
if (!Array) {
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  _easycom_mescroll_body2();
}
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  _easycom_mescroll_body();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.list.data, (item, index, i0) => {
      return common_vendor.e({
        a: item.live_status == 101
      }, item.live_status == 101 ? {} : {}, {
        b: item.live_status == 102
      }, item.live_status == 102 ? {} : {}, {
        c: item.live_status >= 103
      }, item.live_status >= 103 ? {} : {}, {
        d: common_vendor.t(item.live_status_text_1),
        e: common_vendor.t(item.room_name),
        f: item.share_img,
        g: item.share_img,
        h: common_vendor.t(item.anchor_name),
        i: common_vendor.t(item.live_status_text_2),
        j: index,
        k: common_vendor.o(($event) => $options.onTargetLiveRoom(item.room_id), index),
        l: common_vendor.n(`live-room-item live-status__${item.live_status}`)
      });
    }),
    b: common_vendor.sr("mescrollRef", "810271e5-0"),
    c: common_vendor.o(_ctx.mescrollInit),
    d: common_vendor.o(_ctx.downCallback),
    e: common_vendor.o($options.upCallback),
    f: common_vendor.p({
      sticky: true,
      down: {
        native: true
      },
      up: $data.upOption
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-810271e5"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/live/index.js.map
