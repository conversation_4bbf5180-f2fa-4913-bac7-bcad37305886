"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  center: "xj.vip/center",
  submit: "xj.vip/submit",
  tradeQuery: "xj.vip/tradeQuery"
};
const submit = (data) => {
  return utils_request_index.$http.post(api.submit, { form: data });
};
function tradeQuery(param) {
  return utils_request_index.$http.get(api.tradeQuery, param);
}
exports.submit = submit;
exports.tradeQuery = tradeQuery;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/xj/vip.js.map
