/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.goods-comment.data-v-30b3f719 {
  padding: 20rpx 30rpx;
  background-color: #fff;
}
.item-title.data-v-30b3f719 {
  font-size: 28rpx;
  margin-bottom: 25rpx;
}
.item-title .total.data-v-30b3f719 {
  margin: 0 4rpx;
}
.item-title .show-more.data-v-30b3f719 {
  margin-right: 8rpx;
  font-size: 24rpx;
}
.comment-item.data-v-30b3f719 {
  padding: 15rpx 5rpx;
  margin-bottom: 10rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.comment-item.data-v-30b3f719:last-child {
  margin-bottom: 0;
  border-bottom: none;
}
.comment-item .comment-item_row.data-v-30b3f719 {
  margin-bottom: 10rpx;
}
.user-info.data-v-30b3f719 {
  margin-right: 15rpx;
}
.user-info .user-avatar.data-v-30b3f719 {
  margin-right: 10rpx;
}
.user-info .user-name.data-v-30b3f719 {
  font-size: 24rpx;
}
.item-content.data-v-30b3f719 {
  color: #333;
  margin: 16rpx 0;
  max-height: 76rpx;
  line-height: 38rpx;
}
.comment-time.data-v-30b3f719 {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}