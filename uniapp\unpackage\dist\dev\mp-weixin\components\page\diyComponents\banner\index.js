"use strict";
const common_vendor = require("../../../../common/vendor.js");
const components_page_diyComponents_mixin = require("../mixin.js");
const _sfc_main = {
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object,
    dataList: Array
  },
  mixins: [components_page_diyComponents_mixin.mixin],
  data() {
    return {
      windowWidth: 750,
      indicatorDots: false,
      // 是否显示面板指示点
      autoplay: true,
      // 是否自动切换
      duration: 800,
      // 滑动动画时长
      imgHeights: [],
      // 图片的高度
      swiperIndex: 0
      // 当前banne所在滑块指针
    };
  },
  created() {
    const { windowWidth } = common_vendor.index.getWindowInfo();
    this.windowWidth = windowWidth > 750 ? 750 : windowWidth;
  },
  methods: {
    // 计算图片高度
    onLoadImage({ detail }) {
      const app = this;
      const { width, height } = detail;
      const ratio = width / height;
      const viewHeight = app.windowWidth / ratio;
      app.imgHeights.push(viewHeight);
    },
    // 记录当前指针
    onChangeItem({ detail }) {
      this.swiperIndex = detail.current;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.dataList, (dataItem, index, i0) => {
      return {
        a: dataItem.imgUrl,
        b: common_vendor.o(($event) => _ctx.onLink(dataItem.link), index),
        c: common_vendor.o((...args) => $options.onLoadImage && $options.onLoadImage(...args), index),
        d: index
      };
    }),
    b: $data.autoplay,
    c: $data.duration,
    d: $props.itemStyle.interval * 1e3,
    e: common_vendor.o((...args) => $options.onChangeItem && $options.onChangeItem(...args)),
    f: common_vendor.f($props.dataList, (dataItem, index, i0) => {
      return {
        a: $data.swiperIndex == index ? 1 : "",
        b: index
      };
    }),
    g: $props.itemStyle.btnColor,
    h: common_vendor.n($props.itemStyle.btnShape),
    i: `${$data.imgHeights[$data.swiperIndex]}px`
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-89f6e7a1"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/components/page/diyComponents/banner/index.js.map
