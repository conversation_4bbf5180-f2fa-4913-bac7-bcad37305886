"use strict";
const common_vendor = require("../../common/vendor.js");
const core_mixins_wxofficial = require("../../core/mixins/wxofficial.js");
const utils_color = require("../../utils/color.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const core_app = require("../../core/app.js");
require("../../common/enum/groupon/GoodsStatus.js");
const common_enum_groupon_TaskStatus = require("../../common/enum/groupon/TaskStatus.js");
const common_enum_groupon_ActiveType = require("../../common/enum/groupon/ActiveType.js");
const common_enum_groupon_ActiveStatus = require("../../common/enum/groupon/ActiveStatus.js");
const api_groupon_task = require("../../api/groupon/task.js");
const api_groupon_goods = require("../../api/groupon/goods.js");
const common_model_groupon_Setting = require("../../common/model/groupon/Setting.js");
const pageSize = 15;
const _sfc_main = {
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin, core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 枚举类
      ActiveStatusEnum: common_enum_groupon_ActiveStatus.ActiveStatusEnum,
      ActiveTypeEnum: common_enum_groupon_ActiveType.ActiveTypeEnum,
      TaskStatusEnum: common_enum_groupon_TaskStatus.TaskStatusEnum,
      // 当前tab索引
      curTab: 0,
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: { size: pageSize },
        // 数量要大于3条才显示无更多数据
        noMoreSize: 3
      },
      // 拼团设置
      setting: {},
      // 拼团商品列表
      goodsList: core_app.getEmptyPaginateObj(),
      // 我的拼单列表
      myList: core_app.getEmptyPaginateObj()
    };
  },
  watch: {
    curTab(val) {
      common_vendor.index.setNavigationBarTitle({ title: val == 0 ? "拼团活动" : "我的拼团" });
    }
  },
  computed: {
    // 标签背景色
    tagBackgroundColor() {
      return utils_color.hex2rgba(this.appTheme.mainBg, 0.1);
    },
    // 标签边框颜色
    tagBorderColor() {
      return utils_color.hex2rgba(this.appTheme.mainBg, 0.6);
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.tab) {
      this.curTab = options.tab;
    }
    this.getSetting();
    this.setWxofficialShareData();
  },
  methods: {
    // 获取拼团设置
    getSetting() {
      common_model_groupon_Setting.GrouponSettingModel.data().then((setting) => this.setting = setting);
    },
    /**
     * 上拉加载的回调
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getListData(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 获取列表数据(根据当前选项卡判断调用的方法)
    getListData(pageNo) {
      const apiFuc = {
        0: this.getGoodsList,
        1: this.getMyList
      };
      return apiFuc[this.curTab](pageNo);
    },
    /**
     * 获取拼团商品列表
     * @param {Number} pageNo 页码
     */
    getGoodsList(pageNo = 1) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_groupon_goods.list({ page: pageNo }).then((result) => {
          const newList = result.data.list;
          app.goodsList.data = core_app.getMoreListData(newList, app.goodsList, pageNo);
          resolve(newList);
        }).catch(reject);
      });
    },
    // 获取我的拼团列表
    getMyList(pageNo) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_groupon_task.myList({ page: pageNo }).then((result) => {
          const newList = result.data.list;
          app.myList.data = core_app.getMoreListData(newList, app.myList, pageNo);
          resolve(newList);
        }).catch(reject);
      });
    },
    // 切换当前选项卡
    onChangeTab(key = 0) {
      const app = this;
      app.curTab = key;
      app.goodsList = core_app.getEmptyPaginateObj();
      app.myList = core_app.getEmptyPaginateObj();
      app.mescroll.resetUpScroll();
    },
    // 跳转到拼团商品详情
    onTargetGoods(item) {
      this.$navTo("pages/groupon/goods/index", { grouponGoodsId: item.groupon_goods_id });
    },
    // 跳转拼单详情页
    onTargetTask(item) {
      this.$navTo("pages/groupon/task/index", { taskId: item.task_id });
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      this.updateShareCardData({ title: "拼团活动" });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    const params = this.$getShareUrlParams();
    return {
      title: "拼团活动",
      path: `/pages/groupon/index?${params}`
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    const params = this.$getShareUrlParams();
    return {
      title: "拼团活动",
      path: `/pages/groupon/index?${params}`
    };
  }
};
if (!Array) {
  const _easycom_u_tag2 = common_vendor.resolveComponent("u-tag");
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  (_easycom_u_tag2 + _easycom_mescroll_body2)();
}
const _easycom_u_tag = () => "../../uni_modules/vk-uview-ui/components/u-tag/u-tag.js";
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  (_easycom_u_tag + _easycom_mescroll_body)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.curTab == 0 && $data.setting.backdrop
  }, $data.curTab == 0 && $data.setting.backdrop ? {
    b: $data.setting.backdrop.src
  } : {}, {
    c: $data.curTab == 0
  }, $data.curTab == 0 ? {
    d: common_vendor.f($data.goodsList.data, (item, index, i0) => {
      return common_vendor.e({
        a: item.active_type != $data.ActiveTypeEnum.NORMAL.value
      }, item.active_type != $data.ActiveTypeEnum.NORMAL.value ? {
        b: common_vendor.t($data.ActiveTypeEnum[item.active_type].name2)
      } : {}, {
        c: item.goods_image,
        d: common_vendor.t(item.goods_name),
        e: "d25fc2e1-1-" + i0 + ",d25fc2e1-0",
        f: common_vendor.p({
          color: _ctx.appTheme.mainBg,
          ["border-color"]: _ctx.appTheme.mainBg,
          text: `${item.show_people}人团`,
          type: "error",
          size: "mini",
          mode: "plain"
        }),
        g: item.active_sales
      }, item.active_sales ? {
        h: "d25fc2e1-2-" + i0 + ",d25fc2e1-0",
        i: common_vendor.p({
          color: _ctx.appTheme.mainBg,
          ["border-color"]: $options.tagBorderColor,
          ["bg-color"]: $options.tagBackgroundColor,
          text: `已团${item.active_sales}件`,
          type: "error",
          size: "mini"
        })
      } : {}, {
        j: common_vendor.t(item.groupon_price),
        k: common_vendor.t(item.original_price),
        l: common_vendor.o(($event) => $options.onTargetGoods(item), index),
        m: index
      });
    })
  } : {}, {
    e: $data.curTab == 1
  }, $data.curTab == 1 ? {
    f: common_vendor.f($data.myList.data, (item, index, i0) => {
      return common_vendor.e({
        a: item.active_type != $data.ActiveTypeEnum.NORMAL.value
      }, item.active_type != $data.ActiveTypeEnum.NORMAL.value ? {
        b: common_vendor.t($data.ActiveTypeEnum[item.active_type].name2)
      } : {}, {
        c: item.goods.goods_image,
        d: common_vendor.t(item.goods.goods_name),
        e: item.status == $data.TaskStatusEnum.NORMAL.value
      }, item.status == $data.TaskStatusEnum.NORMAL.value ? {
        f: "d25fc2e1-3-" + i0 + ",d25fc2e1-0",
        g: common_vendor.p({
          text: `已拼${item.joined_people}人，还差${item.people - item.joined_people}人`,
          type: "warning",
          size: "mini"
        })
      } : {
        h: "d25fc2e1-4-" + i0 + ",d25fc2e1-0",
        i: common_vendor.p({
          text: `${item.people}人团`,
          type: "error",
          size: "mini",
          mode: "plain"
        })
      }, {
        j: common_vendor.t($data.TaskStatusEnum[item.status].name),
        k: item.status == $data.TaskStatusEnum.NORMAL.value
      }, item.status == $data.TaskStatusEnum.NORMAL.value ? {} : {}, {
        l: common_vendor.o(($event) => $options.onTargetTask(item), index),
        m: index
      });
    })
  } : {}, {
    g: common_vendor.o(($event) => $options.onChangeTab(0)),
    h: $data.curTab == 0 ? 1 : "",
    i: common_vendor.o(($event) => $options.onChangeTab(1)),
    j: $data.curTab == 1 ? 1 : "",
    k: common_vendor.sr("mescrollRef", "d25fc2e1-0"),
    l: common_vendor.o(_ctx.mescrollInit),
    m: common_vendor.o($options.upCallback),
    n: common_vendor.p({
      sticky: true,
      down: {
        use: false
      },
      up: $data.upOption
    }),
    o: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-d25fc2e1"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/groupon/index.js.map
