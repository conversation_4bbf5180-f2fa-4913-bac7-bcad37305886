"use strict";
const common_vendor = require("../common/vendor.js");
const store_index = require("../store/index.js");
const utils_util = require("../utils/util.js");
const common_constant_index = require("../common/constant/index.js");
require("./config/index.js");
const showSuccess = (msg, callback) => {
  common_vendor.index.showToast({
    title: msg,
    icon: "success",
    mask: true,
    duration: 1500,
    success() {
      callback && callback();
    }
  });
};
const showError = (msg, callback) => {
  common_vendor.index.showModal({
    title: "友情提示",
    content: msg,
    showCancel: false,
    success(res) {
      callback && callback();
    }
  });
};
const showToast = (msg, duration = 1500, mask = true) => {
  common_vendor.index.showToast({
    title: msg,
    // 提示的内容
    icon: "none",
    mask,
    // 是否显示透明蒙层，防止触摸穿透 (支付宝小程序不支持)
    duration
    // 提示的延迟时间，单位毫秒，默认：1500	
  });
};
const getTabBarLinks = () => {
  const tabBarLinks = [
    "pages/index/index",
    "pages/category/index",
    "pages/cart/index",
    "pages/user/index"
  ];
  return tabBarLinks;
};
const buildUrL = (h5Url, path, params) => {
  let complete = h5Url;
  if (!utils_util.isEmpty(path)) {
    complete += "#/" + path;
    const shareParamsStr = getShareUrlParams(params);
    if (!utils_util.isEmpty(shareParamsStr)) {
      complete += "?" + shareParamsStr;
    }
  }
  return complete;
};
const getShareUrlParams = (params) => {
  return utils_util.urlEncode(getShareParams(params));
};
const getShareParams = (params) => {
  return {
    refereeId: store_index.store.getters.userId,
    // 推荐人ID
    ...params
  };
};
const navTo = (url, query = {}, modo = "navigateTo") => {
  if (!url || url.length == 0) {
    return false;
  }
  if (utils_util.inArray(url, getTabBarLinks())) {
    common_vendor.index.switchTab({
      url: `/${url}`
    });
    return true;
  }
  const queryStr = !utils_util.isEmpty(query) ? "?" + utils_util.urlEncode(query) : "";
  modo === "navigateTo" && common_vendor.index.navigateTo({
    url: `/${url}${queryStr}`
  });
  modo === "redirectTo" && common_vendor.index.redirectTo({
    url: `/${url}${queryStr}`
  });
  return true;
};
const getCurrentPage = () => {
  const pages = getCurrentPages();
  const pathInfo = pages[pages.length - 1].$page.fullPath.split("?");
  return { path: pathInfo[0].slice(1), query: utils_util.urlDecode(pathInfo[1]) };
};
const getCartTotalNum = (value) => {
  const cartTotal = common_vendor.index.getStorageSync("cartTotalNum") || 0;
  return checkLogin() ? cartTotal : 0;
};
const setCartTotalNum = (value) => {
  common_vendor.index.setStorageSync("cartTotalNum", Number(value));
};
const setCartTabBadge = () => {
  const cartTabbarIndex = 2;
  const cartTotal = getCartTotalNum();
  if (cartTotal > 0) {
    common_vendor.index.setTabBarBadge({
      index: cartTabbarIndex,
      text: `${cartTotal}`
    });
  } else {
    common_vendor.index.removeTabBarBadge({
      index: cartTabbarIndex
    });
  }
  return;
};
const checkLogin = () => {
  return !!store_index.store.getters.userId;
};
const getEmptyPaginateObj = () => {
  return utils_util.cloneObj(common_constant_index.paginate);
};
const getMoreListData = (resList, oldList, pageNo) => {
  if (pageNo == 1)
    oldList.data = [];
  return oldList.data.concat(resList.data);
};
const sceneDecode = (str) => {
  if (str === void 0)
    return {};
  const data = {};
  const params = decodeURIComponent(str).split(",");
  for (const i in params) {
    const val = params[i].split(":");
    val.length > 0 && val[0] && (data[val[0]] = val[1] || null);
  }
  return data;
};
const getSceneData = (query) => {
  return utils_util.hasOwnProperty(query, "scene") ? sceneDecode(query.scene) : {};
};
const checkModuleKey = (moduleKey) => {
  return utils_util.inArray(moduleKey, store_index.store.getters.modules);
};
const checkModules = (moduleKeys) => {
  return moduleKeys.filter((val) => checkModuleKey(val)).length > 0;
};
const filterModules = (array) => {
  return array.filter((item) => !item.moduleKey || checkModuleKey(item.moduleKey));
};
const onLink = (linkObj) => {
  if (!linkObj)
    return false;
  if (linkObj.type === "PAGE") {
    navTo(linkObj.param.path, linkObj.param.query);
  }
  if (linkObj.type === "CUSTOM") {
    navTo(linkObj.param.path, utils_util.urlDecode(linkObj.param.queryStr));
  }
  if (linkObj.type === "MP-WEIXIN") {
    common_vendor.index.navigateToMiniProgram({
      appId: linkObj.param.appId,
      path: linkObj.param.path
    });
  }
  if (linkObj.type === "URL") {
    common_vendor.index.setClipboardData({
      data: linkObj.param.url,
      success: () => showToast("链接已复制"),
      fail: ({ errMsg }) => showToast("复制失败 " + errMsg)
    });
  }
  return true;
};
exports.buildUrL = buildUrL;
exports.checkLogin = checkLogin;
exports.checkModuleKey = checkModuleKey;
exports.checkModules = checkModules;
exports.filterModules = filterModules;
exports.getCurrentPage = getCurrentPage;
exports.getEmptyPaginateObj = getEmptyPaginateObj;
exports.getMoreListData = getMoreListData;
exports.getSceneData = getSceneData;
exports.getShareParams = getShareParams;
exports.getShareUrlParams = getShareUrlParams;
exports.getTabBarLinks = getTabBarLinks;
exports.navTo = navTo;
exports.onLink = onLink;
exports.setCartTabBadge = setCartTabBadge;
exports.setCartTotalNum = setCartTotalNum;
exports.showError = showError;
exports.showSuccess = showSuccess;
exports.showToast = showToast;
//# sourceMappingURL=../../.sourcemap/mp-weixin/core/app.js.map
