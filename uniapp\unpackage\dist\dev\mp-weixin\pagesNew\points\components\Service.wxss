/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.service-wrapper.data-v-28b9d8e2 {
  min-height: 24rpx;
  margin-bottom: -24rpx;
}
.service-simple.data-v-28b9d8e2 {
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
}
.service-simple .s-list.data-v-28b9d8e2 {
  flex: 1;
  margin-left: -15rpx;
}
.service-simple .s-item.data-v-28b9d8e2 {
  float: left;
  font-size: 26rpx;
  margin: 8rpx 15rpx;
}
.service-simple .s-item .item-icon.data-v-28b9d8e2 {
  color: var(--main-bg);
}
.service-simple .s-item .item-val.data-v-28b9d8e2 {
  margin-left: 12rpx;
}
.service-content.data-v-28b9d8e2 {
  padding: 24rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 24rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
}
.service-content .title.data-v-28b9d8e2 {
  font-size: 30rpx;
  margin-bottom: 50rpx;
  font-weight: bold;
  text-align: center;
}
.service-content .content-scroll.data-v-28b9d8e2 {
  min-height: 400rpx;
  max-height: 760rpx;
}
.service-content .s-list.data-v-28b9d8e2 {
  padding: 0 30rpx 0 80rpx;
}
.service-content .s-item.data-v-28b9d8e2 {
  position: relative;
  margin-bottom: 60rpx;
}
.service-content .s-item .item-icon.data-v-28b9d8e2 {
  position: absolute;
  top: 6rpx;
  left: -50rpx;
  color: var(--main-bg);
}
.service-content .s-item .item-val.data-v-28b9d8e2 {
  font-size: 28rpx;
}
.service-content .s-item .item-summary.data-v-28b9d8e2 {
  font-size: 26rpx;
  margin-top: 20rpx;
  color: #6d6d6d;
}