"use strict";
const common_vendor = require("../../../common/vendor.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const core_app = require("../../../core/app.js");
const api_dealer_withdraw = require("../../../api/dealer/withdraw.js");
const common_model_dealer_Setting = require("../../../common/model/dealer/Setting.js");
require("../../../common/enum/dealer/withdraw/PayType.js");
const common_enum_dealer_withdraw_ApplyStatus = require("../../../common/enum/dealer/withdraw/ApplyStatus.js");
const pageSize = 15;
const ApplyStatusText = {};
const ApplyStatusColor = {
  [common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.WAIT.value]: "col-8",
  [common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.PASSED.value]: "col-green",
  [common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.REJECT.value]: "col-m",
  [common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.PAYMENT.value]: "col-green",
  [common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.SUCCESS.value]: "col-green",
  [common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.FAIL.value]: "col-s"
};
const _sfc_main = {
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin],
  data() {
    return {
      // 枚举类
      ApplyStatusEnum: common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum,
      ApplyStatusColor,
      ApplyStatusText,
      // 选项卡列表
      tabList: [],
      // 当前选项
      curTab: 0,
      // 列表数据
      list: core_app.getEmptyPaginateObj(),
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: {
          size: pageSize
        },
        // 数量要大于12条才显示无更多数据
        noMoreSize: 12,
        // 空布局
        empty: {
          tip: "亲，暂无相关数据"
        }
      },
      // 驳回原因弹窗
      showRejectReason: false,
      rejectReason: ""
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.type = options.type ? options.type : -1;
    this.getSetting();
  },
  methods: {
    queu(item) {
      if (common_vendor.index.canIUse("requestMerchantTransfer")) {
        common_vendor.index.requestMerchantTransfer({
          mchId: "1716812986",
          appId: "wx68bd33014864318f",
          package: item.package_info,
          success: (res) => {
            common_vendor.index.__f__("log", "at pages/dealer/withdraw/list.vue:135", "success:", res);
          },
          fail: (res) => {
            common_vendor.index.__f__("log", "at pages/dealer/withdraw/list.vue:138", "fail:", res);
          }
        });
      } else {
        common_vendor.index.showModal({
          content: "你的微信版本过低，请更新至最新版本。",
          showCancel: false
        });
      }
    },
    // 获取分销设置
    getSetting() {
      const app = this;
      common_model_dealer_Setting.SettingModel.data().then((setting) => {
        const words = setting.words.withdraw_list;
        app.setPageTitle(words.title);
        app.setTabList(words.words);
        app.setStatusText(words.words);
      });
    },
    // 设置页面标题
    setPageTitle(title) {
      common_vendor.index.setNavigationBarTitle({
        title: title.value
      });
    },
    // 设置选项卡数据
    setTabList(words) {
      const app = this;
      app.tabList = [
        {
          value: -1,
          name: words.all.value
        },
        {
          value: common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.WAIT.value,
          name: words.apply_10.value
        },
        {
          value: common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.PASSED.value,
          name: words.apply_20.value
        },
        {
          value: common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.REJECT.value,
          name: words.apply_30.value
        },
        {
          value: common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.PAYMENT.value,
          name: words.apply_40.value
        }
      ];
    },
    // 设置状态文字
    setStatusText(words) {
      const app = this;
      app.ApplyStatusText = {
        [common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.WAIT.value]: words.apply_10.value,
        [common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.PASSED.value]: words.apply_20.value,
        [common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.REJECT.value]: words.apply_30.value,
        [common_enum_dealer_withdraw_ApplyStatus.ApplyStatusEnum.PAYMENT.value]: words.apply_40.value
      };
    },
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 获取提现列表
    getList(pageNo = 1) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_dealer_withdraw.list({
          applyStatus: app.getTabValue(),
          type: app.type,
          page: pageNo
        }).then((result) => {
          const newList = result.data.list;
          app.list.data = core_app.getMoreListData(newList, app.list, pageNo);
          resolve(newList);
        });
      });
    },
    // 获取当前标签项的值
    getTabValue() {
      const app = this;
      if (app.tabList.length) {
        return app.tabList[app.curTab].value;
      }
      return -1;
    },
    // 切换标签项
    onChangeTab(index) {
      const app = this;
      app.curTab = index;
      app.onRefreshList();
    },
    // 刷新列表数据
    onRefreshList() {
      this.list = core_app.getEmptyPaginateObj();
      setTimeout(() => {
        this.mescroll.resetUpScroll();
      }, 120);
    },
    // 显示驳回原因
    handleShowRejectReason(item) {
      this.showRejectReason = true;
      this.rejectReason = item.reject_reason;
    }
  }
};
if (!Array) {
  const _easycom_u_tabs2 = common_vendor.resolveComponent("u-tabs");
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  (_easycom_u_tabs2 + _easycom_mescroll_body2 + _easycom_u_modal2)();
}
const _easycom_u_tabs = () => "../../../uni_modules/vk-uview-ui/components/u-tabs/u-tabs.js";
const _easycom_mescroll_body = () => "../../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
const _easycom_u_modal = () => "../../../uni_modules/vk-uview-ui/components/u-modal/u-modal.js";
if (!Math) {
  (_easycom_u_tabs + _easycom_mescroll_body + _easycom_u_modal)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o($options.onChangeTab),
    b: common_vendor.o(($event) => $data.curTab = $event),
    c: common_vendor.p({
      list: $data.tabList,
      ["is-scroll"]: false,
      ["active-color"]: "#786cff",
      duration: 0.2,
      modelValue: $data.curTab
    }),
    d: common_vendor.f($data.list.data, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.money),
        b: common_vendor.t(item.create_time),
        c: common_vendor.t(item.status_text),
        d: common_vendor.n($data.ApplyStatusColor[item.apply_status]),
        e: item.apply_status == $data.ApplyStatusEnum.REJECT.value
      }, item.apply_status == $data.ApplyStatusEnum.REJECT.value ? {
        f: common_vendor.o(($event) => $options.handleShowRejectReason(item), index)
      } : {}, {
        g: common_vendor.t(item.source),
        h: item.apply_status == 40
      }, item.apply_status == 40 ? {
        i: common_vendor.o(($event) => $options.queu(item), index)
      } : {}, {
        j: index
      });
    }),
    e: common_vendor.sr("mescrollRef", "9c9f2815-0"),
    f: common_vendor.o(_ctx.mescrollInit),
    g: common_vendor.o($options.upCallback),
    h: common_vendor.p({
      sticky: true,
      down: {
        use: false
      },
      up: $data.upOption
    }),
    i: common_vendor.t($data.rejectReason),
    j: common_vendor.o(($event) => $data.showRejectReason = $event),
    k: common_vendor.p({
      title: "驳回原因",
      modelValue: $data.showRejectReason
    }),
    l: common_vendor.s(_ctx.appThemeStyle)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-9c9f2815"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/dealer/withdraw/list.js.map
