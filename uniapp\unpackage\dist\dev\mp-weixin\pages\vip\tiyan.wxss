/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-688ea882 {
  min-height: 100vh;
}
.top-img1.data-v-688ea882 {
  width: 100%;
  height: 0;
  padding-bottom: 94%;
  /* 3068/3261 = 94% */
  background-image: url("https://xinjiang.zhanyuankj.cn/10001/20250730/1bddc8d45eb45e4b0f5aa026799c6673.jpg");
  background-size: 100% 100%;
}
.top-img2.data-v-688ea882 {
  width: 100%;
  height: 0;
  padding-bottom: 37%;
  /* 1207/3266 = 37% */
  background-image: url("https://xinjiang.zhanyuankj.cn/10001/20250730/da39058eb440b6bbc8bda146a5cf240d.jpg");
  background-size: 100% 100%;
}
.top-img3.data-v-688ea882 {
  width: 100%;
  height: 0;
  padding-bottom: 14%;
  background-image: url("https://xinjiang.zhanyuankj.cn/10001/20250730/25ceb82043a1e5c2984c67b958724b42.jpg");
  background-size: 100% 100%;
  position: relative;
}
.coupon-text.data-v-688ea882 {
  position: absolute;
  left: 38%;
  top: 36%;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}
.content.data-v-688ea882 {
  padding: 20rpx;
}
.header.data-v-688ea882 {
  text-align: center;
  padding: 40rpx 0;
  color: white;
}
.header .header-title.data-v-688ea882 {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}
.header .header-subtitle.data-v-688ea882 {
  font-size: 28rpx;
  opacity: 0.9;
}
.lottery-section.data-v-688ea882 {
  margin: 50rpx 0;
  text-align: center;
}
.lottery-title.data-v-688ea882 {
  color: white;
  margin-bottom: 100rpx;
}
.lottery-title text.data-v-688ea882 {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}
.lottery-title .lottery-subtitle.data-v-688ea882 {
  font-size: 24rpx;
  opacity: 0.9;
}
.lottery-container.data-v-688ea882 {
  position: relative;
  width: 500rpx;
  height: 500rpx;
  margin: 0 auto 110rpx auto;
}
.lottery-btn.data-v-688ea882 {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 20rpx rgba(39, 174, 96, 0.3);
}
.lottery-btn.disabled.data-v-688ea882 {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.5);
  box-shadow: none;
}
.action-buttons.data-v-688ea882 {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
  gap: 30rpx;
}
.action-btn.data-v-688ea882 {
  flex: 1;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.lottery-wheel.data-v-688ea882 {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  transition: transform 2s ease-out;
  box-shadow: 0 0 30rpx rgba(0, 0, 0, 0.2);
  background: conic-gradient(#e74c3c 0deg 45deg, #1abc9c 45deg 90deg, #3498db 90deg 135deg, #27ae60 135deg 180deg, #f39c12 180deg 225deg, #9b59b6 225deg 270deg, #7f8c8d 270deg 315deg, #95a5a6 315deg 360deg);
}
.lottery-wheel.no-transition.data-v-688ea882 {
  transition: none;
}
.lottery-sector.data-v-688ea882 {
  position: absolute;
  width: 100%;
  height: 100%;
}
.lottery-sector .prize-text.data-v-688ea882 {
  position: absolute;
  font-size: 24rpx;
  font-weight: bold;
  color: white;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);
  left: 50%;
  top: 50%;
}
.sector-0 .prize-text.data-v-688ea882 {
  transform: translate(-50%, -50%) rotate(22.5deg) translateY(-160rpx);
}
.sector-1 .prize-text.data-v-688ea882 {
  transform: translate(-50%, -50%) rotate(67.5deg) translateY(-160rpx);
}
.sector-2 .prize-text.data-v-688ea882 {
  transform: translate(-50%, -50%) rotate(112.5deg) translateY(-160rpx);
}
.sector-3 .prize-text.data-v-688ea882 {
  transform: translate(-50%, -50%) rotate(157.5deg) translateY(-160rpx);
}
.sector-4 .prize-text.data-v-688ea882 {
  transform: translate(-50%, -50%) rotate(202.5deg) translateY(-160rpx);
}
.sector-5 .prize-text.data-v-688ea882 {
  transform: translate(-50%, -50%) rotate(247.5deg) translateY(-160rpx);
}
.sector-6 .prize-text.data-v-688ea882 {
  transform: translate(-50%, -50%) rotate(292.5deg) translateY(-160rpx);
}
.sector-7 .prize-text.data-v-688ea882 {
  transform: translate(-50%, -50%) rotate(337.5deg) translateY(-160rpx);
}
.lottery-pointer.data-v-688ea882 {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border: 2rpx solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  box-shadow: 0 3rpx 9rpx rgba(0, 0, 0, 0.3);
}
.pointer-arrow.data-v-688ea882 {
  width: 80rpx;
  height: 80rpx;
  position: absolute;
  top: -50rpx;
  left: 50%;
  transform: translateX(-50%);
}