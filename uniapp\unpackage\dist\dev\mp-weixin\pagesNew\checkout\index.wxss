/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.flow-delivery.data-v-b460ee3b {
  padding: 34rpx 30rpx;
  background: #fff url("data:image/png;base64,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") bottom left repeat-x;
  background-size: 120rpx auto;
  margin-bottom: 25rpx;
}
.flow-delivery .detail-location.data-v-b460ee3b {
  font-size: 36rpx;
}
.flow-delivery .detail-content.data-v-b460ee3b {
  padding: 0 20rpx;
}
.flow-delivery .detail-content .detail-content__title-phone.data-v-b460ee3b {
  margin-left: 10rpx;
}
.flow-delivery .detail-content .detail-content__describe.data-v-b460ee3b {
  font-size: 28rpx;
  color: #777;
}
.flow-delivery .detail-content__title.data-v-b460ee3b {
  margin-bottom: 6rpx;
}
.flow-all-money .ipt-wrapper .input.data-v-b460ee3b {
  font-size: 28rpx;
  display: block;
  height: 75rpx;
}
.checkout_list.data-v-b460ee3b {
  padding: 20rpx 30rpx 4rpx 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #f8f8f8;
}
.checkout_list .flow-shopList.data-v-b460ee3b {
  padding: 5rpx 0 10rpx;
  border-bottom: 1rpx solid #f8f8f8;
}
.checkout_list .flow-shopList.data-v-b460ee3b:last-child {
  border-bottom: 0;
}
.flow-header-left.data-v-b460ee3b {
  padding-left: 90rpx;
}
.flow-shopList .flow-list-right .flow-cont.price-delete.data-v-b460ee3b {
  font-size: 26rpx;
  color: #777;
  text-decoration: line-through;
}
.flow-shopList .grade-price.data-v-b460ee3b {
  padding-top: 8rpx;
  font-size: 28rpx;
  color: var(--main-bg);
  text-align: right;
}
.flow-shopList .goods-name.data-v-b460ee3b {
  font-size: 28rpx;
  color: #333;
  min-height: 68rpx;
  line-height: 1.3;
}
.popup__coupon.data-v-b460ee3b {
  background: #fff;
  box-sizing: border-box;
  padding: 30rpx 0 30rpx 0;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 30rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 30rpx);
}
.popup__coupon .coupon__do_not.data-v-b460ee3b {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 30rpx;
  box-shadow: 0 -12rpx 30rpx -6rpx rgba(151, 151, 151, 0.1);
}
.popup__coupon .coupon__do_not .control.data-v-b460ee3b {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 90%;
  height: 72rpx;
  color: #888;
  border: 1rpx solid #e3e3e3;
  border-radius: 10rpx;
}
.popup__coupon .coupon__title.data-v-b460ee3b {
  text-align: center;
  margin-bottom: 15rpx;
}
.popup__coupon .coupon-list.data-v-b460ee3b {
  padding: 40rpx 30rpx;
}
.popup__coupon .coupon-item.data-v-b460ee3b {
  margin-bottom: 22rpx;
  font-size: 24rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.06);
  position: relative;
}
.popup__coupon .item-wrapper.data-v-b460ee3b {
  display: flex;
  align-items: center;
}
.popup__coupon .item-wrapper.disable .coupon-tag.data-v-b460ee3b {
  background: linear-gradient(-113deg, #bdbdbd, #a2a1a2);
}
.popup__coupon .item-wrapper.disable .coupon-reduce.data-v-b460ee3b {
  color: #757575;
}
.popup__coupon .item-wrapper.disable .state-text.data-v-b460ee3b {
  color: #757575;
}
.popup__coupon .coupon-tag.data-v-b460ee3b {
  position: absolute;
  left: 0;
  top: 0;
  color: #fff;
  width: 96rpx;
  text-align: center;
  font-size: 22rpx;
  border-radius: 12rpx 0 12rpx 0;
  font-weight: 500;
  height: 34rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
}
.popup__coupon .coupon-left.data-v-b460ee3b {
  width: 242rpx;
  text-align: center;
  display: flex;
  flex-flow: column;
  justify-content: center;
  position: relative;
}
.popup__coupon .coupon-left .coupon-reduce.data-v-b460ee3b {
  color: var(--main-bg);
}
.popup__coupon .coupon-left .coupon-reduce-unit.data-v-b460ee3b {
  display: inline-block;
  margin-right: -4rpx;
  font-size: 32rpx;
  font-weight: 600;
}
.popup__coupon .coupon-left .coupon-reduce-amount.data-v-b460ee3b {
  display: inline-block;
}
.popup__coupon .coupon-left .coupon-reduce-amount .value.data-v-b460ee3b {
  font-size: 48rpx;
}
.popup__coupon .coupon-left .coupon-hint.data-v-b460ee3b {
  margin-top: 12rpx;
  color: #757575;
  font-size: 24rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.popup__coupon .coupon-content.data-v-b460ee3b {
  flex: 1;
  padding: 32rpx 0;
  position: relative;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  align-items: flex-start;
  justify-content: space-between;
}
.popup__coupon .coupon-content .coupon-name.data-v-b460ee3b {
  color: #212121;
  font-size: 28rpx;
  font-weight: 500;
}
.popup__coupon .coupon-content .coupon-middle.data-v-b460ee3b {
  flex: 1;
  padding-top: 12rpx;
  margin-bottom: 26rpx;
}
.popup__coupon .coupon-content .coupon-middle .coupon-expire.data-v-b460ee3b {
  color: #757575;
  font-size: 24rpx;
}
.popup__coupon .coupon-content .coupon-expand.data-v-b460ee3b {
  display: flex;
  align-items: center;
  color: #9e9e9e;
  font-size: 24rpx;
}
.popup__coupon .coupon-content .coupon-expand-arrow.data-v-b460ee3b {
  margin-top: 4rpx;
  font-size: 24rpx;
  display: inline-block;
  vertical-align: initial;
  transform: rotate(0);
  margin-left: 8rpx;
  transition: all 0.15s ease-in-out;
}
.popup__coupon .coupon-content .coupon-expand-arrow.expand.data-v-b460ee3b {
  transform: rotate(180deg);
}
.popup__coupon .coupon-right.data-v-b460ee3b {
  padding-right: 38rpx;
}
.popup__coupon .coupon-right .btn-receive.data-v-b460ee3b,
.popup__coupon .coupon-right .state-text.data-v-b460ee3b {
  text-align: center;
  width: 100rpx;
  padding: 15rpx 0;
}
.popup__coupon .coupon-right .btn-receive.data-v-b460ee3b {
  font-size: 23rpx;
  line-height: 1;
  font-weight: 500;
  border-radius: 8rpx;
  cursor: pointer;
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
}
.popup__coupon .coupon-expand-rules.data-v-b460ee3b {
  display: none;
  position: relative;
}
.popup__coupon .coupon-expand-rules.expand.data-v-b460ee3b {
  top: -30rpx;
  display: block;
}
.popup__coupon .coupon-expand-rules-content.data-v-b460ee3b {
  padding: 8rpx 30rpx 8rpx 242rpx;
  font-weight: 400;
  color: #9e9e9e;
  line-height: 36rpx;
}
.popup__coupon .coupon-expand-rules-content .pre.data-v-b460ee3b {
  font-family: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
}
.points .title.data-v-b460ee3b {
  margin-right: 5rpx;
}
.points .icon-help.data-v-b460ee3b {
  font-size: 28rpx;
}
.points .points-money.data-v-b460ee3b {
  margin-right: 20rpx;
}
.goods-props.data-v-b460ee3b {
  padding-top: 10rpx;
  font-size: 24rpx;
  color: #999;
}
.goods-props .goods-props-item.data-v-b460ee3b {
  float: left;
}
.goods-props .goods-props-item .group-name.data-v-b460ee3b {
  margin-right: 6rpx;
}
.right-arrow.data-v-b460ee3b {
  margin-left: 16rpx;
  font-size: 26rpx;
}
.flow-fixed-footer.data-v-b460ee3b {
  position: fixed;
  bottom: var(--window-bottom);
  left: var(--window-left);
  right: var(--window-right);
  background: #fff;
  border-top: 1px solid #eee;
  z-index: 11;
  padding-bottom: calc(constant(safe-area-inset-bottom) + var(--window-bottom));
  padding-bottom: calc(env(safe-area-inset-bottom) + var(--window-bottom));
}
.flow-fixed-footer .chackout-left.data-v-b460ee3b {
  font-size: 28rpx;
  line-height: 92rpx;
  color: #777;
  flex: 4;
  padding-left: 12px;
}
.flow-fixed-footer .chackout-right.data-v-b460ee3b {
  font-size: 34rpx;
  flex: 2;
}
.flow-fixed-footer .flow-btn.data-v-b460ee3b {
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
  text-align: center;
  line-height: 92rpx;
  display: block;
  font-size: 28rpx;
}
.flow-fixed-footer .flow-btn.disabled.data-v-b460ee3b {
  opacity: 0.6;
}
.points-content.data-v-b460ee3b {
  padding: 30rpx 48rpx;
  font-size: 28rpx;
  line-height: 50rpx;
  text-align: left;
  color: #606266;
  height: 620rpx;
  box-sizing: border-box;
}
.flow-num-box.data-v-b460ee3b {
  font-size: 28rpx;
  color: #777;
  padding: 16rpx 24rpx;
  text-align: right;
}
.flow-shopList.data-v-b460ee3b {
  padding: 18rpx 0;
}
.flow-shopList .flow-list-left.data-v-b460ee3b {
  margin-right: 20rpx;
}
.flow-shopList .flow-list-left image.data-v-b460ee3b {
  width: 180rpx;
  height: 180rpx;
  border: 1rpx solid #eee;
  background: #fff;
}
.flow-shopList .flow-list-right .flow-cont.data-v-b460ee3b {
  font-size: 28rpx;
  color: var(--main-bg);
}
.flow-shopList .flow-list-right .small.data-v-b460ee3b {
  font-size: 26rpx;
  color: #777;
}
.flow-shopList .flow-list-right .flow-list-cont.data-v-b460ee3b {
  padding-top: 10rpx;
}
.flow-all-money.data-v-b460ee3b {
  padding: 0 24rpx;
  color: #444;
}
.flow-all-money .flow-all-list.data-v-b460ee3b {
  font-size: 28rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}
.flow-all-money .flow-all-list.data-v-b460ee3b:last-child {
  border-bottom: none;
}
.flow-all-money .flow-all-list-cont.data-v-b460ee3b {
  font-size: 28rpx;
  padding: 10rpx 0;
}
.flow-all-money .flow-arrow.data-v-b460ee3b {
  justify-content: flex-end;
  align-items: center;
}
.swiper-tab.data-v-b460ee3b {
  width: 100%;
  text-align: center;
  height: 85rpx;
  background-color: #fff;
  border-bottom: 1px solid #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.swiper-tab .swiper-tab-item.data-v-b460ee3b {
  width: 35%;
  height: 100%;
  font-size: 28rpx;
  color: #777;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  border-bottom: 2px solid #ffffff00;
}
.swiper-tab .swiper-tab-item.on.data-v-b460ee3b {
  color: var(--main-bg);
  border-bottom: 2px solid var(--main-bg);
}
.flow-extract-contact.data-v-b460ee3b {
  padding: 8rpx 24rpx;
  font-size: 28rpx;
  color: #444;
  margin-bottom: 25rpx;
}
.flow-extract-contact .contact-item.data-v-b460ee3b {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}
.flow-extract-contact .contact-item.data-v-b460ee3b:last-child {
  border-bottom: none;
}
.flow-extract-contact .item-label.data-v-b460ee3b {
  margin-right: 26rpx;
  width: 150rpx;
}
.flow-extract-contact .item-ipt .input.data-v-b460ee3b {
  font-size: 28rpx;
}
.flow-extract-contact .item-ipt .input .input-placeholder.data-v-b460ee3b {
  font-size: 28rpx;
}
.my-radio.data-v-b460ee3b {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 34rpx;
  height: 34rpx;
  text-align: center;
  border: 1px solid #c8c9cc;
  transition-duration: 0.2s;
  border-radius: 100%;
  color: transparent;
}
.my-radio.checked.data-v-b460ee3b {
  background-color: var(--main-bg);
  border-color: var(--main-bg);
  color: #fff;
}
.my-radio.disabled.data-v-b460ee3b {
  background-color: #ebedf0;
  border-color: #c8c9cc;
}
.my-radio.disabled-checked.data-v-b460ee3b {
  color: #c8c9cc !important;
}