"use strict";
const utils_request_core_request = require("../core/request.js");
const utils_request_upload_utils = require("./utils.js");
const utils_request_core_utils = require("../core/utils.js");
class fileUpload extends utils_request_core_request.request {
  constructor(props) {
    super(props);
  }
  //七牛云上传图片
  async qnImgUpload(options = {}) {
    let files;
    try {
      files = await utils_request_upload_utils.chooseImage(options);
      options.onSelectComplete && options.onSelectComplete(files);
    } catch (err) {
      this.requestError && this.requestError(err);
      return Promise.reject(err);
    }
    if (files) {
      return this.qnFileUpload({
        ...options,
        files
      });
    }
  }
  //七牛云上传视频
  async qnVideoUpload(options = {}) {
    let files;
    try {
      files = await utils_request_upload_utils.chooseVideo(options);
      options.onSelectComplete && options.onSelectComplete(files);
    } catch (err) {
      this.requestError && this.requestError(err);
      return Promise.reject(err);
    }
    if (files) {
      return this.qnFileUpload({
        ...options,
        files
      });
    }
  }
  //七牛云文件上传（支持多张上传）
  async qnFileUpload(options = {}) {
    let requestInfo;
    try {
      requestInfo = {
        ...this.config,
        ...options,
        header: {},
        method: "FILE"
      };
      if (this.requestStart) {
        let requestStart = this.requestStart(requestInfo);
        if (typeof requestStart == "object") {
          let changekeys = ["load", "files"];
          changekeys.forEach((key) => {
            requestInfo[key] = requestStart[key];
          });
        } else {
          throw {
            errMsg: "【request】请求开始拦截器未通过",
            statusCode: 0,
            data: requestInfo.data,
            method: requestInfo.method,
            header: requestInfo.header,
            url: requestInfo.url
          };
        }
      }
      let requestResult = await utils_request_upload_utils.qiniuUpload(requestInfo, this.getQnToken);
      return Promise.resolve(requestResult);
    } catch (err) {
      this.requestError && this.requestError(err);
      return Promise.reject(err);
    } finally {
      this.requestEnd && this.requestEnd(requestInfo);
    }
  }
  //本地服务器图片上传
  async urlImgUpload() {
    let options = {};
    if (arguments[0]) {
      if (typeof arguments[0] == "string") {
        options.url = arguments[0];
      } else if (typeof arguments[0] == "object") {
        options = Object.assign(options, arguments[0]);
      }
    }
    if (arguments[1] && typeof arguments[1] == "object") {
      options = Object.assign(options, arguments[1]);
    }
    try {
      options.files = await utils_request_upload_utils.chooseImage(options);
      options.onSelectComplete && options.onSelectComplete(options.files);
    } catch (err) {
      this.requestError && this.requestError(err);
      return Promise.reject(err);
    }
    if (options.files) {
      return this.urlFileUpload(options);
    }
  }
  //本地服务器上传视频
  async urlVideoUpload() {
    let options = {};
    if (arguments[0]) {
      if (typeof arguments[0] == "string") {
        options.url = arguments[0];
      } else if (typeof arguments[0] == "object") {
        options = Object.assign(options, arguments[0]);
      }
    }
    if (arguments[1] && typeof arguments[1] == "object") {
      options = Object.assign(options, arguments[1]);
    }
    try {
      options.files = await utils_request_upload_utils.chooseVideo(options);
      options.onSelectComplete && options.onSelectComplete(options.files);
    } catch (err) {
      this.requestError && this.requestError(err);
      return Promise.reject(err);
    }
    if (options.files) {
      return this.urlFileUpload(options);
    }
  }
  //本地服务器文件上传方法
  async urlFileUpload() {
    let requestInfo = {
      method: "FILE"
    };
    if (arguments[0]) {
      if (typeof arguments[0] == "string") {
        requestInfo.url = arguments[0];
      } else if (typeof arguments[0] == "object") {
        requestInfo = Object.assign(requestInfo, arguments[0]);
      }
    }
    if (arguments[1] && typeof arguments[1] == "object") {
      requestInfo = Object.assign(requestInfo, arguments[1]);
    }
    if (!requestInfo.url && this.defaultUploadUrl) {
      requestInfo.url = this.defaultUploadUrl;
    }
    let runRequestStart = false;
    try {
      if (!requestInfo.url) {
        throw {
          errMsg: "【request】文件上传缺失数据url",
          statusCode: 0,
          data: requestInfo.data,
          method: requestInfo.method,
          header: requestInfo.header,
          url: requestInfo.url
        };
      }
      requestInfo = utils_request_core_utils.mergeConfig(this, requestInfo);
      runRequestStart = true;
      if (this.requestStart) {
        let requestStart = this.requestStart(requestInfo);
        if (typeof requestStart == "object") {
          let changekeys = ["data", "header", "isPrompt", "load", "isFactory", "files"];
          changekeys.forEach((key) => {
            requestInfo[key] = requestStart[key];
          });
        } else {
          throw {
            errMsg: "【request】请求开始拦截器未通过",
            statusCode: 0,
            data: requestInfo.data,
            method: requestInfo.method,
            header: requestInfo.header,
            url: requestInfo.url
          };
        }
      }
      let requestResult = await utils_request_upload_utils.urlUpload(requestInfo, this.dataFactory);
      return Promise.resolve(requestResult);
    } catch (err) {
      this.requestError && this.requestError(err);
      return Promise.reject(err);
    } finally {
      if (runRequestStart) {
        this.requestEnd && this.requestEnd(requestInfo);
      }
    }
  }
}
exports.fileUpload = fileUpload;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/utils/request/upload/upload.js.map
