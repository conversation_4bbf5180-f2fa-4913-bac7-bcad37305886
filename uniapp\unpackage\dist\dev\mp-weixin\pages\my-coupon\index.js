"use strict";
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const core_app = require("../../core/app.js");
const api_myCoupon = require("../../api/myCoupon.js");
require("../../common/enum/coupon/ApplyRange.js");
require("../../common/enum/coupon/ExpireType.js");
const common_enum_coupon_CouponType = require("../../common/enum/coupon/CouponType.js");
const common_vendor = require("../../common/vendor.js");
const pageSize = 15;
const tabs = [{
  name: `未使用`,
  value: "isUnused"
}, {
  name: `已使用`,
  value: "isUse"
}, {
  name: `已过期`,
  value: "isExpire"
}];
const _sfc_main = {
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin],
  data() {
    return {
      // 枚举类
      CouponTypeEnum: common_enum_coupon_CouponType.CouponTypeEnum,
      // 标签栏数据
      tabs,
      // 当前标签索引
      curTab: 0,
      // 优惠券列表数据
      list: core_app.getEmptyPaginateObj(),
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: { size: pageSize },
        // 数量要大于4条才显示无更多数据
        noMoreSize: 4,
        // 空布局
        empty: {
          tip: "亲，暂无相关优惠券"
        }
      }
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },
  methods: {
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getCouponList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 获取优惠券列表
    getCouponList(pageNo = 1) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_myCoupon.list({ dataType: app.getTabValue(), page: pageNo }, { load: false }).then((result) => {
          const newList = result.data.list;
          app.list.data = core_app.getMoreListData(newList, app.list, pageNo);
          resolve(newList);
        });
      });
    },
    // 评分类型
    getTabValue() {
      return this.tabs[this.curTab].value;
    },
    // 切换标签项
    onChangeTab(index) {
      const app = this;
      app.curTab = index;
      app.onRefreshList();
    },
    // 刷新优惠券列表
    onRefreshList() {
      this.list = core_app.getEmptyPaginateObj();
      setTimeout(() => {
        this.mescroll.resetUpScroll();
      }, 120);
    },
    // 展开优惠券描述
    handleDescribe(index) {
      this.list.data[index].expand = !this.list.data[index].expand;
    }
  }
};
if (!Array) {
  const _easycom_u_tabs2 = common_vendor.resolveComponent("u-tabs");
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  (_easycom_u_tabs2 + _easycom_mescroll_body2)();
}
const _easycom_u_tabs = () => "../../uni_modules/vk-uview-ui/components/u-tabs/u-tabs.js";
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  (_easycom_u_tabs + _easycom_mescroll_body)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o($options.onChangeTab),
    b: common_vendor.o(($event) => $data.curTab = $event),
    c: common_vendor.p({
      list: $data.tabs,
      ["is-scroll"]: false,
      ["active-color"]: _ctx.appTheme.mainBg,
      duration: 0.2,
      modelValue: $data.curTab
    }),
    d: common_vendor.f($data.list.data, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($data.CouponTypeEnum[item.coupon_type].name),
        b: item.coupon_type == $data.CouponTypeEnum.FULL_DISCOUNT.value
      }, item.coupon_type == $data.CouponTypeEnum.FULL_DISCOUNT.value ? {
        c: common_vendor.t(item.reduce_price)
      } : {}, {
        d: item.coupon_type == $data.CouponTypeEnum.DISCOUNT.value
      }, item.coupon_type == $data.CouponTypeEnum.DISCOUNT.value ? {
        e: common_vendor.t(item.discount)
      } : {}, {
        f: common_vendor.t(item.min_price),
        g: common_vendor.t(item.name),
        h: item.expire_type == $data.CouponTypeEnum.FULL_DISCOUNT.value
      }, item.expire_type == $data.CouponTypeEnum.FULL_DISCOUNT.value ? {
        i: common_vendor.t(item.expire_day)
      } : {}, {
        j: item.expire_type == $data.CouponTypeEnum.DISCOUNT.value
      }, item.expire_type == $data.CouponTypeEnum.DISCOUNT.value ? common_vendor.e({
        k: item.start_time === item.end_time
      }, item.start_time === item.end_time ? {
        l: common_vendor.t(item.start_time)
      } : {
        m: common_vendor.t(item.start_time),
        n: common_vendor.t(item.end_time)
      }) : {}, {
        o: item.describe
      }, item.describe ? {
        p: common_vendor.n(item.expand ? "expand" : ""),
        q: common_vendor.o(($event) => $options.handleDescribe(index), index)
      } : {}, {
        r: !item.state.value
      }, !item.state.value ? {
        s: common_vendor.t(item.state.text)
      } : {}, {
        t: common_vendor.n(!item.state.value ? "disable" : ""),
        v: common_vendor.t(item.describe),
        w: common_vendor.n(item.expand ? "expand" : ""),
        x: index
      });
    }),
    e: common_vendor.sr("mescrollRef", "bd4efac3-0"),
    f: common_vendor.o(_ctx.mescrollInit),
    g: common_vendor.o($options.upCallback),
    h: common_vendor.p({
      sticky: true,
      down: {
        use: false
      },
      up: $data.upOption
    }),
    i: common_vendor.s(_ctx.appThemeStyle)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-bd4efac3"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my-coupon/index.js.map
