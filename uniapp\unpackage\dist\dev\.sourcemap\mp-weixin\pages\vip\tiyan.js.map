{"version": 3, "file": "tiyan.js", "sources": ["pages/vip/tiyan.vue", "../../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdmlwL3RpeWFuLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 顶部图片 -->\r\n\t\t<view class=\"top-img1\"></view>\r\n\t\t<view class=\"top-img2\"></view>\r\n\t\t<view class=\"top-img3\" @click=\"receiveCoupon\">\r\n\t\t\t<text class=\"coupon-text\">{{ couponReceived ? '已领取' : '待领取' }}</text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 内容区域 -->\r\n\t\t<view class=\"content\">\r\n\t\t\t<!-- 页面头部 -->\r\n\t\t\t<view class=\"header\">\r\n\t\t\t<text class=\"header-title\">新人体验专区</text>\r\n\t\t\t<text class=\"header-subtitle\">限时优惠，仅限新用户</text>\r\n\t\t</view>\r\n\r\n\r\n\r\n\t\t<!-- 抽奖转盘区域 -->\r\n\t\t<view class=\"lottery-section\">\r\n\t\t\t<view class=\"lottery-title\">\r\n\t\t\t\t<text>幸运转盘</text>\r\n\t\t\t\t<text class=\"lottery-subtitle\">每天3次机会，100%中奖 | 剩余次数：{{ remainingTimes }}/3</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"lottery-container\">\r\n\t\t\t\t<view class=\"lottery-wheel\" :class=\"{ 'no-transition': !isRotating }\" :style=\"{ transform: 'rotate(' + rotateAngle + 'deg)' }\">\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-for=\"(prize, index) in prizes\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\tclass=\"lottery-sector\"\r\n\t\t\t\t\t\t:class=\"'sector-' + index\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<text class=\"prize-text\">{{ prize.name }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"lottery-pointer\">\r\n\t\t\t\t\t<image class=\"pointer-arrow\" src=\"https://xinjiang.zhanyuankj.cn/10001/20250730/ccaf95551f632d9140e4577c3075ac7d.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<button\r\n\t\t\t\tclass=\"lottery-btn\"\r\n\t\t\t\t:class=\"{ 'disabled': remainingTimes <= 0 || isRotating }\"\r\n\t\t\t\t@click=\"startLottery\"\r\n\t\t\t\t:disabled=\"remainingTimes <= 0 || isRotating\"\r\n\t\t\t>\r\n\t\t\t\t{{ isRotating ? '抽奖中...' : '开始抽奖' }}\r\n\t\t\t</button>\r\n\r\n\t\t\t<!-- 跳转按钮 -->\r\n\t\t\t<view class=\"action-buttons\">\r\n\t\t\t\t<button class=\"action-btn\" @click=\"goToRecharge\">点击充值</button>\r\n\t\t\t\t<button class=\"action-btn\" @click=\"goToExchange\">积分兑换</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 中奖结果弹窗 -->\r\n\t\t<u-modal\r\n\t\t\tv-model=\"showResult\"\r\n\t\t\t:content=\"resultContent\"\r\n\t\t\t:show-cancel-button=\"false\"\r\n\t\t\tconfirm-text=\"确定\"\r\n\t\t\t@confirm=\"closeResult\"\r\n\t\t></u-modal>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport * as UserApi from '@/api/user'\r\nimport * as MyCouponApi from '@/api/myCoupon'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 用户信息\r\n\t\t\tuserInfo: {},\r\n\t\t\t// 优惠券状态\r\n\t\t\tcouponReceived: false,\r\n\t\t\t// 抽奖相关\r\n\t\t\trotateAngle: 0,\r\n\t\t\tisRotating: false,\r\n\t\t\tremainingTimes: 3,\r\n\t\t\tshowResult: false,\r\n\t\t\tresultContent: '',\r\n\t\t\t// 奖品配置 - 根据图片实际显示顺序排列\r\n\t\t\tprizes: [\r\n\t\t\t\t{ name: '积分+5', type: 'points', value: 5 },   // 0-45度\r\n\t\t\t\t{ name: '积分+10', type: 'points', value: 10 }, // 45-90度\r\n\t\t\t\t{ name: '积分+15', type: 'points', value: 15 }, // 90-135度\r\n\t\t\t\t{ name: '积分+20', type: 'points', value: 20 }, // 135-180度\r\n\t\t\t\t{ name: '积分+12', type: 'points', value: 12 }, // 180-225度\r\n\t\t\t\t{ name: '积分+8', type: 'points', value: 8 },   // 225-270度\r\n\t\t\t\t{ name: '积分+18', type: 'points', value: 18 }, // 270-315度\r\n\t\t\t\t{ name: '积分+25', type: 'points', value: 25 }  // 315-360度\r\n\t\t\t]\r\n\t\t}\r\n\t},\r\n\r\n\t\tonLoad() {\r\n\t\t\tthis.loadUserData()\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 加载用户数据\r\n\t\t\tloadUserData() {\r\n\t\t\t\t// 获取用户信息\r\n\t\t\t\tUserApi.info().then(result => {\r\n\t\t\t\t\tthis.userInfo = result.data.userInfo\r\n\t\t\t\t})\r\n\r\n\t\t\t\t// 从本地存储获取优惠券状态\r\n\t\t\t\tconst couponStatus = uni.getStorageSync('coupon_received_68_9')\r\n\t\t\t\tthis.couponReceived = !!couponStatus\r\n\r\n\t\t\t\t// 获取今日抽奖次数\r\n\t\t\t\tconst today = new Date().toDateString()\r\n\t\t\t\tconst todayLottery = uni.getStorageSync('lottery_' + today)\r\n\t\t\t\tthis.remainingTimes = todayLottery ? (3 - todayLottery.times) : 3\r\n\t\t\t},\r\n\r\n\t\t\t// 领取优惠券\r\n\t\t\tasync receiveCoupon() {\r\n\t\t\t\tif (this.couponReceived) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '优惠券已领取',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 检查会员等级权限\r\n\t\t\t\tif (!this.checkVipPermission()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '仅限会员领取，请先开通会员',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t})\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/vip/index'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}, 1500)\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tuni.showLoading({ title: '领取中...' })\r\n\r\n\t\t\t\t\t// 调用优惠券领取API，传入优惠券ID 10009\r\n\t\t\t\t\tconst result = await MyCouponApi.receive(10009)\r\n\r\n\t\t\t\t\tif (result.status === 200) {\r\n\t\t\t\t\t\tthis.couponReceived = true\r\n\t\t\t\t\t\tuni.setStorageSync('coupon_received_68_9', true)\r\n\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '优惠券领取成功！',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t// 1.5秒后跳转到商品详情页\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: '/pages/goods/detail?goodsId=10007'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, 1500)\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('优惠券领取失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: error.message || '领取失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 检查VIP权限\r\n\t\t\tcheckVipPermission() {\r\n\t\t\t\t// 检查用户等级权重，普通会员weight=1，不能领取\r\n\t\t\t\tif (!this.userInfo?.grade?.weight || this.userInfo.grade.weight <= 1) {\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\treturn true\r\n\t\t\t},\r\n\r\n\t\t\t// 开始抽奖\r\n\t\t\tstartLottery() {\r\n\t\t\t\t// 检查会员等级权限\r\n\t\t\t\tif (!this.checkVipPermission()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '抽奖仅限会员参与，请先开通会员',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t})\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/vip/index'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}, 1500)\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.remainingTimes <= 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '今日抽奖次数已用完',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.isRotating) return\r\n\r\n\t\t\t\t// 随机选择奖品\r\n\t\t\t\tconst prizeIndex = Math.floor(Math.random() * this.prizes.length)\r\n\t\t\t\tconst prize = this.prizes[prizeIndex]\r\n\r\n\t\t\t\t// 先复原转盘（无动画）\r\n\t\t\t\tthis.isRotating = false\r\n\t\t\t\tthis.rotateAngle = 0\r\n\r\n\t\t\t\t// 延迟开始转动（有动画）\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.isRotating = true\r\n\t\t\t\t\tconst sectorCenterAngle = prizeIndex * 45 + 22.5\r\n\t\t\t\t\tconst targetAngle = 360 * 5 + (360 - sectorCenterAngle)\r\n\t\t\t\t\tthis.rotateAngle = targetAngle\r\n\t\t\t\t}, 100)\r\n\r\n\t\t\t\t// 2秒后显示结果\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.isRotating = false\r\n\t\t\t\t\tthis.showPrizeResult(prize)\r\n\t\t\t\t\tthis.updateLotteryTimes()\r\n\t\t\t\t}, 2000)\r\n\t\t\t},\r\n\r\n\t\t\t// 显示中奖结果\r\n\t\t\tshowPrizeResult(prize) {\r\n\t\t\t\tthis.resultContent = `恭喜您获得：${prize.name}！`\r\n\t\t\t\tthis.showResult = true\r\n\t\t\t},\r\n\r\n\t\t\t// 更新抽奖次数\r\n\t\t\tupdateLotteryTimes() {\r\n\t\t\t\tthis.remainingTimes--\r\n\t\t\t\tconst today = new Date().toDateString()\r\n\t\t\t\tconst todayLottery = uni.getStorageSync('lottery_' + today) || { times: 0 }\r\n\t\t\t\ttodayLottery.times++\r\n\t\t\t\tuni.setStorageSync('lottery_' + today, todayLottery)\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭结果弹窗\r\n\t\t\tcloseResult() {\r\n\t\t\t\tthis.showResult = false\r\n\t\t\t},\r\n\r\n\t\t\t// 跳转到充值页面\r\n\t\t\tgoToRecharge() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/vip/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 跳转到积分兑换页面\r\n\t\t\tgoToExchange() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pagesNew/points/category/index'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.top-img1 {\r\n\twidth: 100%;\r\n\theight: 0;\r\n\tpadding-bottom: 94%; /* 3068/3261 = 94% */\r\n\tbackground-image: url('https://xinjiang.zhanyuankj.cn/10001/20250730/1bddc8d45eb45e4b0f5aa026799c6673.jpg');\r\n\tbackground-size: 100% 100%;\r\n}\r\n\r\n.top-img2 {\r\n\twidth: 100%;\r\n\theight: 0;\r\n\tpadding-bottom: 37%; /* 1207/3266 = 37% */\r\n\tbackground-image: url('https://xinjiang.zhanyuankj.cn/10001/20250730/da39058eb440b6bbc8bda146a5cf240d.jpg');\r\n\tbackground-size: 100% 100%;\r\n}\r\n\r\n.top-img3 {\r\n\twidth: 100%;\r\n\theight: 0;\r\n\tpadding-bottom: 14%;\r\n\tbackground-image: url('https://xinjiang.zhanyuankj.cn/10001/20250730/25ceb82043a1e5c2984c67b958724b42.jpg');\r\n\tbackground-size: 100% 100%;\r\n\tposition: relative;\r\n}\r\n\r\n.coupon-text {\r\n\tposition: absolute;\r\n\tleft: 38%;\r\n\ttop: 36%;\r\n\tcolor: white;\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\ttext-shadow: 2px 2px 4px rgba(0,0,0,0.5);\r\n}\r\n\r\n.content {\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.header {\r\n\ttext-align: center;\r\n\tpadding: 40rpx 0;\r\n\tcolor: white;\r\n\r\n\t.header-title {\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.header-subtitle {\r\n\t\tfont-size: 28rpx;\r\n\t\topacity: 0.9;\r\n\t}\r\n}\r\n\r\n\r\n\r\n.lottery-section {\r\n\tmargin: 50rpx 0;\r\n\ttext-align: center;\r\n}\r\n\r\n.lottery-title {\r\n\tcolor: white;\r\n\tmargin-bottom: 100rpx;\r\n\r\n\ttext {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.lottery-subtitle {\r\n\t\tfont-size: 24rpx;\r\n\t\topacity: 0.9;\r\n\t}\r\n}\r\n\r\n.lottery-container {\r\n\tposition: relative;\r\n\twidth: 500rpx;\r\n\theight: 500rpx;\r\n\tmargin: 0 auto 110rpx auto;\r\n}\r\n\r\n.lottery-btn {\r\n\tbackground: linear-gradient(135deg, #27ae60, #2ecc71);\r\n\tcolor: white;\r\n\tborder: none;\r\n\tborder-radius: 50rpx;\r\n\tpadding: 20rpx 60rpx;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tbox-shadow: 0 8rpx 20rpx rgba(39, 174, 96, 0.3);\r\n\r\n\t&.disabled {\r\n\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\tcolor: rgba(255, 255, 255, 0.5);\r\n\t\tbox-shadow: none;\r\n\t}\r\n}\r\n\r\n.action-buttons {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-top: 40rpx;\r\n\tgap: 30rpx;\r\n}\r\n\r\n.action-btn {\r\n\tflex: 1;\r\n\theight: 80rpx;\r\n\tbackground: rgba(255, 255, 255, 0.1);\r\n\tborder: 1px solid rgba(255, 255, 255, 0.3);\r\n\tcolor: white;\r\n\tborder-radius: 40rpx;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 500;\r\n\tbackdrop-filter: blur(10rpx);\r\n}\r\n\r\n.lottery-wheel {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tborder-radius: 50%;\r\n\tposition: relative;\r\n\ttransition: transform 2s ease-out;\r\n\tbox-shadow: 0 0 30rpx rgba(0, 0, 0, 0.2);\r\n\tbackground: conic-gradient(\r\n\t\t#e74c3c 0deg 45deg,\r\n\t\t#1abc9c 45deg 90deg,\r\n\t\t#3498db 90deg 135deg,\r\n\t\t#27ae60 135deg 180deg,\r\n\t\t#f39c12 180deg 225deg,\r\n\t\t#9b59b6 225deg 270deg,\r\n\t\t#7f8c8d 270deg 315deg,\r\n\t\t#95a5a6 315deg 360deg\r\n\t);\r\n}\r\n\r\n.lottery-wheel.no-transition {\r\n\ttransition: none;\r\n}\r\n\r\n.lottery-sector {\r\n\tposition: absolute;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\r\n\t.prize-text {\r\n\t\tposition: absolute;\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: white;\r\n\t\ttext-shadow: 1rpx 1rpx 2rpx rgba(0,0,0,0.5);\r\n\t\tleft: 50%;\r\n\t\ttop: 50%;\r\n\t}\r\n}\r\n\r\n.sector-0 .prize-text { transform: translate(-50%, -50%) rotate(22.5deg) translateY(-160rpx); }\r\n.sector-1 .prize-text { transform: translate(-50%, -50%) rotate(67.5deg) translateY(-160rpx); }\r\n.sector-2 .prize-text { transform: translate(-50%, -50%) rotate(112.5deg) translateY(-160rpx); }\r\n.sector-3 .prize-text { transform: translate(-50%, -50%) rotate(157.5deg) translateY(-160rpx); }\r\n.sector-4 .prize-text { transform: translate(-50%, -50%) rotate(202.5deg) translateY(-160rpx); }\r\n.sector-5 .prize-text { transform: translate(-50%, -50%) rotate(247.5deg) translateY(-160rpx); }\r\n.sector-6 .prize-text { transform: translate(-50%, -50%) rotate(292.5deg) translateY(-160rpx); }\r\n.sector-7 .prize-text { transform: translate(-50%, -50%) rotate(337.5deg) translateY(-160rpx); }\r\n\r\n.lottery-pointer {\r\n\tposition: absolute;\r\n\ttop: 50%;\r\n\tleft: 50%;\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tbackground: linear-gradient(135deg, #27ae60, #2ecc71);\r\n\tborder: 2rpx solid white;\r\n\tborder-radius: 50%;\r\n\ttransform: translate(-50%, -50%);\r\n\tz-index: 10;\r\n\tbox-shadow: 0 3rpx 9rpx rgba(0, 0, 0, 0.3);\r\n\r\n}\r\n\r\n.pointer-arrow {\r\n\twidth: 80rpx;\r\n\theight: 80rpx;\r\n\tposition: absolute;\r\n\ttop: -50rpx;\r\n\tleft: 50%;\r\n\ttransform: translateX(-50%);\r\n}\r\n</style>", "import MiniProgramPage from 'D:/phpstudy_pro/WWW/ggg/uniapp/pages/vip/tiyan.vue'\nwx.createPage(MiniProgramPage)"], "names": ["UserApi.info", "uni", "MyCouponApi.receive"], "mappings": ";;;;AA0EA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,UAAU,CAAE;AAAA;AAAA,MAEZ,gBAAgB;AAAA;AAAA,MAEhB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA;AAAA,MAEf,QAAQ;AAAA,QACP,EAAE,MAAM,QAAQ,MAAM,UAAU,OAAO,EAAG;AAAA;AAAA,QAC1C,EAAE,MAAM,SAAS,MAAM,UAAU,OAAO,GAAI;AAAA;AAAA,QAC5C,EAAE,MAAM,SAAS,MAAM,UAAU,OAAO,GAAI;AAAA;AAAA,QAC5C,EAAE,MAAM,SAAS,MAAM,UAAU,OAAO,GAAI;AAAA;AAAA,QAC5C,EAAE,MAAM,SAAS,MAAM,UAAU,OAAO,GAAI;AAAA;AAAA,QAC5C,EAAE,MAAM,QAAQ,MAAM,UAAU,OAAO,EAAG;AAAA;AAAA,QAC1C,EAAE,MAAM,SAAS,MAAM,UAAU,OAAO,GAAI;AAAA;AAAA,QAC5C,EAAE,MAAM,SAAS,MAAM,UAAU,OAAO,GAAM;AAAA;AAAA,MAC/C;AAAA,IACD;AAAA,EACA;AAAA,EAEA,SAAS;AACR,SAAK,aAAa;AAAA,EAClB;AAAA,EAED,SAAS;AAAA;AAAA,IAER,eAAe;AAEdA,oBAAc,EAAC,KAAK,YAAU;AAC7B,aAAK,WAAW,OAAO,KAAK;AAAA,OAC5B;AAGD,YAAM,eAAeC,cAAAA,MAAI,eAAe,sBAAsB;AAC9D,WAAK,iBAAiB,CAAC,CAAC;AAGxB,YAAM,SAAQ,oBAAI,KAAM,GAAC,aAAa;AACtC,YAAM,eAAeA,cAAG,MAAC,eAAe,aAAa,KAAK;AAC1D,WAAK,iBAAiB,eAAgB,IAAI,aAAa,QAAS;AAAA,IAChE;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACrB,UAAI,KAAK,gBAAgB;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACV;AACD;AAAA,MACD;AAGA,UAAI,CAAC,KAAK,sBAAsB;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACV;AACD,mBAAW,MAAM;AAChBA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,WACL;AAAA,QACD,GAAE,IAAI;AACP;AAAA,MACD;AAEA,UAAI;AACHA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAGnC,cAAM,SAAS,MAAMC,aAAmB,QAAC,KAAK;AAE9C,YAAI,OAAO,WAAW,KAAK;AAC1B,eAAK,iBAAiB;AACtBD,8BAAI,eAAe,wBAAwB,IAAI;AAE/CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,WACN;AAGD,qBAAW,MAAM;AAChBA,0BAAAA,MAAI,WAAW;AAAA,cACd,KAAK;AAAA,aACL;AAAA,UACD,GAAE,IAAI;AAAA,QACR;AAAA,MACC,SAAO,OAAO;AACfA,sBAAAA,mDAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACN;AAAA,MACF,UAAU;AACTA,sBAAAA,MAAI,YAAY;AAAA,MACjB;AAAA,IACA;AAAA;AAAA,IAGD,qBAAqB;;AAEpB,UAAI,GAAC,gBAAK,aAAL,mBAAe,UAAf,mBAAsB,WAAU,KAAK,SAAS,MAAM,UAAU,GAAG;AACrE,eAAO;AAAA,MACR;AACA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,eAAe;AAEd,UAAI,CAAC,KAAK,sBAAsB;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACV;AACD,mBAAW,MAAM;AAChBA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,WACL;AAAA,QACD,GAAE,IAAI;AACP;AAAA,MACD;AAEA,UAAI,KAAK,kBAAkB,GAAG;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD;AAAA,MACD;AAEA,UAAI,KAAK;AAAY;AAGrB,YAAM,aAAa,KAAK,MAAM,KAAK,WAAW,KAAK,OAAO,MAAM;AAChE,YAAM,QAAQ,KAAK,OAAO,UAAU;AAGpC,WAAK,aAAa;AAClB,WAAK,cAAc;AAGnB,iBAAW,MAAM;AAChB,aAAK,aAAa;AAClB,cAAM,oBAAoB,aAAa,KAAK;AAC5C,cAAM,cAAc,MAAM,KAAK,MAAM;AACrC,aAAK,cAAc;AAAA,MACnB,GAAE,GAAG;AAGN,iBAAW,MAAM;AAChB,aAAK,aAAa;AAClB,aAAK,gBAAgB,KAAK;AAC1B,aAAK,mBAAmB;AAAA,MACxB,GAAE,GAAI;AAAA,IACP;AAAA;AAAA,IAGD,gBAAgB,OAAO;AACtB,WAAK,gBAAgB,SAAS,MAAM,IAAI;AACxC,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,qBAAqB;AACpB,WAAK;AACL,YAAM,SAAQ,oBAAI,KAAM,GAAC,aAAa;AACtC,YAAM,eAAeA,cAAG,MAAC,eAAe,aAAa,KAAK,KAAK,EAAE,OAAO,EAAE;AAC1E,mBAAa;AACbA,oBAAAA,MAAI,eAAe,aAAa,OAAO,YAAY;AAAA,IACnD;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,OACL;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,OACL;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnRD,GAAG,WAAW,eAAe;"}