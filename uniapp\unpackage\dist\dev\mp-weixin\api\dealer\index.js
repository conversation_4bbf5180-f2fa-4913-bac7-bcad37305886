"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  user: "dealer/user",
  center: "dealer/center"
};
const user = (param) => {
  return utils_request_index.$http.get(api.user, param);
};
const center = (param) => {
  return utils_request_index.$http.get(api.center, param);
};
exports.center = center;
exports.user = user;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/dealer/index.js.map
