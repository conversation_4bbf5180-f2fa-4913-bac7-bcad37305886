"use strict";
const common_vendor = require("../../../common/vendor.js");
const core_app = require("../../../core/app.js");
const common_enum_order_DeliveryStatus = require("../../../common/enum/order/DeliveryStatus.js");
const common_enum_order_DeliveryType = require("../../../common/enum/order/DeliveryType.js");
require("../../../common/enum/order/OrderSource.js");
const common_enum_order_OrderStatus = require("../../../common/enum/order/OrderStatus.js");
const common_enum_order_PayStatus = require("../../../common/enum/order/PayStatus.js");
const common_enum_order_ReceiptStatus = require("../../../common/enum/order/ReceiptStatus.js");
require("../../../common/enum/order/OrderType.js");
const api_shop_order = require("../../../api/shop/order.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      // 枚举类
      DeliveryStatusEnum: common_enum_order_DeliveryStatus.DeliveryStatusEnum,
      DeliveryTypeEnum: common_enum_order_DeliveryType.DeliveryTypeEnum,
      OrderStatusEnum: common_enum_order_OrderStatus.OrderStatusEnum,
      PayStatusEnum: common_enum_order_PayStatus.PayStatusEnum,
      ReceiptStatusEnum: common_enum_order_ReceiptStatus.ReceiptStatusEnum,
      // 当前订单ID
      orderId: null,
      // 加载中
      isLoading: true,
      // 当前订单详情
      order: {},
      // 当前设置
      setting: {}
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.onRecordQuery(options);
    this.getOrderDetail();
  },
  methods: {
    // 记录query参数
    onRecordQuery(query) {
      const scene = core_app.getSceneData(query);
      this.orderId = query.orderId ? parseInt(query.orderId) : parseInt(scene.oid);
    },
    // 获取当前订单信息
    getOrderDetail() {
      const app = this;
      app.isLoading = true;
      api_shop_order.detail(app.orderId).then((result) => {
        app.order = result.data.order;
        app.setting = result.data.setting;
        app.isLoading = false;
      });
    },
    // 复制指定内容
    handleCopy(value) {
      const app = this;
      common_vendor.index.setClipboardData({
        data: value,
        success: () => app.$toast("复制成功"),
        fail: ({ errMsg }) => app.$toast("复制失败 " + errMsg)
      });
    },
    // 跳转到门店详情页
    handleTargetExtract(shopId) {
      this.$navTo("pages/shop/detail", { shopId });
    },
    // 跳转到物流跟踪页面
    handleTargetExpress() {
      this.$navTo("pages/order/express/index", { orderId: this.orderId });
    },
    // 跳转到商品详情页面
    handleTargetGoods(goodsId) {
      this.$navTo("pages/goods/detail", { goodsId });
    },
    // 确认核销订单
    onConfirmExtract() {
      const app = this;
      common_vendor.index.showModal({
        title: "友情提示",
        content: "确认要核销该订单吗？请确认买家已取到货~",
        success(o) {
          if (o.confirm) {
            api_shop_order.extract(app.orderId).then((result) => {
              app.$success(result.message);
              setTimeout(() => {
                app.getOrderDetail();
              }, 1500);
            });
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    b: $data.order.order_status == $data.OrderStatusEnum.NORMAL.value
  }, $data.order.order_status == $data.OrderStatusEnum.NORMAL.value ? common_vendor.e({
    c: $data.order.pay_status == $data.PayStatusEnum.PENDING.value
  }, $data.order.pay_status == $data.PayStatusEnum.PENDING.value ? {
    d: common_assets._imports_0$2
  } : $data.order.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value ? {
    f: common_assets._imports_1
  } : $data.order.receipt_status == $data.ReceiptStatusEnum.NOT_RECEIVED.value ? {
    h: common_assets._imports_2
  } : {}, {
    e: $data.order.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value,
    g: $data.order.receipt_status == $data.ReceiptStatusEnum.NOT_RECEIVED.value
  }) : {}, {
    i: $data.order.order_status == $data.OrderStatusEnum.COMPLETED.value
  }, $data.order.order_status == $data.OrderStatusEnum.COMPLETED.value ? {
    j: common_assets._imports_3
  } : {}, {
    k: $data.order.order_status == $data.OrderStatusEnum.CANCELLED.value || $data.order.order_status == $data.OrderStatusEnum.APPLY_CANCEL.value
  }, $data.order.order_status == $data.OrderStatusEnum.CANCELLED.value || $data.order.order_status == $data.OrderStatusEnum.APPLY_CANCEL.value ? {
    l: common_assets._imports_4
  } : {}, {
    m: common_vendor.t($data.order.state_text),
    n: $data.order.delivery_type == $data.DeliveryTypeEnum.EXPRESS.value
  }, $data.order.delivery_type == $data.DeliveryTypeEnum.EXPRESS.value ? {
    o: common_vendor.t($data.order.address.name),
    p: common_vendor.t($data.order.address.phone),
    q: common_vendor.f($data.order.address.region, (region, idx, i0) => {
      return {
        a: common_vendor.t(region),
        b: idx
      };
    }),
    r: common_vendor.t($data.order.address.detail)
  } : {}, {
    s: $data.order.delivery_type == $data.DeliveryTypeEnum.EXTRACT.value
  }, $data.order.delivery_type == $data.DeliveryTypeEnum.EXTRACT.value ? {
    t: common_vendor.t($data.order.extract_shop.shop_name),
    v: common_vendor.t($data.order.extract_shop.region.province),
    w: common_vendor.t($data.order.extract_shop.region.city),
    x: common_vendor.t($data.order.extract_shop.region.region),
    y: common_vendor.t($data.order.extract_shop.address),
    z: common_vendor.o(($event) => $options.handleTargetExtract($data.order.extract_shop.shop_id))
  } : {}, {
    A: $data.order.delivery_type == $data.DeliveryTypeEnum.EXPRESS.value && $data.order.delivery_status == $data.DeliveryStatusEnum.DELIVERED.value && $data.order.express
  }, $data.order.delivery_type == $data.DeliveryTypeEnum.EXPRESS.value && $data.order.delivery_status == $data.DeliveryStatusEnum.DELIVERED.value && $data.order.express ? {
    B: common_vendor.t($data.order.express.express_name),
    C: common_vendor.t($data.order.express_no),
    D: common_vendor.o(($event) => $options.handleCopy($data.order.express_no)),
    E: common_vendor.o(($event) => $options.handleTargetExpress())
  } : {}, {
    F: common_vendor.f($data.order.goods, (goods, idx, i0) => {
      return common_vendor.e({
        a: goods.goods_image,
        b: common_vendor.t(goods.goods_name),
        c: common_vendor.f(goods.goods_props, (props, idx2, i1) => {
          return {
            a: common_vendor.t(props.value.name),
            b: idx2
          };
        }),
        d: common_vendor.t(goods.goods_price),
        e: common_vendor.t(goods.total_num),
        f: common_vendor.o(($event) => $options.handleTargetGoods(goods.goods_id), idx),
        g: goods.refund
      }, goods.refund ? {} : {}, {
        h: idx
      });
    }),
    G: common_vendor.t($data.order.order_no),
    H: common_vendor.o(($event) => $options.handleCopy($data.order.order_no)),
    I: common_vendor.t($data.order.create_time),
    J: common_vendor.t($data.order.buyer_remark ? $data.order.buyer_remark : "--"),
    K: common_vendor.t($data.order.total_price),
    L: $data.order.coupon_money > 0
  }, $data.order.coupon_money > 0 ? {
    M: common_vendor.t($data.order.coupon_money)
  } : {}, {
    N: $data.order.points_money > 0
  }, $data.order.points_money > 0 ? {
    O: common_vendor.t($data.setting.points_name),
    P: common_vendor.t($data.order.points_money)
  } : {}, {
    Q: common_vendor.t($data.order.express_price),
    R: $data.order.update_price.value != "0.00"
  }, $data.order.update_price.value != "0.00" ? {
    S: common_vendor.t($data.order.update_price.symbol),
    T: common_vendor.t($data.order.update_price.value)
  } : {}, {
    U: common_vendor.t($data.order.pay_price),
    V: $data.order.order_status != $data.OrderStatusEnum.CANCELLED.value
  }, $data.order.order_status != $data.OrderStatusEnum.CANCELLED.value ? common_vendor.e({
    W: $data.order.order_status == $data.OrderStatusEnum.APPLY_CANCEL.value
  }, $data.order.order_status == $data.OrderStatusEnum.APPLY_CANCEL.value ? {} : $data.order.pay_status == $data.PayStatusEnum.SUCCESS.value && $data.order.delivery_type == $data.DeliveryTypeEnum.EXTRACT.value && $data.order.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value ? {
    Y: common_vendor.o(($event) => $options.onConfirmExtract())
  } : {}, {
    X: $data.order.pay_status == $data.PayStatusEnum.SUCCESS.value && $data.order.delivery_type == $data.DeliveryTypeEnum.EXTRACT.value && $data.order.delivery_status == $data.DeliveryStatusEnum.NOT_DELIVERED.value
  }) : {}, {
    Z: common_vendor.s(_ctx.appThemeStyle)
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fb1de822"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/order/extract/check.js.map
