"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  orderInfo: "cashier/orderInfo",
  orderPay: "cashier/orderPay",
  tradeQuery: "cashier/tradeQuery"
};
function orderInfo(orderId, param) {
  return utils_request_index.$http.get(api.orderInfo, { orderId, ...param });
}
function orderPay(orderId, data) {
  return utils_request_index.$http.post(api.orderPay, { orderId, ...data });
}
function tradeQuery(param) {
  return utils_request_index.$http.get(api.tradeQuery, param);
}
exports.orderInfo = orderInfo;
exports.orderPay = orderPay;
exports.tradeQuery = tradeQuery;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/cashier/index.js.map
