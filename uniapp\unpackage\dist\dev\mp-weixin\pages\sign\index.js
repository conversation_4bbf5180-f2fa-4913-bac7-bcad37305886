"use strict";
const common_vendor = require("../../common/vendor.js");
const api_xj_sign = require("../../api/xj/sign.js");
if (!Array) {
  const _easycom_cy_qiandao2 = common_vendor.resolveComponent("cy-qiandao");
  _easycom_cy_qiandao2();
}
const _easycom_cy_qiandao = () => "../../uni_modules/cy-qiandao/components/cy-qiandao/cy-qiandao.js";
if (!Math) {
  _easycom_cy_qiandao();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const qiandaolist = common_vendor.ref([]);
    const qiandaodays = common_vendor.ref(0);
    const lianxuqiandaodays = common_vendor.ref(0);
    const isLoading = common_vendor.ref(false);
    common_vendor.onLoad(() => {
      getqiandao();
    });
    function getqiandao() {
      api_xj_sign.list().then((result) => {
        qiandaolist.value = result.data.list;
        qiandaodays.value = qiandaolist.value.length;
        isLoading.value = true;
        iscontinuous();
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/sign/index.vue:39", "获取签到数据失败:", error);
        isLoading.value = true;
      });
    }
    function iscontinuous() {
      if (!qiandaolist.value || qiandaolist.value.length === 0) {
        lianxuqiandaodays.value = 0;
        return;
      }
      let dates = qiandaolist.value.map((dateStr) => {
        const parts = dateStr.split("-");
        if (parts.length === 3) {
          const year = parts[0];
          const month = parts[1].padStart(2, "0");
          const day = parts[2].padStart(2, "0");
          return (/* @__PURE__ */ new Date(`${year}/${month}/${day}`)).getTime();
        }
        return new Date(dateStr.replace(/-/g, "/")).getTime();
      }).filter((time) => !isNaN(time)).sort((a, b) => b - a);
      if (dates.length === 0) {
        lianxuqiandaodays.value = 0;
        return;
      }
      let continuousDays = 1;
      const oneDayMs = 24 * 60 * 60 * 1e3;
      for (let i = 0; i < dates.length - 1; i++) {
        const dayDiff = Math.round((dates[i] - dates[i + 1]) / oneDayMs);
        if (dayDiff === 1) {
          continuousDays++;
        } else if (dayDiff > 1) {
          break;
        }
      }
      lianxuqiandaodays.value = continuousDays;
    }
    function qiandao(e) {
      qiandaolist.value = e.qiandaoarr;
      qiandaodays.value = e.qiandaoarr.length;
      iscontinuous();
      api_xj_sign.addView().then(() => {
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/sign/index.vue:100", "签到记录失败:", error);
      });
    }
    function clickday(e) {
      common_vendor.index.__f__("log", "at pages/sign/index.vue:105", e);
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isLoading.value
      }, isLoading.value ? {
        b: common_vendor.o(qiandao),
        c: common_vendor.o(clickday),
        d: common_vendor.p({
          qiandaoinfo: qiandaolist.value
        })
      } : {}, {
        e: common_vendor.t(qiandaodays.value),
        f: common_vendor.t(lianxuqiandaodays.value)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8b70e6ba"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/sign/index.js.map
