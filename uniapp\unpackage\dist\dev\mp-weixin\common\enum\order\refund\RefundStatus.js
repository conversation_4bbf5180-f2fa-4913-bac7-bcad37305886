"use strict";
const common_enum_enum = require("../../enum.js");
const RefundStatusEnum = new common_enum_enum.Enum([
  { key: "NORMAL", name: "进行中", value: 0 },
  { key: "REJECTED", name: "已拒绝", value: 10 },
  { key: "COMPLETED", name: "已完成", value: 20 },
  { key: "CANCELLED", name: "已取消", value: 30 }
]);
exports.RefundStatusEnum = RefundStatusEnum;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/common/enum/order/refund/RefundStatus.js.map
