/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.diy-goodsGroup.data-v-c6fc819d {
  overflow: hidden;
}
.diy-goodsGroup .tabs.data-v-c6fc819d {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 16rpx;
  height: 106rpx;
}
.diy-goodsGroup .tabs .tab-item.data-v-c6fc819d {
  position: relative;
  color: #000;
  padding-bottom: 10rpx;
}
.diy-goodsGroup .tabs .tab-item.active .sub-name.data-v-c6fc819d {
  background-color: var(--tab-active-bg-color);
  color: var(--tab-active-font-color);
}
.diy-goodsGroup .tabs .tab-item .tab-name.data-v-c6fc819d {
  font-size: 28rpx;
  margin-bottom: 4rpx;
}
.diy-goodsGroup .tabs .tab-item .sub-name.data-v-c6fc819d {
  font-size: 22rpx;
  padding: 2rpx 10rpx;
  border-radius: 16rpx;
  color: #000;
  text-align: center;
}
.diy-goodsGroup .goods-list.data-v-c6fc819d {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -20rpx !important;
}
.diy-goodsGroup .goods-item.data-v-c6fc819d {
  flex: 0 1 48.6%;
  margin-right: 2.8%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}
.diy-goodsGroup .goods-item.display-card.data-v-c6fc819d {
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.07);
}
.diy-goodsGroup .goods-item.data-v-c6fc819d:nth-child(2n) {
  margin-right: 0 !important;
}
.diy-goodsGroup .goods-item .goods-image.data-v-c6fc819d {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  overflow: hidden;
}
.diy-goodsGroup .goods-item .goods-image.data-v-c6fc819d:after {
  content: "";
  display: block;
  margin-top: 100%;
}
.diy-goodsGroup .goods-item .goods-image .image.data-v-c6fc819d {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.diy-goodsGroup .goods-item .goods-info.data-v-c6fc819d {
  padding: 20rpx;
}
.diy-goodsGroup .goods-item .goods-name.data-v-c6fc819d {
  font-size: 26rpx;
  color: #000;
  margin-bottom: 8rpx;
  line-height: 1.3;
}
.diy-goodsGroup .goods-item .goods-name.row-two.data-v-c6fc819d {
  min-height: 68rpx;
}
.diy-goodsGroup .goods-item .goods-selling.data-v-c6fc819d {
  display: flex;
  font-size: 24rpx;
  margin-bottom: 8rpx;
  height: 38rpx;
}
.diy-goodsGroup .goods-item .goods-sales.data-v-c6fc819d {
  font-size: 24rpx;
  color: #959595;
  margin-bottom: 8rpx;
}
.diy-goodsGroup .goods-item .goods-sales .line.data-v-c6fc819d {
  margin: 0 8rpx;
}
.diy-goodsGroup .goods-item .footer.data-v-c6fc819d {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.diy-goodsGroup .goods-item .footer .goods-price.data-v-c6fc819d {
  position: relative;
  color: #ff1051;
}
.diy-goodsGroup .goods-item .footer .goods-price .unit.data-v-c6fc819d {
  font-size: 20rpx;
}
.diy-goodsGroup .goods-item .footer .goods-price .value.data-v-c6fc819d {
  font-size: 30rpx;
}
.diy-goodsGroup .goods-item .footer .goods-price .unit2.data-v-c6fc819d {
  margin-left: 4rpx;
  font-size: 22rpx;
}
.diy-goodsGroup .goods-item .footer .goods-price .line-price.data-v-c6fc819d {
  margin-left: 6rpx;
  color: #959595;
}
.diy-goodsGroup .goods-item .footer .goods-price .line-price .unit.data-v-c6fc819d {
  text-decoration: line-through;
  font-size: 22rpx;
}
.diy-goodsGroup .goods-item .footer .goods-price .line-price .value.data-v-c6fc819d {
  text-decoration: line-through;
  font-size: 22rpx;
}
.diy-goodsGroup .goods-item .footer .action .btn-cart.data-v-c6fc819d {
  text-align: center;
}
.diy-goodsGroup .goods-item .footer .action .btn-cart .cart-icon.data-v-c6fc819d {
  font-size: 36rpx;
}