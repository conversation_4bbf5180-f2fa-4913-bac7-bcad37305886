
page {
    background: #fff;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.c-violet.data-v-2f63016d {
  color: #786cff;
}
.c-80.data-v-2f63016d {
  color: #808080;
}
.widget-list.data-v-2f63016d {
  padding: 10rpx 20rpx 40rpx 20rpx;
  box-sizing: border-box;
}
.widget__detail.data-v-2f63016d {
  padding: 20rpx 15rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  border-bottom: 1rpx solid #e7e7e7;
}
.widget__detail .user-avatar.data-v-2f63016d {
  margin-right: 16rpx;
}
.widget__detail .user-info.data-v-2f63016d {
  height: 100%;
}
.widget__detail .user-info .user-nickName.data-v-2f63016d {
  margin-bottom: 8rpx;
}
.widget__detail .detail__money.data-v-2f63016d {
  width: 100%;
  margin-bottom: 8rpx;
}