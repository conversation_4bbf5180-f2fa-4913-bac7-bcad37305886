"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const utils_util = require("../../../utils/util.js");
const _sfc_main = {
  props: {
    // 是否存在第三方用户信息
    isParty: {
      type: Boolean,
      default: () => true
    },
    // 第三方用户信息数据
    partyData: {
      type: Object
    }
  },
  data() {
    return {
      // 微信小程序登录凭证 (code)
      // 提交到后端，用于换取openid
      code: ""
    };
  },
  methods: {
    // 按钮点击事件: 获取微信手机号按钮
    // 实现目的: 在getphonenumber事件触发之前获取微信登录code
    // 因为如果在getphonenumber事件中获取code的话,提交到后端的encryptedData会存在解密不了的情况
    async clickPhoneNumber() {
      this.code = await this.getCode();
    },
    // 微信授权获取手机号一键登录
    // getphonenumber事件的回调方法
    async handelMpWeixinMobileLogin({
      detail
    }) {
      const app = this;
      if (detail.errMsg != "getPhoneNumber:ok") {
        common_vendor.index.__f__("log", "at pages/login/components/mp-weixin-mobile.vue:58", "微信授权获取手机号失败", detail);
        return;
      }
      if (detail.errMsg == "getPhoneNumber:ok") {
        app.isLoading = true;
        store_index.store.dispatch("LoginMpWxMobile", {
          code: app.code,
          encryptedData: detail.encryptedData,
          iv: detail.iv,
          isParty: true,
          partyData: {
            code: await app.getCode(),
            oauth: "MP-WEIXIN"
          },
          refereeId: store_index.store.getters.refereeId
        }).then((result) => {
          app.$toast(result.message);
          common_vendor.index.$emit("syncRefresh", true);
          setTimeout(() => app.onNavigateBack(1), 2e3);
        }).catch((err) => {
          const resultData = err.result.data;
          if (utils_util.isEmpty(resultData)) {
            app.$toast(err.result.message);
          }
          if (resultData.isBack) {
            setTimeout(() => app.onNavigateBack(1), 2e3);
          }
        }).finally(() => app.isLoading = false);
      }
    },
    // 获取微信登录的code
    // https://developers.weixin.qq.com/miniprogram/dev/api/open-api/login/wx.login.html
    getCode() {
      return new Promise((resolve, reject) => {
        common_vendor.index.login({
          provider: "weixin",
          success: (res) => {
            common_vendor.index.__f__("log", "at pages/login/components/mp-weixin-mobile.vue:104", "code", res.code);
            resolve(res.code);
          },
          fail: reject
        });
      });
    },
    /**
     * 登录成功-跳转回原页面
     */
    onNavigateBack(delta = 1) {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        common_vendor.index.navigateBack({
          delta: Number(delta || 1)
        });
      } else {
        this.$navTo("pages/index/index");
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o(($event) => $options.handelMpWeixinMobileLogin($event)),
    b: common_vendor.o((...args) => $options.clickPhoneNumber && $options.clickPhoneNumber(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-198e5798"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/login/components/mp-weixin-mobile.js.map
