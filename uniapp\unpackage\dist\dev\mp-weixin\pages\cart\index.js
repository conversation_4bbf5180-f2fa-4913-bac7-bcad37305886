"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_util = require("../../utils/util.js");
const core_app = require("../../core/app.js");
const api_cart = require("../../api/cart.js");
const Empty = () => "../../components/empty/index.js";
const Recommended = () => "../../components/recommended/index.js";
const PromotePopup = () => "../../components/promote-popup/index.js";
const CartIdsIndex = "CartIds";
const _sfc_main = {
  components: {
    Empty,
    Recommended,
    PromotePopup
  },
  data() {
    return {
      inArray: utils_util.inArray,
      // 正在加载
      isLoading: true,
      // 当前模式: normal正常 edit编辑
      mode: "normal",
      // 购物车商品列表
      list: [],
      // 购物车商品总数量
      total: null,
      // 选中的商品ID记录
      checkedIds: [],
      // 选中的商品总金额
      totalPrice: "0.00"
    };
  },
  watch: {
    // 监听选中的商品
    checkedIds: {
      handler(val) {
        this.onCalcTotalPrice();
        common_vendor.index.setStorageSync(CartIdsIndex, val);
      },
      deep: true,
      immediate: false
    },
    // 监听购物车商品总数量
    total(val) {
      core_app.setCartTotalNum(val);
      core_app.setCartTabBadge();
    }
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkedIds = common_vendor.index.getStorageSync(CartIdsIndex);
    core_app.checkLogin() ? this.getCartList() : this.isLoading = false;
  },
  methods: {
    // 计算合计金额 (根据选中的商品)
    onCalcTotalPrice() {
      const app = this;
      const checkedList = app.list.filter((item) => utils_util.inArray(item.id, app.checkedIds));
      let tempPrice = 0;
      checkedList.forEach((item) => {
        const unitPrice = item.goods.skuInfo.goods_price * 100;
        tempPrice += unitPrice * item.goods_num;
      });
      app.totalPrice = (tempPrice / 100).toFixed(2);
    },
    // 获取购物车商品列表
    getCartList() {
      const app = this;
      app.isLoading = true;
      api_cart.list().then((result) => {
        app.list = result.data.list;
        app.total = result.data.cartTotal;
        app.onClearInvalidId();
      }).finally(() => app.isLoading = false);
    },
    // 清除checkedIds中无效的ID
    onClearInvalidId() {
      const app = this;
      const listIds = app.list.map((item) => item.id);
      app.checkedIds = utils_util.arrayIntersect(listIds, app.checkedIds);
    },
    // 切换当前模式
    handleToggleMode() {
      this.mode = this.mode == "normal" ? "edit" : "normal";
    },
    // 监听步进器更改事件
    onChangeStepper({ value }, item) {
      if (item.goods_num == value)
        return;
      if (!item.debounceHandle) {
        item.oldValue = item.goods_num;
        item.debounceHandle = utils_util.debounce(this.onUpdateCartNum, 500);
      }
      item.goods_num = value;
      item.debounceHandle(item, item.oldValue, value);
    },
    // 提交更新购物车数量
    onUpdateCartNum(item, oldValue, newValue) {
      const app = this;
      api_cart.update(item.goods_id, item.goods_sku_id, newValue).then((result) => {
        app.total = result.data.cartTotal;
        app.onCalcTotalPrice();
        item.debounceHandle = null;
      }).catch((err) => {
        item.goods_num = oldValue;
        setTimeout(() => app.$toast(err.errMsg), 10);
      });
    },
    // 跳转到商品详情页
    onTargetGoods(goodsId) {
      this.$navTo("pages/goods/detail", { goodsId });
    },
    // 点击去逛逛按钮, 跳转到首页
    onTargetIndex() {
      this.$navTo("pages/index/index");
    },
    // 选中商品
    handleCheckItem(cartId) {
      const { checkedIds } = this;
      const index = checkedIds.findIndex((id) => id === cartId);
      index < 0 ? checkedIds.push(cartId) : checkedIds.splice(index, 1);
    },
    // 全选事件
    handleCheckAll() {
      const { checkedIds, list } = this;
      this.checkedIds = checkedIds.length === list.length ? [] : list.map((item) => item.id);
    },
    // 结算选中的商品
    handleOrder() {
      const app = this;
      if (app.checkedIds.length) {
        const cartIds = app.checkedIds.join();
        app.$navTo("pages/checkout/index", { mode: "cart", cartIds });
      }
    },
    // 删除选中的商品弹窗事件
    handleDelete() {
      const app = this;
      if (!app.checkedIds.length) {
        return false;
      }
      common_vendor.index.showModal({
        title: "友情提示",
        content: "您确定要删除该商品吗？",
        showCancel: true,
        success({ confirm }) {
          confirm && app.onClearCart();
        }
      });
    },
    // 确认删除商品
    onClearCart() {
      const app = this;
      api_cart.clear(app.checkedIds).then((result) => {
        app.getCartList();
        app.handleToggleMode();
      });
    }
  }
};
if (!Array) {
  const _easycom_u_checkbox2 = common_vendor.resolveComponent("u-checkbox");
  const _easycom_u_number_box2 = common_vendor.resolveComponent("u-number-box");
  const _component_empty = common_vendor.resolveComponent("empty");
  const _component_recommended = common_vendor.resolveComponent("recommended");
  const _component_PromotePopup = common_vendor.resolveComponent("PromotePopup");
  (_easycom_u_checkbox2 + _easycom_u_number_box2 + _component_empty + _component_recommended + _component_PromotePopup)();
}
const _easycom_u_checkbox = () => "../../uni_modules/vk-uview-ui/components/u-checkbox/u-checkbox.js";
const _easycom_u_number_box = () => "../../uni_modules/vk-uview-ui/components/u-number-box/u-number-box.js";
if (!Math) {
  (_easycom_u_checkbox + _easycom_u_number_box)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.list.length
  }, $data.list.length ? common_vendor.e({
    b: common_vendor.t($data.total),
    c: $data.mode == "normal"
  }, $data.mode == "normal" ? {} : {}, {
    d: $data.mode == "edit"
  }, $data.mode == "edit" ? {} : {}, {
    e: common_vendor.o(($event) => $options.handleToggleMode())
  }) : {}, {
    f: $data.list.length
  }, $data.list.length ? {
    g: common_vendor.f($data.list, (item, index, i0) => {
      return {
        a: "8039fbf1-0-" + i0,
        b: common_vendor.p({
          modelValue: $data.inArray(item.id, $data.checkedIds),
          shape: "circle",
          activeColor: _ctx.appTheme.mainBg
        }),
        c: common_vendor.o(($event) => $options.handleCheckItem(item.id), index),
        d: item.goods.goods_image,
        e: common_vendor.o(($event) => $options.onTargetGoods(item.goods_id), index),
        f: common_vendor.t(item.goods.goods_name),
        g: common_vendor.o(($event) => $options.onTargetGoods(item.goods_id), index),
        h: common_vendor.f(item.goods.skuInfo.goods_props, (props, idx, i1) => {
          return {
            a: common_vendor.t(props.value.name),
            b: idx
          };
        }),
        i: common_vendor.t(item.goods.skuInfo.goods_price),
        j: common_vendor.o(($event) => $options.onChangeStepper($event, item), index),
        k: "8039fbf1-1-" + i0,
        l: common_vendor.p({
          min: 1,
          modelValue: item.goods_num,
          step: 1
        }),
        m: index
      };
    })
  } : {}, {
    h: !$data.list.length
  }, !$data.list.length ? {
    i: common_vendor.o(($event) => $options.onTargetIndex()),
    j: common_vendor.p({
      isLoading: $data.isLoading,
      ["custom-style"]: {
        padding: "180rpx 50rpx"
      },
      tips: "您的购物车是空的, 快去逛逛吧"
    })
  } : {}, {
    k: $data.list.length
  }, $data.list.length ? common_vendor.e({
    l: common_vendor.o(($event) => $options.handleCheckAll()),
    m: common_vendor.p({
      modelValue: $data.checkedIds.length > 0 && $data.checkedIds.length === $data.list.length,
      shape: "circle",
      activeColor: _ctx.appTheme.mainBg
    }),
    n: common_vendor.t($data.totalPrice),
    o: $data.mode == "normal"
  }, $data.mode == "normal" ? {
    p: common_vendor.t($data.checkedIds.length > 0 ? `(${$data.total})` : ""),
    q: $data.checkedIds.join() == "" ? 1 : "",
    r: common_vendor.o(($event) => $options.handleOrder())
  } : {}, {
    s: $data.mode == "edit"
  }, $data.mode == "edit" ? {
    t: !$data.checkedIds.length ? 1 : "",
    v: common_vendor.o(($event) => $options.handleDelete())
  } : {}) : {}, {
    w: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8039fbf1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/cart/index.js.map
