"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "dealer.orderVip/list",
  submit: "dealer.orderVip/submit"
};
const submit = (data) => {
  return utils_request_index.$http.post(api.submit, data);
};
const list = (param) => {
  return utils_request_index.$http.get(api.list, param);
};
exports.list = list;
exports.submit = submit;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/dealer/orderVip.js.map
