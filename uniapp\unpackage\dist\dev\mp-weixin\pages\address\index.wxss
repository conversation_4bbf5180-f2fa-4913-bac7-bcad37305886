/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.addres-list.data-v-c47feaaa {
  padding-top: 20rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 140rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 140rpx);
}
.address-item.data-v-c47feaaa {
  margin: 0 auto 20rpx auto;
  padding: 30rpx 40rpx;
  width: 94%;
  box-shadow: 0 1rpx 5rpx 0 rgba(0, 0, 0, 0.05);
  border-radius: 16rpx;
  background: #fff;
}
.contacts.data-v-c47feaaa {
  font-size: 30rpx;
  margin-bottom: 16rpx;
}
.contacts .name.data-v-c47feaaa {
  margin-right: 16rpx;
}
.address.data-v-c47feaaa {
  font-size: 28rpx;
}
.address .region.data-v-c47feaaa {
  margin-right: 10rpx;
}
.line.data-v-c47feaaa {
  margin: 20rpx 0;
  border-bottom: 1rpx solid #f3f3f3;
}
.item-option.data-v-c47feaaa {
  display: flex;
  justify-content: space-between;
  height: 48rpx;
}
.item-option .item-radio.data-v-c47feaaa {
  font-size: 28rpx;
}
.item-option .item-radio .radio.data-v-c47feaaa {
  vertical-align: middle;
  transform: scale(0.76);
}
.item-option .item-radio .text.data-v-c47feaaa {
  vertical-align: middle;
}
.item-option .events.data-v-c47feaaa {
  display: flex;
  align-items: center;
  line-height: 48rpx;
}
.item-option .events .event-item.data-v-c47feaaa {
  font-size: 28rpx;
  margin-right: 26rpx;
  color: #4c4c4c;
}
.item-option .events .event-item.data-v-c47feaaa:last-child {
  margin-right: 0;
}
.item-option .events .event-item .title.data-v-c47feaaa {
  margin-left: 8rpx;
}
.footer-fixed.data-v-c47feaaa {
  position: fixed;
  bottom: var(--window-bottom);
  left: 0;
  right: 0;
  z-index: 11;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.footer-fixed .btn-wrapper.data-v-c47feaaa {
  height: 120rpx;
  padding: 0 40rpx;
}
.footer-fixed .btn-item.data-v-c47feaaa {
  flex: 1;
  font-size: 28rpx;
  height: 86rpx;
  color: #fff;
  border-radius: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 1rpx 5rpx 0 rgba(0, 0, 0, 0.05);
}
.footer-fixed .btn-item-main.data-v-c47feaaa {
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
}