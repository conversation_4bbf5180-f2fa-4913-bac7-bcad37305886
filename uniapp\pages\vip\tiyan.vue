<template>
	<view class="container">
		<!-- 顶部图片 -->
		<view class="top-img1"></view>
		<view class="top-img2"></view>
		<view class="top-img3" @click="receiveCoupon">
			<text class="coupon-text">{{ couponReceived ? '已领取' : '待领取' }}</text>
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<!-- 页面头部 -->
			<view class="header">
			<text class="header-title">新人体验专区</text>
			<text class="header-subtitle">限时优惠，仅限新用户</text>
		</view>



		<!-- 抽奖转盘区域 -->
		<view class="lottery-section">
			<view class="lottery-title">
				<text>幸运转盘</text>
				<text class="lottery-subtitle">每天3次机会，100%中奖 | 剩余次数：{{ remainingTimes }}/3</text>
			</view>

			<view class="lottery-container">
				<view class="lottery-wheel" :class="{ 'no-transition': !isRotating }" :style="{ transform: 'rotate(' + rotateAngle + 'deg)' }">
					<view
						v-for="(prize, index) in prizes"
						:key="index"
						class="lottery-sector"
						:class="'sector-' + index"
					>
						<text class="prize-text">{{ prize.name }}</text>
					</view>
				</view>
				<view class="lottery-pointer">
					<image class="pointer-arrow" src="https://xinjiang.zhanyuankj.cn/10001/20250730/ccaf95551f632d9140e4577c3075ac7d.png" mode="aspectFit"></image>
				</view>
			</view>

			<button
				class="lottery-btn"
				:class="{ 'disabled': remainingTimes <= 0 || isRotating }"
				@click="startLottery"
				:disabled="remainingTimes <= 0 || isRotating"
			>
				{{ isRotating ? '抽奖中...' : '开始抽奖' }}
			</button>

			<!-- 跳转按钮 -->
			<view class="action-buttons">
				<button class="action-btn" @click="goToRecharge">点击充值</button>
				<button class="action-btn" @click="goToExchange">积分兑换</button>
			</view>
		</view>

		<!-- 中奖结果弹窗 -->
		<u-modal
			v-model="showResult"
			:content="resultContent"
			:show-cancel-button="false"
			confirm-text="确定"
			@confirm="closeResult"
		></u-modal>
		</view>
	</view>
</template>

<script>
import * as UserApi from '@/api/user'
import * as MyCouponApi from '@/api/myCoupon'

export default {
	data() {
		return {
			// 用户信息
			userInfo: {},
			// 优惠券状态
			couponReceived: false,
			// 抽奖相关
			rotateAngle: 0,
			isRotating: false,
			remainingTimes: 3,
			showResult: false,
			resultContent: '',
			// 奖品配置 - 根据图片实际显示顺序排列
			prizes: [
				{ name: '积分+5', type: 'points', value: 5 },   // 0-45度
				{ name: '积分+10', type: 'points', value: 10 }, // 45-90度
				{ name: '积分+15', type: 'points', value: 15 }, // 90-135度
				{ name: '积分+20', type: 'points', value: 20 }, // 135-180度
				{ name: '积分+12', type: 'points', value: 12 }, // 180-225度
				{ name: '积分+8', type: 'points', value: 8 },   // 225-270度
				{ name: '积分+18', type: 'points', value: 18 }, // 270-315度
				{ name: '积分+25', type: 'points', value: 25 }  // 315-360度
			]
		}
	},

		onLoad() {
			this.loadUserData()
		},

		methods: {
			// 加载用户数据
			loadUserData() {
				// 获取用户信息
				UserApi.info().then(result => {
					this.userInfo = result.data.userInfo
				})

				// 从本地存储获取优惠券状态
				const couponStatus = uni.getStorageSync('coupon_received_68_9')
				this.couponReceived = !!couponStatus

				// 获取今日抽奖次数
				const today = new Date().toDateString()
				const todayLottery = uni.getStorageSync('lottery_' + today)
				this.remainingTimes = todayLottery ? (3 - todayLottery.times) : 3
			},

			// 领取优惠券
			async receiveCoupon() {
				if (this.couponReceived) {
					uni.showToast({
						title: '优惠券已领取',
						icon: 'none',
						duration: 1500
					})
					return
				}

				// 检查会员等级权限
				if (!this.checkVipPermission()) {
					uni.showToast({
						title: '仅限会员领取，请先开通会员',
						icon: 'none',
						duration: 1500
					})
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/vip/index'
						})
					}, 1500)
					return
				}

				try {
					uni.showLoading({ title: '领取中...' })

					// 调用优惠券领取API，传入优惠券ID 100009
					const result = await MyCouponApi.receive(100009)

					if (result.status === 200) {
						this.couponReceived = true
						uni.setStorageSync('coupon_received_68_9', true)

						uni.showToast({
							title: '优惠券领取成功！',
							icon: 'success'
						})
					}
				} catch (error) {
					console.error('优惠券领取失败:', error)
					uni.showToast({
						title: error.message || '领取失败',
						icon: 'none'
					})
				} finally {
					uni.hideLoading()
				}
			},

			// 检查VIP权限
			checkVipPermission() {
				// 检查用户等级权重，普通会员weight=1，不能领取
				if (!this.userInfo?.grade?.weight || this.userInfo.grade.weight <= 1) {
					return false
				}
				return true
			},

			// 开始抽奖
			startLottery() {
				// 检查会员等级权限
				if (!this.checkVipPermission()) {
					uni.showToast({
						title: '抽奖仅限会员参与，请先开通会员',
						icon: 'none',
						duration: 1500
					})
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/vip/index'
						})
					}, 1500)
					return
				}

				if (this.remainingTimes <= 0) {
					uni.showToast({
						title: '今日抽奖次数已用完',
						icon: 'none'
					})
					return
				}

				if (this.isRotating) return

				// 随机选择奖品
				const prizeIndex = Math.floor(Math.random() * this.prizes.length)
				const prize = this.prizes[prizeIndex]

				// 先复原转盘（无动画）
				this.isRotating = false
				this.rotateAngle = 0

				// 延迟开始转动（有动画）
				setTimeout(() => {
					this.isRotating = true
					const sectorCenterAngle = prizeIndex * 45 + 22.5
					const targetAngle = 360 * 5 + (360 - sectorCenterAngle)
					this.rotateAngle = targetAngle
				}, 100)

				// 2秒后显示结果
				setTimeout(() => {
					this.isRotating = false
					this.showPrizeResult(prize)
					this.updateLotteryTimes()
				}, 2000)
			},

			// 显示中奖结果
			showPrizeResult(prize) {
				this.resultContent = `恭喜您获得：${prize.name}！`
				this.showResult = true
			},

			// 更新抽奖次数
			updateLotteryTimes() {
				this.remainingTimes--
				const today = new Date().toDateString()
				const todayLottery = uni.getStorageSync('lottery_' + today) || { times: 0 }
				todayLottery.times++
				uni.setStorageSync('lottery_' + today, todayLottery)
			},

			// 关闭结果弹窗
			closeResult() {
				this.showResult = false
			},

			// 跳转到充值页面
			goToRecharge() {
				uni.navigateTo({
					url: '/pages/vip/index'
				})
			},

			// 跳转到积分兑换页面
			goToExchange() {
				uni.navigateTo({
					url: '/pagesNew/points/category/index'
				})
			}
		}
	}
	</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
}

.top-img1 {
	width: 100%;
	height: 0;
	padding-bottom: 94%; /* 3068/3261 = 94% */
	background-image: url('https://xinjiang.zhanyuankj.cn/10001/20250730/1bddc8d45eb45e4b0f5aa026799c6673.jpg');
	background-size: 100% 100%;
}

.top-img2 {
	width: 100%;
	height: 0;
	padding-bottom: 37%; /* 1207/3266 = 37% */
	background-image: url('https://xinjiang.zhanyuankj.cn/10001/20250730/da39058eb440b6bbc8bda146a5cf240d.jpg');
	background-size: 100% 100%;
}

.top-img3 {
	width: 100%;
	height: 0;
	padding-bottom: 14%;
	background-image: url('https://xinjiang.zhanyuankj.cn/10001/20250730/25ceb82043a1e5c2984c67b958724b42.jpg');
	background-size: 100% 100%;
	position: relative;
}

.coupon-text {
	position: absolute;
	left: 38%;
	top: 36%;
	color: white;
	font-size: 36rpx;
	font-weight: bold;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.content {
	padding: 20rpx;
}

.header {
	text-align: center;
	padding: 40rpx 0;
	color: white;

	.header-title {
		font-size: 48rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;
	}

	.header-subtitle {
		font-size: 28rpx;
		opacity: 0.9;
	}
}



.lottery-section {
	margin: 50rpx 0;
	text-align: center;
}

.lottery-title {
	color: white;
	margin-bottom: 100rpx;

	text {
		font-size: 36rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;
	}

	.lottery-subtitle {
		font-size: 24rpx;
		opacity: 0.9;
	}
}

.lottery-container {
	position: relative;
	width: 500rpx;
	height: 500rpx;
	margin: 0 auto 110rpx auto;
}

.lottery-btn {
	background: linear-gradient(135deg, #27ae60, #2ecc71);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 20rpx 60rpx;
	font-size: 32rpx;
	font-weight: bold;
	box-shadow: 0 8rpx 20rpx rgba(39, 174, 96, 0.3);

	&.disabled {
		background: rgba(255, 255, 255, 0.2);
		color: rgba(255, 255, 255, 0.5);
		box-shadow: none;
	}
}

.action-buttons {
	display: flex;
	justify-content: space-between;
	margin-top: 40rpx;
	gap: 30rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.3);
	color: white;
	border-radius: 40rpx;
	font-size: 28rpx;
	font-weight: 500;
	backdrop-filter: blur(10rpx);
}

.lottery-wheel {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	position: relative;
	transition: transform 2s ease-out;
	box-shadow: 0 0 30rpx rgba(0, 0, 0, 0.2);
	background: conic-gradient(
		#e74c3c 0deg 45deg,
		#1abc9c 45deg 90deg,
		#3498db 90deg 135deg,
		#27ae60 135deg 180deg,
		#f39c12 180deg 225deg,
		#9b59b6 225deg 270deg,
		#7f8c8d 270deg 315deg,
		#95a5a6 315deg 360deg
	);
}

.lottery-wheel.no-transition {
	transition: none;
}

.lottery-sector {
	position: absolute;
	width: 100%;
	height: 100%;

	.prize-text {
		position: absolute;
		font-size: 24rpx;
		font-weight: bold;
		color: white;
		text-shadow: 1rpx 1rpx 2rpx rgba(0,0,0,0.5);
		left: 50%;
		top: 50%;
	}
}

.sector-0 .prize-text { transform: translate(-50%, -50%) rotate(22.5deg) translateY(-160rpx); }
.sector-1 .prize-text { transform: translate(-50%, -50%) rotate(67.5deg) translateY(-160rpx); }
.sector-2 .prize-text { transform: translate(-50%, -50%) rotate(112.5deg) translateY(-160rpx); }
.sector-3 .prize-text { transform: translate(-50%, -50%) rotate(157.5deg) translateY(-160rpx); }
.sector-4 .prize-text { transform: translate(-50%, -50%) rotate(202.5deg) translateY(-160rpx); }
.sector-5 .prize-text { transform: translate(-50%, -50%) rotate(247.5deg) translateY(-160rpx); }
.sector-6 .prize-text { transform: translate(-50%, -50%) rotate(292.5deg) translateY(-160rpx); }
.sector-7 .prize-text { transform: translate(-50%, -50%) rotate(337.5deg) translateY(-160rpx); }

.lottery-pointer {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #27ae60, #2ecc71);
	border: 2rpx solid white;
	border-radius: 50%;
	transform: translate(-50%, -50%);
	z-index: 10;
	box-shadow: 0 3rpx 9rpx rgba(0, 0, 0, 0.3);

}

.pointer-arrow {
	width: 80rpx;
	height: 80rpx;
	position: absolute;
	top: -50rpx;
	left: 50%;
	transform: translateX(-50%);
}
</style>