"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../store/index.js");
require("../../core/config/index.js");
const api_page = require("../../api/page.js");
const api_user = require("../../api/user.js");
const core_platform = require("../../core/platform.js");
const api_article_index = require("../../api/article/index.js");
const utils_verify = require("../../utils/verify.js");
const Page = () => "../../components/page/index.js";
const PrivacyPopup = () => "../../components/privacy-popup/index.js";
const PromotePopup = () => "../../components/promote-popup/index.js";
getApp();
const _sfc_main = {
  components: {
    Page,
    PrivacyPopup,
    PromotePopup
  },
  data() {
    return {
      article: {},
      vipShow: false,
      form: {
        name: "",
        phone: "",
        detail: "",
        address: ""
      },
      showInvite: false,
      // 页面参数
      options: {},
      // 页面属性
      page: {},
      // 页面元素
      items: [],
      // 启用开屏推广
      enablePromote: core_platform.platfrom != "MP-WEIXIN"
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.options = options;
    this.getPageData();
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
  },
  methods: {
    vip() {
      let app = this;
      api_article_index.detail(10004).then((result) => {
        app.article = result.data.detail;
        app.vipShow = true;
      });
    },
    toVip() {
      this.vipShow = false;
      this.showInvite = true;
    },
    // 表单提交
    addAddress() {
      const app = this;
      if (app.form.name == "") {
        common_vendor.index.showToast({
          title: "请输入姓名",
          icon: "none"
        });
        return false;
      }
      if (!utils_verify.isMobile(app.form.phone)) {
        common_vendor.index.showToast({
          title: "请输入正确手机号",
          icon: "none"
        });
        return false;
      }
      if (app.form.address == "") {
        common_vendor.index.showToast({
          title: "请输入联系地址",
          icon: "none"
        });
        return false;
      }
      api_user.add({
        form: app.form
      }).then((result) => {
        app.$toast(result.message);
        app.showInvite = false;
      });
    },
    /**
     * 加载页面数据
     * @param {Object} callback
     */
    getPageData(callback) {
      const app = this;
      const pageId = app.options.pageId || 10003;
      api_page.detail(pageId).then((result) => {
        const {
          data: {
            pageData
          }
        } = result;
        app.page = pageData.page;
        app.items = pageData.items;
        app.setPageBar();
      }).finally(() => callback && callback());
    },
    /**
     * 设置顶部导航栏
     */
    setPageBar() {
      const {
        page
      } = this;
      common_vendor.index.setNavigationBarTitle({
        title: page.params.title
      });
      common_vendor.index.setNavigationBarColor({
        frontColor: page.style.titleTextColor === "white" ? "#ffffff" : "#000000",
        backgroundColor: page.style.titleBackgroundColor
      });
    },
    // 用户隐私保护提示结束事件
    onPrivacyEnd() {
      this.enablePromote = true;
    }
  },
  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.getPageData(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    const app = this;
    const {
      page
    } = app;
    return {
      title: page.params.shareTitle,
      path: "/pages/index/index?" + app.$getShareUrlParams()
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    const app = this;
    const {
      page
    } = app;
    return {
      title: page.params.shareTitle,
      path: "/pages/index/index?" + app.$getShareUrlParams()
    };
  }
};
if (!Array) {
  const _component_Page = common_vendor.resolveComponent("Page");
  const _component_PromotePopup = common_vendor.resolveComponent("PromotePopup");
  const _component_PrivacyPopup = common_vendor.resolveComponent("PrivacyPopup");
  const _easycom_mp_html2 = common_vendor.resolveComponent("mp-html");
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  const _easycom_u_input2 = common_vendor.resolveComponent("u-input");
  const _easycom_u_form_item2 = common_vendor.resolveComponent("u-form-item");
  const _easycom_u_form2 = common_vendor.resolveComponent("u-form");
  (_component_Page + _component_PromotePopup + _component_PrivacyPopup + _easycom_mp_html2 + _easycom_u_popup2 + _easycom_u_input2 + _easycom_u_form_item2 + _easycom_u_form2)();
}
const _easycom_mp_html = () => "../../uni_modules/mp-html/components/mp-html/mp-html.js";
const _easycom_u_popup = () => "../../uni_modules/vk-uview-ui/components/u-popup/u-popup.js";
const _easycom_u_input = () => "../../uni_modules/vk-uview-ui/components/u-input/u-input.js";
const _easycom_u_form_item = () => "../../uni_modules/vk-uview-ui/components/u-form-item/u-form-item.js";
const _easycom_u_form = () => "../../uni_modules/vk-uview-ui/components/u-form/u-form.js";
if (!Math) {
  (_easycom_mp_html + _easycom_u_popup + _easycom_u_input + _easycom_u_form_item + _easycom_u_form)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      items: $data.items
    }),
    b: $data.enablePromote
  }, $data.enablePromote ? {} : {}, {
    c: common_vendor.o(($event) => $options.onPrivacyEnd()),
    d: common_vendor.p({
      hideTabBar: true
    }),
    e: common_vendor.o((...args) => $options.vip && $options.vip(...args)),
    f: common_vendor.t($data.article.title),
    g: common_vendor.p({
      content: $data.article.content
    }),
    h: common_vendor.o(($event) => $data.vipShow = false),
    i: common_vendor.o((...args) => $options.toVip && $options.toVip(...args)),
    j: common_vendor.o(($event) => $data.vipShow = $event),
    k: common_vendor.p({
      mode: "center",
      modelValue: $data.vipShow
    }),
    l: common_vendor.o(($event) => $data.form.name = $event),
    m: common_vendor.p({
      placeholder: "请输入姓名",
      modelValue: $data.form.name
    }),
    n: common_vendor.p({
      label: "姓名",
      prop: "name",
      required: ""
    }),
    o: common_vendor.o(($event) => $data.form.phone = $event),
    p: common_vendor.p({
      placeholder: "请输入手机号",
      modelValue: $data.form.phone
    }),
    q: common_vendor.p({
      label: "电话",
      prop: "phone",
      required: ""
    }),
    r: common_vendor.o(($event) => $data.form.address = $event),
    s: common_vendor.p({
      placeholder: "请输入联系地址",
      modelValue: $data.form.address
    }),
    t: common_vendor.p({
      label: "联系地址",
      prop: "phone",
      required: ""
    }),
    v: common_vendor.o(($event) => $data.form.detail = $event),
    w: common_vendor.p({
      placeholder: "备注信息",
      modelValue: $data.form.detail
    }),
    x: common_vendor.p({
      label: "备注",
      prop: "detail",
      ["border-bottom"]: false
    }),
    y: common_vendor.sr("uForm", "e54fd6d8-6,e54fd6d8-5"),
    z: common_vendor.p({
      model: $data.form,
      ["label-width"]: "140rpx"
    }),
    A: common_vendor.o((...args) => $options.addAddress && $options.addAddress(...args)),
    B: common_vendor.o(($event) => $data.showInvite = $event),
    C: common_vendor.p({
      mode: "center",
      width: "90%",
      ["border-radius"]: "10",
      ["mask-close-able"]: true,
      closeable: true,
      modelValue: $data.showInvite
    }),
    D: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e54fd6d8"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/invite/index.js.map
