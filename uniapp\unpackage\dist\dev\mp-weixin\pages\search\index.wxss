/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-2dab939d {
  padding: 20rpx;
  min-height: 100vh;
  background: #f7f7f7;
}
.search-wrapper.data-v-2dab939d {
  display: flex;
  height: 64rpx;
}
.search-input.data-v-2dab939d {
  width: 80%;
  background: #fff;
  border-radius: 10rpx 0 0 10rpx;
  box-sizing: border-box;
  overflow: hidden;
}
.search-input .search-input-wrapper.data-v-2dab939d {
  display: flex;
}
.search-input .search-input-wrapper .left.data-v-2dab939d {
  display: flex;
  width: 60rpx;
  justify-content: center;
  align-items: center;
}
.search-input .search-input-wrapper .left .search-icon.data-v-2dab939d {
  display: block;
  color: #b4b4b4;
  font-size: 28rpx;
}
.search-input .search-input-wrapper .right.data-v-2dab939d {
  flex: 1;
}
.search-input .search-input-wrapper .right input.data-v-2dab939d {
  font-size: 28rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
}
.search-input .search-input-wrapper .right input .input-placeholder.data-v-2dab939d {
  color: #aba9a9;
}
.search-button.data-v-2dab939d {
  width: 20%;
  box-sizing: border-box;
}
.search-button .button.data-v-2dab939d {
  height: 64rpx;
  font-size: 28rpx;
  border-radius: 0 10rpx 10rpx 0;
  background: var(--main-bg);
  color: var(--main-text);
  display: flex;
  justify-content: center;
  align-items: center;
}
.history .his-head.data-v-2dab939d {
  font-size: 28rpx;
  padding: 50rpx 0 0 0;
  color: #777;
}
.history .his-head .icon.data-v-2dab939d {
  float: right;
}
.history .his-list.data-v-2dab939d {
  padding: 20rpx 0;
  overflow: hidden;
}
.history .his-list .his-item.data-v-2dab939d {
  width: 33.3%;
  float: left;
  padding: 10rpx;
  box-sizing: border-box;
}
.history .his-list .his-item .history-button.data-v-2dab939d {
  text-align: center;
  padding: 14rpx;
  line-height: 30rpx;
  border-radius: 100rpx;
  background: #fff;
  font-size: 26rpx;
  border: 1rpx solid #efefef;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}