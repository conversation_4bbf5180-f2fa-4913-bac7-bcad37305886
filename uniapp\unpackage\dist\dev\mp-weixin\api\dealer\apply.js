"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  status: "dealer.apply/status",
  submit: "dealer.apply/submit"
};
const status = (param) => {
  return utils_request_index.$http.get(api.status, param);
};
const submit = (data) => {
  return utils_request_index.$http.post(api.submit, data);
};
exports.status = status;
exports.submit = submit;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/dealer/apply.js.map
