"use strict";
const core_config_index = require("../../../core/config/index.js");
const api_groupon_setting = require("../../../api/groupon/setting.js");
const utils_storage = require("../../../utils/storage.js");
const CACHE_KEY = "Groupon-Setting";
const setStorage = (data2) => {
  const expireTime = 30 * 60;
  utils_storage.storage.set(CACHE_KEY, data2, expireTime);
};
const getStorage = () => {
  return utils_storage.storage.get(CACHE_KEY);
};
const getApiData = () => {
  return new Promise((resolve, reject) => {
    api_groupon_setting.data().then((result) => {
      resolve(result.data.setting);
    });
  });
};
const data = (isCache = false) => {
  if (isCache == void 0) {
    isCache = core_config_index.Config.get("enabledSettingCache");
  }
  return new Promise((resolve, reject) => {
    const cacheData = getStorage();
    if (isCache && cacheData) {
      resolve(cacheData);
    } else {
      getApiData().then((data2) => {
        setStorage(data2);
        resolve(data2);
      });
    }
  });
};
const GrouponSettingModel = {
  data
};
exports.GrouponSettingModel = GrouponSettingModel;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/common/model/groupon/Setting.js.map
