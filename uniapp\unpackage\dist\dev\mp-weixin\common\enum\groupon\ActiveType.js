"use strict";
const common_enum_enum = require("../enum.js");
const ActiveTypeEnum = new common_enum_enum.Enum([
  { key: "NORMAL", name: "普通拼团", value: 10, name2: "多人拼团" },
  { key: "PULL_NEW", name: "老带新拼团", value: 20, name2: "新人团" },
  { key: "STEPS", name: "阶梯拼团", value: 30, name2: "阶梯团" }
]);
exports.ActiveTypeEnum = ActiveTypeEnum;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/common/enum/groupon/ActiveType.js.map
