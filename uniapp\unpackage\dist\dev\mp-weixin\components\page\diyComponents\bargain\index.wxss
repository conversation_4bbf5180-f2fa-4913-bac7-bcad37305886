/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.diy-bargain .goods-item.data-v-647596e4 {
  display: flex;
  margin-bottom: 20rpx;
  background: #fff;
  padding: 20rpx 16rpx;
}
.diy-bargain .goods-item.data-v-647596e4:last-child {
  margin-bottom: 0;
}
.diy-bargain .goods-item .goods-image .image.data-v-647596e4 {
  display: block;
  width: 220rpx;
  height: 220rpx;
}
.diy-bargain .goods-item .goods-info.data-v-647596e4 {
  width: 498rpx;
  padding-top: 4rpx;
  margin-left: 14rpx;
  position: relative;
}
.diy-bargain .goods-item .goods-info .goods-name.data-v-647596e4 {
  font-size: 28rpx;
  min-height: 68rpx;
  line-height: 1.3;
}
.diy-bargain .peoples.data-v-647596e4 {
  display: flex;
  margin-top: 14rpx;
}
.diy-bargain .peoples .user-list.data-v-647596e4 {
  display: flex;
  margin-right: 10rpx;
}
.diy-bargain .peoples .user-list .user-item-avatar.data-v-647596e4 {
  margin-left: -8rpx;
}
.diy-bargain .peoples .user-list .user-item-avatar.data-v-647596e4:first-child {
  margin-left: 0;
}
.diy-bargain .peoples .people__text.data-v-647596e4 {
  font-size: 24rpx;
  color: #818181;
}
.diy-bargain .goods-price.data-v-647596e4 {
  margin-top: 14rpx;
  color: #818181;
  font-size: 24rpx;
  text-decoration: line-through;
}
.diy-bargain .floor-price.data-v-647596e4 {
  color: var(--main-bg);
}
.diy-bargain .floor-price .small.data-v-647596e4 {
  font-size: 24rpx;
}
.diy-bargain .floor-price .big.data-v-647596e4 {
  font-size: 24rpx;
}
.diy-bargain .opt-touch.data-v-647596e4 {
  position: absolute;
  bottom: 0;
  right: 10rpx;
}
.diy-bargain .opt-touch .touch-btn.data-v-647596e4 {
  color: #fff;
  font-size: 28rpx;
  background: #d3a975;
  border-radius: 30rpx;
  padding: 10rpx 28rpx;
}