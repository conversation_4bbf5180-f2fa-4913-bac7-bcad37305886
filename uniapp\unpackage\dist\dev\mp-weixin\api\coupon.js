"use strict";
const utils_request_index = require("../utils/request/index.js");
const api = {
  list: "coupon/list"
};
const list = (param, option) => {
  const options = {
    isPrompt: true,
    //（默认 true 说明：本接口抛出的错误是否提示）
    load: true,
    //（默认 true 说明：本接口是否提示加载动画）
    ...option
  };
  return utils_request_index.$http.get(api.list, param, options);
};
exports.list = list;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/coupon.js.map
