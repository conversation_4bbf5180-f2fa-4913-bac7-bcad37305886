"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  name: "u-rate",
  emits: ["update:modelValue", "input", "change"],
  props: {
    // 用于v-model双向绑定选中的星星数量
    // 1.4.5版新增
    value: {
      type: [Number, String],
      default: -1
    },
    modelValue: {
      type: [Number, String],
      default: -1
    },
    // 要显示的星星数量
    count: {
      type: [Number, String],
      default: 5
    },
    // 当前需要默认选中的星星(选中的个数)
    // 1.4.5后通过value双向绑定，不再建议使用此参数
    current: {
      type: [Number, String],
      default: 0
    },
    // 是否不可选中
    disabled: {
      type: Boolean,
      default: false
    },
    // 星星的大小，单位rpx
    size: {
      type: [Number, String],
      default: 32
    },
    // 未选中时的颜色
    inactiveColor: {
      type: String,
      default: "#b2b2b2"
    },
    // 选中的颜色
    activeColor: {
      type: String,
      default: "#FA3534"
    },
    // 星星之间的间距，单位rpx
    gutter: {
      type: [Number, String],
      default: 10
    },
    // 最少能选择的星星个数
    minCount: {
      type: [Number, String],
      default: 0
    },
    // 是否允许半星(功能尚未实现)
    allowHalf: {
      type: Boolean,
      default: false
    },
    // 选中时的图标(星星)
    activeIcon: {
      type: String,
      default: "star-fill"
    },
    // 未选中时的图标(星星)
    inactiveIcon: {
      type: String,
      default: "star"
    },
    // 自定义扩展前缀，方便用户扩展自己的图标库
    customPrefix: {
      type: String,
      default: "uicon"
    },
    colors: {
      type: Array,
      default() {
        return [];
      }
    },
    icons: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      // 生成一个唯一id，否则一个页面多个评分组件，会造成冲突
      elId: this.$u.guid(),
      elClass: this.$u.guid(),
      starBoxLeft: 0,
      // 评分盒子左边到屏幕左边的距离，用于滑动选择时计算距离
      // 当前激活的星星的index，如果存在value，优先使用value，因为它可以双向绑定(1.4.5新增)
      activeIndex: 0,
      starWidth: 0,
      // 每个星星的宽度
      starWidthArr: []
      //每个星星最右边到组件盒子最左边的距离
    };
  },
  created() {
    this.activeIndex = this.valueCom != -1 ? this.valueCom : this.current;
  },
  watch: {
    current(val) {
      this.activeIndex = val;
    },
    valueCom(val) {
      this.activeIndex = val;
    }
  },
  computed: {
    valueCom() {
      return this.modelValue;
    },
    decimal() {
      if (this.disabled) {
        return this.activeIndex * 100 % 100;
      } else if (this.allowHalf) {
        return 50;
      }
    },
    elActiveIcon() {
      const len = this.icons.length;
      if (len && len <= this.count) {
        const step = Math.round(this.activeIndex / Math.round(this.count / len));
        if (step < 1)
          return this.icons[0];
        if (step > len)
          return this.icons[len - 1];
        return this.icons[step - 1];
      }
      return this.activeIcon;
    },
    elActiveColor() {
      const len = this.colors.length;
      if (len && len <= this.count) {
        const step = Math.round(this.activeIndex / Math.round(this.count / len));
        if (step < 1)
          return this.colors[0];
        if (step > len)
          return this.colors[len - 1];
        return this.colors[step - 1];
      }
      return this.activeColor;
    }
  },
  methods: {
    // 获取评分组件盒子的布局信息
    getElRectById() {
      this.$uGetRect("#" + this.elId).then((res) => {
        this.starBoxLeft = res.left;
      });
    },
    // 获取单个星星的尺寸
    getElRectByClass() {
      this.$uGetRect("." + this.elClass).then((res) => {
        this.starWidth = res.width;
        for (let i = 0; i < this.count; i++) {
          this.starWidthArr[i] = (i + 1) * this.starWidth;
        }
      });
    },
    // 手指滑动
    touchMove(e) {
      if (this.disabled) {
        return;
      }
      if (!e.changedTouches[0]) {
        return;
      }
      const movePageX = e.changedTouches[0].pageX;
      const distance = movePageX - this.starBoxLeft;
      if (distance <= 0) {
        this.activeIndex = 0;
      }
      let index = Math.ceil(distance / this.starWidth);
      this.activeIndex = index > this.count ? this.count : index;
      if (this.activeIndex < this.minCount)
        this.activeIndex = this.minCount;
      this.emitEvent();
    },
    // 通过点击，直接选中
    click(index, e) {
      if (this.disabled) {
        return;
      }
      if (this.allowHalf)
        ;
      if (index == 1) {
        if (this.activeIndex == 1) {
          this.activeIndex = 0;
        } else {
          this.activeIndex = 1;
        }
      } else {
        this.activeIndex = index;
      }
      if (this.activeIndex < this.minCount)
        this.activeIndex = this.minCount;
      this.emitEvent();
    },
    // 发出事件
    emitEvent() {
      this.$emit("change", this.activeIndex);
      if (this.valueCom != -1) {
        this.$emit("input", this.activeIndex);
        this.$emit("update:modelValue", this.activeIndex);
      }
    },
    showDecimalIcon(index) {
      return this.disabled && parseInt(this.activeIndex) === index;
    }
  },
  mounted() {
    this.getElRectById();
    this.getElRectByClass();
  }
};
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  _easycom_u_icon2();
}
const _easycom_u_icon = () => "../u-icon/u-icon.js";
if (!Math) {
  _easycom_u_icon();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.count, (item, index, i0) => {
      return {
        a: common_vendor.o(($event) => $options.click(index + 1, $event), index),
        b: "3fa418fb-0-" + i0,
        c: common_vendor.p({
          name: $data.activeIndex > index ? $options.elActiveIcon : $props.inactiveIcon,
          color: $data.activeIndex > index ? $options.elActiveColor : $props.inactiveColor,
          ["custom-style"]: {
            fontSize: $props.size + "rpx",
            padding: `0 ${$props.gutter / 2 + "rpx"}`
          },
          ["custom-prefix"]: $props.customPrefix,
          ["show-decimal-icon"]: $options.showDecimalIcon(index),
          percent: $options.decimal,
          ["inactive-color"]: $props.inactiveColor
        }),
        d: index
      };
    }),
    b: common_vendor.n($data.elClass),
    c: $data.elId,
    d: common_vendor.o((...args) => $options.touchMove && $options.touchMove(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3fa418fb"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/vk-uview-ui/components/u-rate/u-rate.js.map
