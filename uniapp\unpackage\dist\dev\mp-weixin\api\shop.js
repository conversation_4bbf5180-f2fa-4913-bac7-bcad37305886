"use strict";
const utils_request_index = require("../utils/request/index.js");
const api = {
  list: "shop/list",
  detail: "shop/detail"
};
const list = (param) => {
  return utils_request_index.$http.get(api.list, param);
};
const detail = (shopId) => {
  return utils_request_index.$http.get(api.detail, { shopId });
};
exports.detail = detail;
exports.list = list;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/shop.js.map
