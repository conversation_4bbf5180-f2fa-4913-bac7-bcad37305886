
page {
    background: #fff;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.input.data-v-7bd142cd {
  display: block;
}
.c-violet.data-v-7bd142cd {
  color: #786cff;
}
.col-80.data-v-7bd142cd {
  color: #808080;
}
.col-bb.data-v-7bd142cd {
  color: #bbb;
}
.f-38.data-v-7bd142cd {
  font-size: 38rpx;
}
.dealer-bg .image.data-v-7bd142cd {
  width: 100%;
}
.widget-form.data-v-7bd142cd {
  position: relative;
  width: 700rpx;
  box-sizing: border-box;
  box-shadow: 0 1rpx 20rpx rgba(0, 0, 0, 0.21);
  border-radius: 12rpx;
  margin-top: -80rpx;
}
.widget-form .form-title.data-v-7bd142cd {
  padding: 0 40rpx;
  height: 90rpx;
  border-bottom: 1rpx solid #e7e7e7;
  display: flex;
  align-items: center;
}
.widget-form .form-box.data-v-7bd142cd {
  padding: 40rpx 35rpx;
}
.widget-form .form-box .form-field.data-v-7bd142cd {
  height: 80rpx;
  margin-bottom: 24rpx;
  padding: 10rpx 28rpx;
  background-color: #f9f9f9;
  box-sizing: border-box;
  font-size: 28rpx;
}
.widget-form .form-box .form-field .field-label.data-v-7bd142cd {
  width: 130rpx;
  color: #808080;
}
.widget-form .form-box .form-field .input.data-v-7bd142cd {
  font-size: 28rpx;
}
.form-license .license-icon.data-v-7bd142cd {
  margin-right: 12rpx;
}
.form-submit.data-v-7bd142cd {
  margin-top: 40rpx;
}
.form-submit button.data-v-7bd142cd {
  font-size: 30rpx;
  background: #786cff;
  border: 1rpx solid #786cff;
  color: white;
  border-radius: 50rpx;
  padding: 0 120rpx;
}
.form-submit button[disabled].data-v-7bd142cd {
  background: #8e84fc;
  border-color: #8e84fc;
  color: white;
}
.pops-content.data-v-7bd142cd {
  padding: 30rpx 48rpx;
  font-size: 28rpx;
  line-height: 50rpx;
  text-align: left;
  color: #606266;
  min-height: 320rpx;
  max-height: 640rpx;
  box-sizing: border-box;
}
.dealer-boot.data-v-7bd142cd {
  padding: 10rpx 30rpx;
  margin-top: 80rpx;
}
.dealer-boot .msg__icon.data-v-7bd142cd {
  font-size: 120rpx;
  color: #8e84fc;
}
.dealer-boot .boot__submit.data-v-7bd142cd {
  margin-top: 60rpx;
}