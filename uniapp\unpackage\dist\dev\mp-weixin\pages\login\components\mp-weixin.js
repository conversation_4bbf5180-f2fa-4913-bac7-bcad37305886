"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const api_login_index = require("../../../api/login/index.js");
const api_upload = require("../../../api/upload.js");
const utils_util = require("../../../utils/util.js");
const utils_verify = require("../../../utils/verify.js");
const common_model_Store = require("../../../common/model/Store.js");
const AvatarImage = () => "../../../components/avatar-image/index.js";
const _sfc_main = {
  components: {
    AvatarImage
  },
  data() {
    return {
      // 商城基本信息
      storeInfo: void 0,
      // 是否需要填写用户昵称和头像
      isPersonal: void 0,
      // 微信小程序登录凭证 (code)
      // 提交到后端，用于换取openid
      code: "",
      // 按钮禁用
      disabled: false,
      // 头像路径 (用于显示)
      avatarUrl: "",
      // 临时图片 (用于上传)
      tempFile: void 0,
      // 表单数据
      form: {
        avatarId: null,
        nickName: ""
      }
    };
  },
  created() {
    this.getStoreInfo();
    this.getIsPersonal();
  },
  methods: {
    // 获取商城基本信息
    getStoreInfo() {
      common_model_Store.StoreModel.storeInfo().then((storeInfo) => this.storeInfo = storeInfo);
    },
    /**
     * 请求后端是否需要填写昵称头像 (微信小程序端)
     *  - 条件1: 后台开启了填写微信头像和昵称
     *  - 条件2: 用户首次注册或者已注册但未填写过信息
     */
    async getIsPersonal() {
      const app = this;
      api_login_index.isPersonalMpweixin({ code: await app.getCode() }).then((result) => app.isPersonal = result.data.isPersonalMpweixin);
    },
    // 获取code
    // https://developers.weixin.qq.com/miniprogram/dev/api/open-api/login/wx.login.html
    getCode() {
      return new Promise((resolve, reject) => {
        common_vendor.index.login({
          provider: "weixin",
          success({ code }) {
            common_vendor.index.__f__("log", "at pages/login/components/mp-weixin.vue:121", "code", code);
            resolve(code);
          },
          fail: reject
        });
      });
    },
    // 绑定昵称输入框 (用于微信小程序端快速填写昵称能力)
    onInputNickName({ detail }) {
      common_vendor.index.__f__("log", "at pages/login/components/mp-weixin.vue:131", detail);
      if (detail.value) {
        this.form.nickName = detail.value;
      }
    },
    // 点击头像按钮事件
    onClickAvatar() {
      return;
    },
    // 选择头像事件 - 仅限微信小程序
    onChooseAvatar({ detail }) {
      const app = this;
      app.avatarUrl = detail.avatarUrl;
      app.tempFile = { path: app.avatarUrl };
    },
    // 选择图片
    chooseImage() {
      const app = this;
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"],
        // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"],
        // 可以指定来源是相册还是相机，默认二者都有
        success({ tempFiles }) {
          app.tempFile = tempFiles[0];
          app.avatarUrl = app.tempFile.path;
        }
      });
    },
    // 上传图片
    uploadFile() {
      const app = this;
      return api_upload.image([app.tempFile], false).then((fileIds) => {
        app.form.avatarId = fileIds[0];
        app.tempFile = null;
        return true;
      }).catch(() => {
        return false;
      });
    },
    // 确认提交头像昵称
    async handleSubmit() {
      const app = this;
      common_vendor.index.__f__("log", "at pages/login/components/mp-weixin.vue:186", "handleSubmit", app.form, app.tempFile);
      if (app.disabled === true) {
        return;
      }
      if (app.tempFile === void 0 || utils_verify.isEmpty(app.form.nickName)) {
        app.$toast("请填写头像和昵称~");
        return;
      }
      app.disabled = true;
      if (!await app.uploadFile()) {
        app.$toast("很抱歉，头像上传失败");
        app.disabled = false;
        return;
      }
      app.onAuthSuccess({ nickName: app.form.nickName, avatarId: app.form.avatarId });
    },
    // 一键登录按钮点击事件
    handleLogin() {
      this.onAuthSuccess({});
    },
    // 授权成功事件
    // 这里分为两个逻辑:
    // 1.将code和userInfo提交到后端，如果存在该用户 则实现自动登录，无需再填写手机号
    // 2.如果不存在该用户, 则显示注册页面, 需填写手机号
    // 3.如果后端报错了, 则显示错误信息
    async onAuthSuccess(userInfo) {
      const app = this;
      store_index.store.dispatch("LoginMpWx", {
        partyData: {
          code: await app.getCode(),
          oauth: "MP-WEIXIN",
          userInfo
        },
        refereeId: store_index.store.getters.refereeId
      }).then((result) => {
        app.$toast(result.message);
        common_vendor.index.$emit("syncRefresh", true);
        setTimeout(() => app.onNavigateBack(), 2e3);
      }).catch((err) => {
        const resultData = err.result.data;
        if (utils_util.isEmpty(resultData)) {
          app.$toast(err.result.message);
        }
        if (resultData.isBack) {
          setTimeout(() => app.onNavigateBack(1), 2e3);
        }
        if (resultData.isBindMobile) {
          app.onEmitSuccess(userInfo);
        }
      });
    },
    // 将oauth提交给父级
    // 这里要重新获取code, 因为上一次获取的code不能复用(会报错)
    async onEmitSuccess(userInfo) {
      const app = this;
      app.$emit("success", {
        oauth: "MP-WEIXIN",
        // 第三方登录类型: MP-WEIXIN
        code: await app.getCode(),
        // 微信登录的code, 用于换取openid
        userInfo
        // 微信用户信息
      });
    },
    /**
     * 暂不登录
     */
    handleCancel() {
      this.onNavigateBack();
    },
    /**
     * 登录成功-跳转回原页面
     */
    onNavigateBack(delta = 1) {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        common_vendor.index.navigateBack({
          delta: Number(delta || 1)
        });
      } else {
        this.$navTo("pages/index/index");
      }
    }
  }
};
if (!Array) {
  const _component_avatar_image = common_vendor.resolveComponent("avatar-image");
  _component_avatar_image();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isPersonal === true
  }, $data.isPersonal === true ? {
    b: common_vendor.p({
      url: $data.avatarUrl,
      width: 100
    }),
    c: common_vendor.o(($event) => $options.onClickAvatar()),
    d: common_vendor.o((...args) => $options.onChooseAvatar && $options.onChooseAvatar(...args)),
    e: common_vendor.o([($event) => $data.form.nickName = $event.detail.value, (...args) => $options.onInputNickName && $options.onInputNickName(...args)]),
    f: common_vendor.o((...args) => $options.onInputNickName && $options.onInputNickName(...args)),
    g: $data.form.nickName,
    h: $data.disabled ? 1 : "",
    i: common_vendor.o(($event) => $options.handleSubmit()),
    j: common_vendor.o(($event) => $options.handleCancel())
  } : {}, {
    k: $data.isPersonal === false
  }, $data.isPersonal === false ? {
    l: $data.storeInfo && $data.storeInfo.image_url ? $data.storeInfo.image_url : "/static/default-logo.png",
    m: $data.disabled ? 1 : "",
    n: common_vendor.o(($event) => $options.handleLogin()),
    o: common_vendor.o(($event) => $options.handleCancel())
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f2695243"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/login/components/mp-weixin.js.map
