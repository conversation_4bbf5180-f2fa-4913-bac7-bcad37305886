"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_request_upload_qiniuUploader = require("./qiniuUploader.js");
const randomChar = function(l, url = "") {
  const x = "0123456789qwertyuioplkjhgfdsazxcvbnm";
  let tmp = "";
  let time = /* @__PURE__ */ new Date();
  for (let i = 0; i < l; i++) {
    tmp += x.charAt(Math.ceil(Math.random() * 1e8) % x.length);
  }
  return "file/" + url + time.getTime() + tmp;
};
const chooseImage = function(data) {
  return new Promise((resolve, reject) => {
    common_vendor.index.chooseImage({
      count: data.count || 9,
      //默认9
      sizeType: data.sizeType || ["original", "compressed"],
      //可以指定是原图还是压缩图，默认二者都有
      sourceType: data.sourceType || ["album", "camera"],
      //从相册选择
      success: function(res) {
        resolve(res.tempFiles);
      },
      fail: (err) => {
        reject({
          errMsg: err.errMsg,
          errCode: err.errCode,
          statusCode: 0
        });
      }
    });
  });
};
const chooseVideo = function(data) {
  return new Promise((resolve, reject) => {
    common_vendor.index.chooseVideo({
      sourceType: data.sourceType || ["album", "camera"],
      //从相册选择
      compressed: data.compressed || false,
      //是否压缩所选的视频源文件，默认值为 true，需要压缩。
      maxDuration: data.maxDuration || 60,
      //拍摄视频最长拍摄时间，单位秒。最长支持 60 秒。
      camera: data.camera || "back",
      //'front'、'back'，默认'back'
      success: function(res) {
        let files = [{
          path: res.tempFilePath
        }];
        files[0].duration = res.duration;
        files[0].size = res.size;
        files[0].height = res.height;
        files[0].width = res.width;
        resolve(files);
      },
      fail: (err) => {
        reject({
          errMsg: err.errMsg,
          errCode: err.errCode,
          statusCode: 0
        });
      }
    });
  });
};
const qiniuUpload = function(requestInfo, getQnToken) {
  return new Promise((resolve, reject) => {
    if (Array.isArray(requestInfo.files)) {
      let len = requestInfo.files.length;
      let fileList = new Array();
      if (getQnToken) {
        getQnToken((qnRes) => {
          let prefixLen = qnRes.visitPrefix.length;
          if (qnRes.visitPrefix.charAt(prefixLen - 1) == "/") {
            qnRes.visitPrefix = qnRes.visitPrefix.substring(0, prefixLen - 1);
          }
          uploadFile(0);
          function uploadFile(i) {
            let item = requestInfo.files[i];
            let updateUrl = randomChar(10, qnRes.folderPath);
            let fileData = {
              fileIndex: i,
              files: requestInfo.files,
              ...item
            };
            if (item.name) {
              fileData.name = item.name;
              let nameArr = item.name.split(".");
              updateUrl += "." + nameArr[nameArr.length - 1];
            }
            utils_request_upload_qiniuUploader.upload(item.path || item, (res) => {
              fileData.url = res.imageURL;
              requestInfo.onEachUpdate && requestInfo.onEachUpdate({
                url: res.imageURL,
                ...fileData
              });
              fileList.push(res.imageURL);
              if (len - 1 > i) {
                uploadFile(i + 1);
              } else {
                resolve(fileList);
              }
            }, (error) => {
              reject(error);
            }, {
              region: qnRes.region || "SCN",
              //地区
              domain: qnRes.visitPrefix,
              // bucket 域名，下载资源时用到。
              key: updateUrl,
              uptoken: qnRes.token,
              // 由其他程序生成七牛 uptoken
              uptokenURL: "UpTokenURL.com/uptoken"
              // 上传地址
            }, (res) => {
              common_vendor.index.__f__("log", "at utils/request/upload/utils.js:126", requestInfo);
              requestInfo.onProgressUpdate && requestInfo.onProgressUpdate(Object.assign({}, fileData, res));
            });
          }
        });
      } else {
        reject({
          errMsg: "请添加七牛云回调方法：getQnToken",
          statusCode: 0
        });
      }
    } else {
      reject({
        errMsg: "files 必须是数组类型",
        statusCode: 0
      });
    }
  });
};
const urlUpload = function(requestInfo, dataFactory) {
  return new Promise((resolve, reject) => {
    if (requestInfo.header["Content-Type"]) {
      delete requestInfo.header["Content-Type"];
    }
    if (requestInfo.header["content-type"]) {
      delete requestInfo.header["content-type"];
    }
    if (Array.isArray(requestInfo.files)) {
      let fileUpload = function(i) {
        let item = requestInfo.files[i];
        let fileData = {
          fileIndex: i,
          files: requestInfo.files,
          ...item
        };
        let config = {
          url: requestInfo.url,
          filePath: item.path,
          header: requestInfo.header,
          //加入请求头
          name: requestInfo.name || "file",
          success: (response) => {
            if (requestInfo.isFactory && dataFactory) {
              dataFactory({
                ...requestInfo,
                response
              }).then((data) => {
                fileList.push(data);
                requestInfo.onEachUpdate && requestInfo.onEachUpdate({
                  data,
                  ...fileData
                });
                if (len <= i) {
                  resolve(fileList);
                } else {
                  fileUpload(i + 1);
                }
              }, (err) => {
                reject(err);
              });
            } else {
              requestInfo.onEachUpdate && requestInfo.onEachUpdate({
                data: response,
                ...fileData
              });
              fileList.push(response);
              if (len <= i) {
                resolve(fileList);
              } else {
                fileUpload(i + 1);
              }
            }
          },
          fail: (err) => {
            reject(err);
          }
        };
        if (requestInfo.data) {
          config.formData = requestInfo.data;
        }
        const uploadTask = common_vendor.index.uploadFile(config);
        uploadTask.onProgressUpdate((res) => {
          requestInfo.onProgressUpdate && requestInfo.onProgressUpdate(Object.assign({}, fileData, res));
        });
      };
      const len = requestInfo.files.length - 1;
      let fileList = new Array();
      fileUpload(0);
    } else {
      reject({
        errMsg: "files 必须是数组类型",
        statusCode: 0
      });
    }
  });
};
exports.chooseImage = chooseImage;
exports.chooseVideo = chooseVideo;
exports.qiniuUpload = qiniuUpload;
exports.urlUpload = urlUpload;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/utils/request/upload/utils.js.map
