"use strict";
const common_vendor = require("../../common/vendor.js");
const core_mixins_wxofficial = require("../../core/mixins/wxofficial.js");
const core_app = require("../../core/app.js");
const api_goods_index = require("../../api/goods/index.js");
const api_user = require("../../api/user.js");
const api_cart = require("../../api/cart.js");
const common_model_Setting = require("../../common/model/Setting.js");
const common_enum_goods_GoodsType = require("../../common/enum/goods/GoodsType.js");
require("../../common/enum/goods/SpecType.js");
const common_enum_goods_GoodsSource = require("../../common/enum/goods/GoodsSource.js");
const Recommended = () => "../../components/recommended/index.js";
const ShareSheet = () => "../../components/share-sheet/index.js";
const CustomerBtn = () => "../../components/customer-btn/index.js";
const SlideImage = () => "./components/SlideImage.js";
const SkuPopup = () => "./components/SkuPopup.js";
const Comment = () => "./components/Comment.js";
const Service = () => "./components/Service.js";
const Market = () => "./components/Market.js";
const _sfc_main = {
  components: {
    Recommended,
    ShareSheet,
    CustomerBtn,
    SlideImage,
    SkuPopup,
    Comment,
    Service,
    Market
  },
  mixins: [core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 枚举类
      GoodsSourceEnum: common_enum_goods_GoodsSource.GoodsSourceEnum,
      // 正在加载
      isLoading: true,
      // 当前商品ID
      goodsId: null,
      // 商品详情
      goods: {},
      // 购物车总数量
      cartTotal: 0,
      // 显示/隐藏SKU弹窗
      showSkuPopup: false,
      // 模式 1:都显示 2:只显示购物车 3:只显示立即购买
      skuMode: 1,
      // 显示/隐藏分享菜单
      showShareSheet: false,
      // 获取商品海报图api方法
      posterApiCall: api_goods_index.poster,
      // 是否支持加入购物车
      isEnableCart: false,
      // 是否显示在线客服按钮
      isShowCustomerBtn: false
    };
  },
  computed: {
    // 当前页面链接
    pagePath() {
      const params = this.$getShareUrlParams({
        goodsId: this.goodsId
      });
      return `/pages/goods/detail?${params}`;
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    this.onRecordQuery(options);
    this.onRefreshPage();
    this.isShowCustomerBtn = await common_model_Setting.SettingModel.isShowCustomerBtn();
  },
  methods: {
    toCheck() {
      let app = this;
      api_user.info({}, {
        load: true
      }).then((result) => {
        app.userInfo = result.data.userInfo;
        if (app.userInfo.is_vip == 0) {
          common_vendor.index.showToast({
            title: "您还不是会员",
            icon: "none"
          });
        } else {
          common_vendor.index.navigateTo({
            url: "/pagesNew/checkout/index?mode=buyNow&goodsId=" + this.goodsId + "&goodsSkuId=0&goodsNum=1"
          });
        }
        common_vendor.index.__f__("log", "at pagesNew/vip/detail.vue:200", result.data.userInfo);
      });
    },
    // 记录query参数
    onRecordQuery(query) {
      const scene = core_app.getSceneData(query);
      this.goodsId = query.goodsId ? parseInt(query.goodsId) : parseInt(scene.gid);
    },
    // 刷新页面数据
    onRefreshPage() {
      const app = this;
      app.isLoading = true;
      Promise.all([app.getGoodsDetail(), app.getCartTotal()]).then(() => app.setWxofficialShareData()).finally(() => app.isLoading = false);
    },
    // 获取商品信息
    getGoodsDetail() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_goods_index.detail(app.goodsId).then((result) => {
          app.goods = result.data.detail;
          if (app.goods.goods_type == common_enum_goods_GoodsType.GoodsTypeEnum.PHYSICAL.value) {
            app.isEnableCart = true;
          }
          resolve(result);
        }).catch(reject);
      });
    },
    // 获取购物车总数量
    getCartTotal() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_cart.total().then((result) => {
          app.cartTotal = result.data.cartTotal;
          resolve(result);
        }).catch(reject);
      });
    },
    // 更新购物车数量
    onAddCart(total) {
      this.cartTotal = total;
    },
    /**
     * 显示/隐藏SKU弹窗
     * @param {skuMode} 模式 1:都显示 2:只显示购物车 3:只显示立即购买
     */
    onShowSkuPopup(skuMode = 1) {
      const app = this;
      if (app.isEnableCart) {
        app.skuMode = skuMode;
      } else {
        app.skuMode = 3;
      }
      app.showSkuPopup = !app.showSkuPopup;
    },
    // 显示隐藏分享菜单
    onShowShareSheet() {
      this.showShareSheet = !this.showShareSheet;
    },
    // 跳转到首页
    onTargetHome(e) {
      this.$navTo("pages/index/index");
    },
    // 跳转到购物车页
    onTargetCart() {
      this.$navTo("pages/cart/index");
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      const {
        goods
      } = this;
      this.updateShareCardData({
        title: goods.goods_name,
        desc: goods.selling_point,
        imgUrl: goods.goods_image
      });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    return {
      title: this.goods.goods_name,
      path: this.pagePath
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    return {
      title: this.goods.goods_name,
      path: this.pagePath
    };
  }
};
if (!Array) {
  const _component_SlideImage = common_vendor.resolveComponent("SlideImage");
  const _component_Service = common_vendor.resolveComponent("Service");
  const _component_SkuPopup = common_vendor.resolveComponent("SkuPopup");
  const _component_Comment = common_vendor.resolveComponent("Comment");
  const _easycom_mp_html2 = common_vendor.resolveComponent("mp-html");
  const _component_recommended = common_vendor.resolveComponent("recommended");
  const _component_share_sheet = common_vendor.resolveComponent("share-sheet");
  (_component_SlideImage + _component_Service + _component_SkuPopup + _component_Comment + _easycom_mp_html2 + _component_recommended + _component_share_sheet)();
}
const _easycom_mp_html = () => "../../uni_modules/mp-html/components/mp-html/mp-html.js";
if (!Math) {
  _easycom_mp_html();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? {
    b: common_vendor.p({
      video: $data.goods.video,
      videoCover: $data.goods.videoCover,
      images: $data.goods.goods_images
    })
  } : {}, {
    c: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    d: $data.goods.is_user_grade
  }, $data.goods.is_user_grade ? {} : {}, {
    e: $data.goods.line_price_min > 0
  }, $data.goods.line_price_min > 0 ? {
    f: common_vendor.t($data.goods.line_price_min)
  } : {}, {
    g: common_vendor.t($data.goods.goods_sales),
    h: common_vendor.t($data.goods.goods_name),
    i: common_vendor.o(($event) => $options.onShowShareSheet()),
    j: $data.goods.selling_point
  }, $data.goods.selling_point ? {
    k: common_vendor.t($data.goods.selling_point)
  } : {}) : {}, {
    l: $data.goods.spec_type == 20
  }, $data.goods.spec_type == 20 ? {
    m: common_vendor.f($data.goods.specList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.spec_name),
        b: index
      };
    }),
    n: common_vendor.o(($event) => $options.onShowSkuPopup(1))
  } : {}, {
    o: !$data.isLoading
  }, !$data.isLoading ? {
    p: common_vendor.p({
      ["goods-id"]: $data.goodsId
    })
  } : {}, {
    q: !$data.isLoading
  }, !$data.isLoading ? {
    r: common_vendor.o($options.onAddCart),
    s: common_vendor.o(($event) => $data.showSkuPopup = $event),
    t: common_vendor.p({
      skuMode: $data.skuMode,
      goods: $data.goods,
      modelValue: $data.showSkuPopup
    })
  } : {}, {
    v: !$data.isLoading
  }, !$data.isLoading ? {
    w: common_vendor.p({
      ["goods-id"]: $data.goodsId,
      limit: 2
    })
  } : {}, {
    x: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    y: $data.goods.content != ""
  }, $data.goods.content != "" ? {
    z: common_vendor.p({
      content: $data.goods.content
    })
  } : {}) : {}, {
    A: common_vendor.o(($event) => $data.showShareSheet = $event),
    B: common_vendor.p({
      shareTitle: $data.goods.goods_name,
      shareImageUrl: $data.goods.goods_image,
      posterApiCall: $data.posterApiCall,
      posterApiParam: {
        goodsId: $data.goodsId
      },
      modelValue: $data.showShareSheet
    }),
    C: !$data.isLoading,
    D: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a41b41b3"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesNew/vip/detail.js.map
