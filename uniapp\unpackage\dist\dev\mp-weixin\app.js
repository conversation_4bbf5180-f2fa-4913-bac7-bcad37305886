"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const store_index = require("./store/index.js");
const common_model_Store = require("./common/model/Store.js");
const core_app = require("./core/app.js");
const utils_util = require("./utils/util.js");
const core_bootstrap = require("./core/bootstrap.js");
const core_mixins_app = require("./core/mixins/app.js");
const uni_modules_vkUviewUi_index = require("./uni_modules/vk-uview-ui/index.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/category/index.js";
  "./pages/cart/index.js";
  "./pages/user/index.js";
  "./pages/custom/index.js";
  "./pages/search/index.js";
  "./pages/login/index.js";
  "./pages/exch/index.js";
  "./pages/user/bind/index.js";
  "./pages/user/personal/index.js";
  "./pages/article/index.js";
  "./pages/article/detail.js";
  "./pages/article/xieyi.js";
  "./pages/video/index.js";
  "./pages/video/detail.js";
  "./pages/help/index.js";
  "./pages/coupon/index.js";
  "./pages/goods/list.js";
  "./pages/goods/detail.js";
  "./pages/comment/index.js";
  "./pages/my-coupon/index.js";
  "./pages/address/index.js";
  "./pages/address/create.js";
  "./pages/address/update.js";
  "./pages/points/log.js";
  "./pages/wallet/index.js";
  "./pages/wallet/apply.js";
  "./pages/wallet/balance/log.js";
  "./pages/wallet/recharge/index.js";
  "./pages/wallet/recharge/order.js";
  "./pages/checkout/index.js";
  "./pages/checkout/cashier/index.js";
  "./pages/order/center.js";
  "./pages/order/index.js";
  "./pages/order/detail.js";
  "./pages/order/express/index.js";
  "./pages/order/extract/check.js";
  "./pages/order/comment/index.js";
  "./pages/refund/index.js";
  "./pages/refund/detail.js";
  "./pages/refund/apply.js";
  "./pages/shop/extract.js";
  "./pages/shop/detail.js";
  "./pages/dealer/index.js";
  "./pages/dealer/apply.js";
  "./pages/dealer/withdraw/apply.js";
  "./pages/dealer/withdraw/list.js";
  "./pages/dealer/poster.js";
  "./pages/dealer/order.js";
  "./pages/dealer/orderVip.js";
  "./pages/dealer/vip/apply.js";
  "./pages/dealer/team.js";
  "./pages/bargain/index.js";
  "./pages/bargain/goods/index.js";
  "./pages/bargain/task.js";
  "./pages/sharp/index.js";
  "./pages/sharp/goods/index.js";
  "./pages/groupon/index.js";
  "./pages/groupon/goods/index.js";
  "./pages/groupon/task/index.js";
  "./pages/live/index.js";
  "./pages/vip/index.js";
  "./pages/vip/tiyan.js";
  "./pages/invite/index.js";
  "./pages/sign/index.js";
  "./pagesNew/vip/index.js";
  "./pagesNew/vip/category/index.js";
  "./pagesNew/vip/detail.js";
  "./pagesNew/points/index.js";
  "./pagesNew/points/duihuan.js";
  "./pagesNew/points/list.js";
  "./pagesNew/points/apply.js";
  "./pagesNew/points/category/index.js";
  "./pagesNew/points/detail.js";
  "./pagesNew/message/index.js";
  "./pagesNew/tixian/center.js";
  "./pagesNew/checkout-points/index.js";
  "./pagesNew/checkout/index.js";
}
const _sfc_main = {
  /**
   * 全局变量
   */
  globalData: {},
  /**
   * 初始化完成时触发
   */
  onLaunch({ path, query, scene }) {
    this.updateManager();
    this.onStartupQuery(utils_util.isObject(query) ? query : {});
    this.getStoreInfo();
  },
  methods: {
    // app启动参数
    onStartupQuery(query) {
      const scene = core_app.getSceneData(query);
      const refereeId = query.refereeId ? query.refereeId : scene.uid;
      refereeId > 0 && this.setRefereeId(refereeId);
    },
    // 记录推荐人ID
    setRefereeId(refereeId) {
      store_index.store.dispatch("setRefereeId", refereeId);
    },
    // 获取商城基础信息
    getStoreInfo() {
      common_model_Store.StoreModel.data(false);
    },
    // 小程序主动更新
    updateManager() {
      const updateManager = common_vendor.index.getUpdateManager();
      updateManager.onCheckForUpdate((res) => {
      });
      updateManager.onUpdateReady(() => {
        common_vendor.index.showModal({
          title: "更新提示",
          content: "新版本已经准备好，即将重启应用",
          showCancel: false,
          success(res) {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
      updateManager.onUpdateFailed(() => {
        common_vendor.index.showModal({
          title: "更新提示",
          content: "新版本下载失败",
          showCancel: false
        });
      });
    }
  }
};
function createApp() {
  const app = common_vendor.createSSRApp({ ..._sfc_main, store: store_index.store, created: core_bootstrap.YoShop2 });
  app.config.globalProperties.$toast = core_app.showToast;
  app.config.globalProperties.$success = core_app.showSuccess;
  app.config.globalProperties.$error = core_app.showError;
  app.config.globalProperties.$navTo = core_app.navTo;
  app.config.globalProperties.$getShareUrlParams = core_app.getShareUrlParams;
  app.config.globalProperties.$checkModule = core_app.checkModuleKey;
  app.config.globalProperties.$checkModules = core_app.checkModules;
  app.use(uni_modules_vkUviewUi_index.uView);
  app.mixin(core_mixins_app.mixin);
  return { app };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
