"use strict";
const common_vendor = require("../common/vendor.js");
const postfix = "_expiry";
const storage = {
  /**  
   * 设置缓存   
   * @param  {[type]} k [键名]  
   * @param  {[type]} v [键值]  
   * @param  {[type]} t [时间、单位秒]  
   */
  set(k, v, t) {
    common_vendor.index.setStorageSync(k, v);
    const seconds = parseInt(t);
    if (seconds > 0) {
      let timestamp = Date.parse(/* @__PURE__ */ new Date());
      timestamp = timestamp / 1e3 + seconds;
      common_vendor.index.setStorageSync(k + postfix, timestamp + "");
    } else {
      common_vendor.index.removeStorageSync(k + postfix);
    }
  },
  /**  
   * 获取缓存   
   * @param  {[type]} k   [键名]  
   * @param  {[type]} def [获取为空时默认]  
   */
  get(k, def) {
    const deadtime = parseInt(common_vendor.index.getStorageSync(k + postfix));
    if (deadtime) {
      if (parseInt(deadtime) < Date.parse(/* @__PURE__ */ new Date()) / 1e3) {
        if (def) {
          return def;
        } else {
          return false;
        }
      }
    }
    const res = common_vendor.index.getStorageSync(k);
    if (res) {
      return res;
    }
    if (def == void 0 || def == "") {
      def = false;
    }
    return def;
  },
  /**
   * 删除指定缓存
   * @param {Object} k
   */
  remove(k) {
    common_vendor.index.removeStorageSync(k);
    common_vendor.index.removeStorageSync(k + postfix);
  },
  /**  
   * 清理所有缓存  
   * @return {[type]} [description]  
   */
  clear() {
    common_vendor.index.clearStorageSync();
  }
};
exports.storage = storage;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/storage.js.map
