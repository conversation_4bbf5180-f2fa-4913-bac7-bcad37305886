"use strict";
const common_vendor = require("../../common/vendor.js");
const api_user = require("../../api/user.js");
const api_myCoupon = require("../../api/myCoupon.js");
const _sfc_main = {
  data() {
    return {
      // 用户信息
      userInfo: {},
      // 优惠券状态
      couponReceived: false,
      // 抽奖相关
      rotateAngle: 0,
      isRotating: false,
      remainingTimes: 3,
      showResult: false,
      resultContent: "",
      // 奖品配置 - 根据图片实际显示顺序排列
      prizes: [
        { name: "积分+5", type: "points", value: 5 },
        // 0-45度
        { name: "积分+10", type: "points", value: 10 },
        // 45-90度
        { name: "积分+15", type: "points", value: 15 },
        // 90-135度
        { name: "积分+20", type: "points", value: 20 },
        // 135-180度
        { name: "积分+12", type: "points", value: 12 },
        // 180-225度
        { name: "积分+8", type: "points", value: 8 },
        // 225-270度
        { name: "积分+18", type: "points", value: 18 },
        // 270-315度
        { name: "积分+25", type: "points", value: 25 }
        // 315-360度
      ]
    };
  },
  onLoad() {
    this.loadUserData();
  },
  methods: {
    // 加载用户数据
    loadUserData() {
      api_user.info().then((result) => {
        this.userInfo = result.data.userInfo;
      });
      const couponStatus = common_vendor.index.getStorageSync("coupon_received_68_9");
      this.couponReceived = !!couponStatus;
      const today = (/* @__PURE__ */ new Date()).toDateString();
      const todayLottery = common_vendor.index.getStorageSync("lottery_" + today);
      this.remainingTimes = todayLottery ? 3 - todayLottery.times : 3;
    },
    // 领取优惠券
    async receiveCoupon() {
      if (this.couponReceived) {
        common_vendor.index.showToast({
          title: "优惠券已领取",
          icon: "none",
          duration: 1500
        });
        return;
      }
      if (!this.checkVipPermission()) {
        common_vendor.index.showToast({
          title: "仅限会员领取，请先开通会员",
          icon: "none",
          duration: 1500
        });
        setTimeout(() => {
          common_vendor.index.navigateTo({
            url: "/pages/vip/index"
          });
        }, 1500);
        return;
      }
      try {
        common_vendor.index.showLoading({ title: "领取中..." });
        const result = await api_myCoupon.receive(10009);
        if (result.status === 200) {
          this.couponReceived = true;
          common_vendor.index.setStorageSync("coupon_received_68_9", true);
          common_vendor.index.showToast({
            title: "优惠券领取成功！",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateTo({
              url: "/pages/goods/detail?goodsId=10007"
            });
          }, 1500);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/vip/tiyan.vue:173", "优惠券领取失败:", error);
        common_vendor.index.showToast({
          title: error.message || "领取失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 检查VIP权限
    checkVipPermission() {
      var _a, _b;
      if (!((_b = (_a = this.userInfo) == null ? void 0 : _a.grade) == null ? void 0 : _b.weight) || this.userInfo.grade.weight <= 1) {
        return false;
      }
      return true;
    },
    // 开始抽奖
    startLottery() {
      if (!this.checkVipPermission()) {
        common_vendor.index.showToast({
          title: "抽奖仅限会员参与，请先开通会员",
          icon: "none",
          duration: 1500
        });
        setTimeout(() => {
          common_vendor.index.navigateTo({
            url: "/pages/vip/index"
          });
        }, 1500);
        return;
      }
      if (this.remainingTimes <= 0) {
        common_vendor.index.showToast({
          title: "今日抽奖次数已用完",
          icon: "none"
        });
        return;
      }
      if (this.isRotating)
        return;
      const prizeIndex = Math.floor(Math.random() * this.prizes.length);
      const prize = this.prizes[prizeIndex];
      this.isRotating = false;
      this.rotateAngle = 0;
      setTimeout(() => {
        this.isRotating = true;
        const sectorCenterAngle = prizeIndex * 45 + 22.5;
        const targetAngle = 360 * 5 + (360 - sectorCenterAngle);
        this.rotateAngle = targetAngle;
      }, 100);
      setTimeout(() => {
        this.isRotating = false;
        this.showPrizeResult(prize);
        this.updateLotteryTimes();
      }, 2e3);
    },
    // 显示中奖结果
    showPrizeResult(prize) {
      this.resultContent = `恭喜您获得：${prize.name}！`;
      this.showResult = true;
    },
    // 更新抽奖次数
    updateLotteryTimes() {
      this.remainingTimes--;
      const today = (/* @__PURE__ */ new Date()).toDateString();
      const todayLottery = common_vendor.index.getStorageSync("lottery_" + today) || { times: 0 };
      todayLottery.times++;
      common_vendor.index.setStorageSync("lottery_" + today, todayLottery);
    },
    // 关闭结果弹窗
    closeResult() {
      this.showResult = false;
    },
    // 跳转到充值页面
    goToRecharge() {
      common_vendor.index.navigateTo({
        url: "/pages/vip/index"
      });
    },
    // 跳转到积分兑换页面
    goToExchange() {
      common_vendor.index.navigateTo({
        url: "/pagesNew/points/category/index"
      });
    }
  }
};
if (!Array) {
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  _easycom_u_modal2();
}
const _easycom_u_modal = () => "../../uni_modules/vk-uview-ui/components/u-modal/u-modal.js";
if (!Math) {
  _easycom_u_modal();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.couponReceived ? "已领取" : "待领取"),
    b: common_vendor.o((...args) => $options.receiveCoupon && $options.receiveCoupon(...args)),
    c: common_vendor.t($data.remainingTimes),
    d: common_vendor.f($data.prizes, (prize, index, i0) => {
      return {
        a: common_vendor.t(prize.name),
        b: index,
        c: common_vendor.n("sector-" + index)
      };
    }),
    e: !$data.isRotating ? 1 : "",
    f: "rotate(" + $data.rotateAngle + "deg)",
    g: common_vendor.t($data.isRotating ? "抽奖中..." : "开始抽奖"),
    h: $data.remainingTimes <= 0 || $data.isRotating ? 1 : "",
    i: common_vendor.o((...args) => $options.startLottery && $options.startLottery(...args)),
    j: $data.remainingTimes <= 0 || $data.isRotating,
    k: common_vendor.o((...args) => $options.goToRecharge && $options.goToRecharge(...args)),
    l: common_vendor.o((...args) => $options.goToExchange && $options.goToExchange(...args)),
    m: common_vendor.o($options.closeResult),
    n: common_vendor.o(($event) => $data.showResult = $event),
    o: common_vendor.p({
      content: $data.resultContent,
      ["show-cancel-button"]: false,
      ["confirm-text"]: "确定",
      modelValue: $data.showResult
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-688ea882"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/vip/tiyan.js.map
