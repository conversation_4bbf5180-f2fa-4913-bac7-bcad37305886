
page {
    background: #fff;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.widget-list.data-v-ace768b5 {
  padding: 16rpx 20rpx 40rpx 20rpx;
  box-sizing: border-box;
}
.widget__detail.data-v-ace768b5 {
  padding: 26rpx 15rpx;
  box-sizing: border-box;
  font-size: 26rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.widget__detail .detail__money.data-v-ace768b5 {
  font-size: 30rpx;
}
.widget__detail .detail__reason.data-v-ace768b5 {
  color: #8e84fc;
}
.pops-content.data-v-ace768b5 {
  padding: 30rpx 48rpx;
  font-size: 28rpx;
  line-height: 44rpx;
  text-align: left;
  color: #606266;
  height: 220rpx;
  box-sizing: border-box;
}