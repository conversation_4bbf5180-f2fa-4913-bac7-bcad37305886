"use strict";
const common_vendor = require("../../common/vendor.js");
const api_xj_exchange = require("../../api/xj/exchange.js");
const api_xj_sign = require("../../api/xj/sign.js");
const _sfc_main = {
  data() {
    return {
      userPoints: 0,
      continuousSignDays: 0,
      products: [
        {
          product_id: "10007",
          name: "有机旱田雪花粉",
          required_sign_days: 7,
          required_points: 600,
          has_exchanged: false,
          can_exchange: false
        },
        {
          product_id: "10008",
          name: "有机福禄喜米",
          required_sign_days: 14,
          required_points: 1200,
          has_exchanged: false,
          can_exchange: false
        }
      ],
      loading: false,
      signList: [],
      showAddressModal: false,
      currentProduct: null,
      addressForm: {
        name: "",
        phone: "",
        address: ""
      }
    };
  },
  onShow() {
    this.loadExchangeData();
  },
  methods: {
    // 加载兑换页面数据
    async loadExchangeData() {
      try {
        common_vendor.index.showLoading({ title: "加载中..." });
        const [exchangeResult, signResult] = await Promise.all([
          api_xj_exchange.index(),
          api_xj_sign.list()
        ]);
        if (exchangeResult.status === 200) {
          const data = exchangeResult.data;
          this.userPoints = data.user_points;
          if (data.exchanged_products) {
            data.exchanged_products.forEach((productKey) => {
              const product = this.products.find((p) => p.product_id === String(productKey));
              if (product) {
                product.has_exchanged = true;
              }
            });
          }
        }
        if (signResult.status === 200) {
          this.signList = signResult.data.list || [];
          this.continuousSignDays = this.calculateContinuousSignDays();
          this.products = this.products.map((product) => ({
            ...product,
            can_exchange: !product.has_exchanged && this.continuousSignDays >= product.required_sign_days && this.userPoints >= product.required_points
          }));
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/exch/index.vue:195", "加载数据失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      } finally {
        try {
          common_vendor.index.hideLoading();
        } catch (e) {
          common_vendor.index.__f__("log", "at pages/exch/index.vue:204", "hideLoading error:", e);
        }
      }
    },
    // 处理兑换
    handleExchange(product) {
      if (!product.can_exchange || this.loading) {
        return;
      }
      this.currentProduct = product;
      this.showAddressModal = true;
    },
    // 关闭弹窗
    closeModal() {
      this.showAddressModal = false;
      this.currentProduct = null;
      this.addressForm = {
        name: "",
        phone: "",
        address: ""
      };
    },
    // 确认兑换
    async confirmExchange() {
      if (!this.addressForm.name.trim()) {
        common_vendor.index.showToast({ title: "请输入收货人姓名", icon: "none" });
        return;
      }
      if (!this.addressForm.phone.trim()) {
        common_vendor.index.showToast({ title: "请输入手机号码", icon: "none" });
        return;
      }
      if (!this.addressForm.address.trim()) {
        common_vendor.index.showToast({ title: "请输入收货地址", icon: "none" });
        return;
      }
      try {
        this.loading = true;
        common_vendor.index.showLoading({ title: "兑换中..." });
        const result = await api_xj_exchange.exchange({
          product_id: this.currentProduct.product_id,
          name: this.addressForm.name,
          phone: this.addressForm.phone,
          address: this.addressForm.address
        });
        if (result.status === 200) {
          common_vendor.index.showToast({
            title: "兑换成功！",
            icon: "success"
          });
          this.closeModal();
          setTimeout(() => {
            common_vendor.index.navigateTo({
              url: "/pages/order/index?status=1"
              // 待发货状态
            });
          }, 1500);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/exch/index.vue:273", "兑换失败:", error);
        common_vendor.index.showToast({
          title: error.message || "兑换失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
        common_vendor.index.hideLoading();
      }
    },
    // 获取按钮文本
    getButtonText(product) {
      if (product.has_exchanged) {
        return "已兑换";
      }
      if (this.continuousSignDays < product.required_sign_days) {
        return `还需签到${product.required_sign_days - this.continuousSignDays}天`;
      }
      if (this.userPoints < product.required_points) {
        return `积分不足`;
      }
      return "立即兑换";
    },
    // 获取签到进度百分比
    getSignProgress(product) {
      return Math.min(100, this.continuousSignDays / product.required_sign_days * 100);
    },
    // 获取积分进度百分比
    getPointsProgress(product) {
      return Math.min(100, this.userPoints / product.required_points * 100);
    },
    // 计算连续签到天数 - 兼容iOS设备
    calculateContinuousSignDays() {
      if (!this.signList || this.signList.length === 0) {
        return 0;
      }
      let dates = this.signList.map((dateStr) => {
        const parts = dateStr.split("-");
        if (parts.length === 3) {
          const year = parts[0];
          const month = parts[1].padStart(2, "0");
          const day = parts[2].padStart(2, "0");
          return (/* @__PURE__ */ new Date(`${year}/${month}/${day}`)).getTime();
        }
        return new Date(dateStr.replace(/-/g, "/")).getTime();
      }).filter((time) => !isNaN(time)).sort((a, b) => b - a);
      if (dates.length === 0) {
        return 0;
      }
      let continuousDays = 1;
      const oneDayMs = 24 * 60 * 60 * 1e3;
      for (let i = 0; i < dates.length - 1; i++) {
        const dayDiff = Math.round((dates[i] - dates[i + 1]) / oneDayMs);
        if (dayDiff === 1) {
          continuousDays++;
        } else if (dayDiff > 1) {
          break;
        }
      }
      return continuousDays;
    },
    // 跳转到视频页面
    goToVideo() {
      common_vendor.index.navigateTo({
        url: "/pages/video/index"
      });
    },
    // 跳转到签到中心
    goToSign() {
      common_vendor.index.navigateTo({
        url: "/pages/sign/index"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.userPoints),
    b: common_vendor.o((...args) => $options.goToVideo && $options.goToVideo(...args)),
    c: common_vendor.t($data.continuousSignDays),
    d: common_vendor.o((...args) => $options.goToSign && $options.goToSign(...args)),
    e: common_vendor.f($data.products, (product, k0, i0) => {
      return {
        a: common_vendor.t(product.name),
        b: common_vendor.t(product.required_sign_days),
        c: common_vendor.t(product.required_points),
        d: common_vendor.t($options.getButtonText(product)),
        e: !product.can_exchange ? 1 : "",
        f: product.has_exchanged ? 1 : "",
        g: !product.can_exchange,
        h: common_vendor.o(($event) => $options.handleExchange(product), product.product_id),
        i: $options.getSignProgress(product) + "%",
        j: common_vendor.t(product.required_sign_days),
        k: $options.getPointsProgress(product) + "%",
        l: common_vendor.t(product.required_points),
        m: product.product_id,
        n: product.has_exchanged ? 1 : "",
        o: product.can_exchange ? 1 : ""
      };
    }),
    f: common_vendor.t($data.continuousSignDays),
    g: common_vendor.t($data.userPoints),
    h: $data.showAddressModal
  }, $data.showAddressModal ? {
    i: common_vendor.o((...args) => $options.closeModal && $options.closeModal(...args)),
    j: $data.addressForm.name,
    k: common_vendor.o(($event) => $data.addressForm.name = $event.detail.value),
    l: $data.addressForm.phone,
    m: common_vendor.o(($event) => $data.addressForm.phone = $event.detail.value),
    n: $data.addressForm.address,
    o: common_vendor.o(($event) => $data.addressForm.address = $event.detail.value),
    p: common_vendor.o((...args) => $options.closeModal && $options.closeModal(...args)),
    q: common_vendor.o((...args) => $options.confirmExchange && $options.confirmExchange(...args)),
    r: common_vendor.o(() => {
    }),
    s: common_vendor.o((...args) => $options.closeModal && $options.closeModal(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-5a4b5f19"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/exch/index.js.map
