
page {
		background-color: black;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.swiper-box.data-v-ade757f3 {
  width: 100%;
  /* 设置宽度 */
  height: 900rpx;
  /* 设置高度，确保可以垂直滚动 */
}
.swiper-item.data-v-ade757f3 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  /* 让每个滑动项填满整个swiper的高度 */
}
.container.data-v-ade757f3 {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.video.data-v-ade757f3 {
  width: 100%;
  /* 或者你想要的任何宽度 */
  height: 100%;
  /* 或者你想要的任何高度 */
}
.article-content.data-v-ade757f3 {
  font-size: 28rpx;
}
.video-duration.data-v-ade757f3 {
  position: absolute;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  z-index: 10;
}
.video-overlay.data-v-ade757f3 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 20;
}