/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
page {
  background: #F5F5F8;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-d25fc2e1 {
  padding-bottom: calc(constant(safe-area-inset-bottom) + 96rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 96rpx);
}
.banner.data-v-d25fc2e1 {
  z-index: 0;
}
.banner .image.data-v-d25fc2e1 {
  display: block;
  width: 100%;
}
.groupon-hall.data-v-d25fc2e1 {
  padding: 0 24rpx;
  position: relative;
  z-index: 1;
}
.groupon-hall.active-list.data-v-d25fc2e1 {
  margin-top: -80rpx;
}
.groupon-hall.my-list.data-v-d25fc2e1 {
  padding-top: 30rpx;
}
.goods-item--container.data-v-d25fc2e1 {
  margin-bottom: 20rpx;
}
.goods-item--container.data-v-d25fc2e1:last-child {
  margin-bottom: 0 !important;
}
.goods-item.data-v-d25fc2e1 {
  padding: 28rpx 24rpx;
  display: flex;
  background: #fff;
  box-sizing: border-box;
  border-radius: 14rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.07);
}
.goods-item-left.data-v-d25fc2e1 {
  position: relative;
  background: #fff;
  margin-right: 20rpx;
}
.goods-item-left .label.data-v-d25fc2e1 {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  background: linear-gradient(to right, #ffa600, #f5b914);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 8rpx;
  border-radius: 8rpx;
}
.goods-item-left .image.data-v-d25fc2e1 {
  display: block;
  width: 220rpx;
  height: 220rpx;
  border-radius: 10rpx;
}
.goods-item-right.data-v-d25fc2e1 {
  position: relative;
  flex: 1;
}
.goods-item-right .goods-name.data-v-d25fc2e1 {
  display: block;
  width: 100%;
  color: #333;
  font-size: 28rpx;
  min-height: 68rpx;
  line-height: 1.3;
}
.goods-item-desc.data-v-d25fc2e1 {
  margin-top: 20rpx;
}
.goods-item-desc .desc_situation.data-v-d25fc2e1 {
  font-size: 26rpx;
  line-height: 1.3;
  color: var(--main-bg);
  margin-top: 20rpx;
}
.goods-item-desc .state-tag.data-v-d25fc2e1 {
  display: inline-block;
  margin-right: 14rpx;
}
.goods-item-desc .desc-footer.data-v-d25fc2e1 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  right: 0rpx;
  bottom: 0rpx;
  min-height: 44rpx;
}
.goods-item-desc .desc-footer .item-status.data-v-d25fc2e1 {
  color: var(--main-bg);
}
.goods-item-desc .desc-footer .item-prices.data-v-d25fc2e1 {
  padding-right: 6rpx;
}
.goods-item-desc .desc-footer .item-prices .price-x.data-v-d25fc2e1 {
  margin-right: 14rpx;
  color: var(--main-bg);
  font-size: 28rpx;
}
.goods-item-desc .desc-footer .item-prices .price-y.data-v-d25fc2e1 {
  color: #999;
  text-decoration: line-through;
  font-size: 24rpx;
}
.goods-item-desc .desc-footer .settlement.data-v-d25fc2e1 {
  padding: 0 30rpx;
  line-height: 56rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 40rpx;
  color: #fff;
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
}
.footer-fixed.data-v-d25fc2e1 {
  position: fixed;
  bottom: var(--window-bottom);
  left: 0;
  right: 0;
  z-index: 11;
  box-shadow: 0 -4rpx 40rpx 0 rgba(151, 151, 151, 0.24);
  background: #fff;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.footer-container.data-v-d25fc2e1 {
  display: flex;
  align-items: center;
  height: 96rpx;
}
.tabbar-item.data-v-d25fc2e1 {
  font-size: 30rpx;
}
.tabbar-item.active .tabbar-item-content.data-v-d25fc2e1 {
  color: var(--main-bg);
}
.tabbar-item .tabbar-item-icon.data-v-d25fc2e1 {
  margin-right: 15rpx;
}
.tabbar-item__divider.data-v-d25fc2e1 {
  padding: 22rpx 0;
}
.divider-line.data-v-d25fc2e1 {
  width: 1rpx;
  height: 62rpx;
  background: #ddd;
}