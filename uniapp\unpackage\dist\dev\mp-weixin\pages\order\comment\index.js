"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_upload = require("../../../api/upload.js");
const api_order_comment = require("../../../api/order/comment.js");
const maxImageLength = 6;
const _sfc_main = {
  data() {
    return {
      // 正在加载
      isLoading: true,
      // 当前订单ID
      orderId: null,
      // 待评价商品列表
      goodsList: [],
      // 表单数据
      formData: [],
      // 最大图片数量
      maxImageLength,
      // 按钮禁用
      disabled: false
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad({ orderId }) {
    this.orderId = orderId;
    this.getGoodsList();
  },
  methods: {
    // 获取待评价商品列表
    getGoodsList() {
      const app = this;
      app.isLoading = true;
      api_order_comment.list(app.orderId).then((result) => {
        app.goodsList = result.data.goodsList;
        app.initFormData();
        app.isLoading = false;
      });
    },
    // 初始化form数据
    initFormData() {
      const { goodsList } = this;
      const formData = goodsList.map((item) => {
        return {
          goods_id: item.goods_id,
          order_goods_id: item.order_goods_id,
          score: 10,
          content: "",
          imageList: [],
          uploaded: []
        };
      });
      this.formData = formData;
    },
    // 设置评分
    setScore(index, score) {
      this.formData[index].score = score;
    },
    // 选择图片
    chooseImage(index) {
      const app = this;
      const oldImageList = app.formData[index].imageList;
      common_vendor.index.chooseImage({
        count: maxImageLength - oldImageList.length,
        sizeType: ["original", "compressed"],
        // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"],
        // 可以指定来源是相册还是相机，默认二者都有
        success({ tempFiles }) {
          app.formData[index].imageList = oldImageList.concat(tempFiles);
        }
      });
    },
    // 删除图片
    deleteImage(index, imageIndex) {
      this.formData[index].imageList.splice(imageIndex, 1);
    },
    // 表单提交
    handleSubmit() {
      const app = this;
      if (app.disabled === true)
        return false;
      app.disabled = true;
      const imagesLength = app.getImagesLength();
      if (imagesLength > 0) {
        app.uploadFile().then(() => {
          common_vendor.index.__f__("log", "at pages/order/comment/index.vue:184", "then");
          app.onSubmit();
        }).catch((err) => {
          common_vendor.index.__f__("log", "at pages/order/comment/index.vue:188", "catch");
          app.disabled = false;
          if (err.statusCode !== 0) {
            app.$toast(err.errMsg);
          }
          common_vendor.index.__f__("log", "at pages/order/comment/index.vue:193", "err", err);
        });
      } else {
        app.onSubmit();
      }
    },
    // 统计图片数量
    getImagesLength() {
      const { formData } = this;
      let imagesLength = 0;
      formData.forEach((item) => {
        if (item.content.trim()) {
          imagesLength += item.imageList.length;
        }
      });
      return imagesLength;
    },
    // 提交到后端
    onSubmit() {
      const app = this;
      api_order_comment.submit(app.orderId, app.formData).then((result) => {
        app.$toast(result.message);
        setTimeout(() => {
          app.disabled = false;
          common_vendor.index.navigateBack();
        }, 1500);
      }).catch((err) => app.disabled = false);
    },
    // 上传图片
    uploadFile() {
      const app = this;
      const { formData } = app;
      const files = [];
      formData.forEach((item, index) => {
        if (item.content.trim() && item.imageList.length) {
          const images = item.imageList.map((image) => image);
          files.push({ formDataIndex: index, images });
        }
      });
      return new Promise((resolve, reject) => {
        Promise.all(files.map((file, index) => {
          return new Promise((resolve2, reject2) => {
            api_upload.image(file.images).then((fileIds) => {
              app.formData[index].uploaded = fileIds;
              resolve2(fileIds);
            }).catch(reject2);
          });
        })).then(resolve, reject);
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? {
    b: common_vendor.f($data.goodsList, (item, index, i0) => {
      return common_vendor.e({
        a: item.goods_image,
        b: common_vendor.t(item.goods_name),
        c: common_vendor.f(item.goods_props, (props, idx, i1) => {
          return {
            a: common_vendor.t(props.value.name),
            b: idx
          };
        }),
        d: common_vendor.t(item.goods_price),
        e: common_vendor.t(item.total_num),
        f: $data.formData[index].score == 10 ? 1 : "",
        g: common_vendor.o(($event) => $options.setScore(index, 10), index),
        h: $data.formData[index].score == 20 ? 1 : "",
        i: common_vendor.o(($event) => $options.setScore(index, 20), index),
        j: $data.formData[index].score == 30 ? 1 : "",
        k: common_vendor.o(($event) => $options.setScore(index, 30), index),
        l: $data.formData[index].content,
        m: common_vendor.o(($event) => $data.formData[index].content = $event.detail.value, index),
        n: common_vendor.f($data.formData[index].imageList, (image, imageIndex, i1) => {
          return {
            a: common_vendor.o(($event) => $options.deleteImage(index, imageIndex), imageIndex),
            b: image.path,
            c: imageIndex
          };
        }),
        o: !$data.formData[index].imageList || $data.formData[index].imageList.length < $data.maxImageLength
      }, !$data.formData[index].imageList || $data.formData[index].imageList.length < $data.maxImageLength ? {
        p: common_vendor.o(($event) => $options.chooseImage(index), index)
      } : {}, {
        q: index
      });
    }),
    c: $data.disabled ? 1 : "",
    d: common_vendor.o(($event) => $options.handleSubmit()),
    e: common_vendor.s(_ctx.appThemeStyle)
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1dbe63fd"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/order/comment/index.js.map
