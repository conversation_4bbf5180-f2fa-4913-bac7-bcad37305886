/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.type.data-v-17a44f9d {
  color: #ff0000;
  font-weight: bold;
  padding-top: 10rpx;
}
.order-item.data-v-17a44f9d {
  margin: 20rpx auto 20rpx auto;
  padding: 30rpx 30rpx;
  width: 94%;
  box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);
  border-radius: 16rpx;
  background: #fff;
}
.item-top.data-v-17a44f9d {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  margin-bottom: 40rpx;
}
.item-top .order-time.data-v-17a44f9d {
  color: #777;
}
.item-top .state-text.data-v-17a44f9d {
  color: var(--main-bg);
}
.goods-list .goods-item.data-v-17a44f9d {
  display: flex;
  margin-bottom: 40rpx;
}
.goods-list .goods-item .goods-image.data-v-17a44f9d {
  width: 180rpx;
  height: 180rpx;
}
.goods-list .goods-item .goods-image .image.data-v-17a44f9d {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.goods-list .goods-item .goods-content.data-v-17a44f9d {
  flex: 1;
  padding-left: 16rpx;
}
.goods-list .goods-item .goods-content .goods-title.data-v-17a44f9d {
  font-size: 26rpx;
  max-height: 76rpx;
}
.goods-list .goods-item .goods-content .goods-props.data-v-17a44f9d {
  margin-top: 14rpx;
  color: #ababab;
  font-size: 24rpx;
  overflow: hidden;
}
.goods-list .goods-item .goods-content .goods-props .goods-props-item.data-v-17a44f9d {
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
  background-color: #fcfcfc;
}
.goods-list .goods-item .goods-trade.data-v-17a44f9d {
  padding-top: 16rpx;
  width: 150rpx;
  text-align: right;
  color: #999;
  font-size: 26rpx;
}
.goods-list .goods-item .goods-trade .goods-price.data-v-17a44f9d {
  vertical-align: bottom;
  margin-bottom: 16rpx;
}
.goods-list .goods-item .goods-trade .goods-price .unit.data-v-17a44f9d {
  margin-right: -2rpx;
  font-size: 24rpx;
}
.order-total.data-v-17a44f9d {
  font-size: 26rpx;
  vertical-align: bottom;
  text-align: right;
  height: 40rpx;
  margin-bottom: 30rpx;
}
.order-total .unit.data-v-17a44f9d {
  margin-left: 8rpx;
  margin-right: -2rpx;
  font-size: 26rpx;
}
.order-total .money.data-v-17a44f9d {
  font-size: 28rpx;
}
.order-handle .btn-group .btn-item.data-v-17a44f9d {
  border-radius: 10rpx;
  padding: 8rpx 20rpx;
  margin-left: 15rpx;
  font-size: 26rpx;
  float: right;
  color: #383838;
  border: 1rpx solid #a8a8a8;
}
.order-handle .btn-group .btn-item.data-v-17a44f9d:last-child {
  margin-left: 0;
}
.order-handle .btn-group .btn-item.active.data-v-17a44f9d {
  color: var(--main-bg);
  border: 1rpx solid var(--main-bg);
}
.qrcode-popup.data-v-17a44f9d {
  padding: 36rpx 30rpx;
}
.qrcode-popup .title.data-v-17a44f9d {
  font-size: 30rpx;
  margin-bottom: 26rpx;
  font-weight: bold;
  text-align: center;
}
.qrcode-popup .pop-content.data-v-17a44f9d {
  min-height: 260rpx;
  padding: 0 10rpx;
}
.qrcode-popup .pop-content .image.data-v-17a44f9d {
  display: block;
  width: 510rpx;
  height: 510rpx;
}