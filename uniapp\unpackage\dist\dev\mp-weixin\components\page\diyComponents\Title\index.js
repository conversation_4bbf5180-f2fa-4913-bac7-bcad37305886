"use strict";
const components_page_diyComponents_mixin = require("../mixin.js");
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemStyle: Object,
    params: Object
  },
  mixins: [components_page_diyComponents_mixin.mixin],
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {}
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($props.params.title),
    b: $props.itemStyle.titleTextColor,
    c: `${$props.params.titleFontSize * 2}rpx`,
    d: $props.params.titleFontWeight,
    e: $props.params.more.enable
  }, $props.params.more.enable ? common_vendor.e({
    f: common_vendor.t($props.params.more.text),
    g: $props.params.more.enableIcon
  }, $props.params.more.enableIcon ? {} : {}, {
    h: $props.itemStyle.moreTextColor,
    i: common_vendor.o(($event) => _ctx.onLink($props.params.more.link))
  }) : {}, {
    j: common_vendor.t($props.params.desc),
    k: $props.itemStyle.descTextColor,
    l: `${$props.params.descFontSize * 2}rpx`,
    m: $props.params.descFontWeight,
    n: `${$props.itemStyle.paddingY * 2}rpx 30rpx`,
    o: $props.itemStyle.background
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c74c9af2"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/components/page/diyComponents/Title/index.js.map
