/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.market-info.data-v-f67ec2c2 {
  margin-top: 40rpx;
}
.market-info .draw-item.data-v-f67ec2c2 {
  display: flex;
  align-items: flex-start;
  font-size: 26rpx;
  margin-bottom: 34rpx;
}
.market-info .draw-item.center.data-v-f67ec2c2 {
  align-items: center;
}
.market-info .draw-item.data-v-f67ec2c2:last-child {
  margin-bottom: 0;
}
.market-info .draw-item .draw-left.data-v-f67ec2c2 {
  color: #888;
}
.market-info .draw-item .draw-content.data-v-f67ec2c2 {
  flex: 1;
}
.market-info .draw-item .draw-right.data-v-f67ec2c2 {
  color: #999;
}
.market-info .coupon-list.data-v-f67ec2c2 {
  display: flex;
  overflow: hidden;
  max-width: 564rpx;
  overflow: hidden;
}
.market-info .coupon-list .coupon-item.data-v-f67ec2c2 {
  font-size: 24rpx;
  margin-right: 16rpx;
  position: relative;
}
.market-info .coupon-list .coupon-item.data-v-f67ec2c2:last-child {
  margin-right: 0;
}
.market-info .coupon-list .coupon-item .tag-wrapper.data-v-f67ec2c2 {
  padding: 2rpx 14rpx;
  position: relative;
  border: 1px solid #f00;
  box-sizing: border-box;
  -webkit-mask: radial-gradient(circle at left, transparent 0px, #000 0), radial-gradient(circle at right, transparent 0px, #000 0);
          mask: radial-gradient(circle at left, transparent 0px, #000 0), radial-gradient(circle at right, transparent 0px, #000 0);
  -webkit-mask-size: 50% 100%;
          mask-size: 50% 100%;
  -webkit-mask-position: left, right;
          mask-position: left, right;
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
}
.market-info .coupon-list .coupon-item .tag-wrapper.data-v-f67ec2c2::before, .market-info .coupon-list .coupon-item .tag-wrapper.data-v-f67ec2c2::after {
  content: "";
  position: absolute;
  top: 10rpx;
  border: 1rpx solid #f00;
  width: 12rpx;
  height: 12rpx;
  border-radius: 100%;
  background: #ffffff;
}
.market-info .coupon-list .coupon-item .tag-wrapper.data-v-f67ec2c2::before {
  right: -10rpx;
}
.market-info .coupon-list .coupon-item .tag-wrapper.data-v-f67ec2c2::after {
  left: -10rpx;
}
.market-info .coupon-list .coupon-item .tag-wrapper .tag-text.data-v-f67ec2c2 {
  color: #f00;
  font-size: 22rpx;
  white-space: nowrap;
}
.market-info .market-list .market-item.data-v-f67ec2c2 {
  display: flex;
  margin-bottom: 20rpx;
}
.market-info .market-list .market-item.data-v-f67ec2c2:last-child {
  margin-bottom: 0;
}
.market-info .market-list .market-item .market-content.data-v-f67ec2c2 {
  margin-left: 12rpx;
  color: #424242;
}
.popup-content.data-v-f67ec2c2 {
  padding: 24rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 24rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
}
.popup-content .title.data-v-f67ec2c2 {
  font-size: 30rpx;
  margin-bottom: 50rpx;
  font-weight: bold;
  text-align: center;
}
.popup-content .content-scroll.data-v-f67ec2c2 {
  min-height: 500rpx;
  max-height: 800rpx;
}
.popup-content .market-title.data-v-f67ec2c2 {
  font-size: 26rpx;
  color: #888;
  margin-bottom: 20rpx;
  margin-top: 50rpx;
}
.popup-content .market-title.data-v-f67ec2c2:first-child {
  margin-top: 0;
}
.popup-content .market-item.data-v-f67ec2c2 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.popup-content .market-item.data-v-f67ec2c2:last-child {
  margin-bottom: 0;
}
.popup-content .market-item .item-left.data-v-f67ec2c2 {
  margin-right: 40rpx;
}
.popup-content .market-item .item-content.data-v-f67ec2c2 {
  flex: 1;
}
.popup-content .coupon-list.data-v-f67ec2c2 {
  padding: 20rpx 20rpx;
}
.popup-content .coupon-item.data-v-f67ec2c2 {
  margin-bottom: 22rpx;
  font-size: 24rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 30rpx 0 rgba(0, 0, 0, 0.04);
  position: relative;
}
.popup-content .item-wrapper.data-v-f67ec2c2 {
  display: flex;
  align-items: center;
}
.popup-content .item-wrapper.disable .coupon-tag.data-v-f67ec2c2 {
  background: linear-gradient(-113deg, #bdbdbd, #a2a1a2);
}
.popup-content .item-wrapper.disable .coupon-reduce.data-v-f67ec2c2 {
  color: #757575;
}
.popup-content .item-wrapper.disable .state-text.data-v-f67ec2c2 {
  color: #757575;
}
.popup-content .coupon-tag.data-v-f67ec2c2 {
  position: absolute;
  left: 0;
  top: 0;
  color: #fff;
  width: 96rpx;
  text-align: center;
  font-size: 22rpx;
  border-radius: 12rpx 0 12rpx 0;
  font-weight: 500;
  height: 34rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
}
.popup-content .coupon-left.data-v-f67ec2c2 {
  width: 242rpx;
  text-align: center;
  display: flex;
  flex-flow: column;
  justify-content: center;
  position: relative;
}
.popup-content .coupon-left .coupon-reduce.data-v-f67ec2c2 {
  color: var(--main-bg);
}
.popup-content .coupon-left .coupon-reduce-unit.data-v-f67ec2c2 {
  display: inline-block;
  margin-right: -4rpx;
  font-size: 32rpx;
  font-weight: 600;
}
.popup-content .coupon-left .coupon-reduce-amount.data-v-f67ec2c2 {
  display: inline-block;
}
.popup-content .coupon-left .coupon-reduce-amount .value.data-v-f67ec2c2 {
  font-size: 48rpx;
}
.popup-content .coupon-left .coupon-hint.data-v-f67ec2c2 {
  margin-top: 12rpx;
  color: #757575;
  font-size: 24rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.popup-content .coupon-content.data-v-f67ec2c2 {
  flex: 1;
  padding: 32rpx 0;
  position: relative;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  align-items: flex-start;
  justify-content: space-between;
}
.popup-content .coupon-content .coupon-name.data-v-f67ec2c2 {
  color: #212121;
  font-size: 28rpx;
  font-weight: 500;
}
.popup-content .coupon-content .coupon-middle.data-v-f67ec2c2 {
  flex: 1;
  padding-top: 12rpx;
  margin-bottom: 26rpx;
}
.popup-content .coupon-content .coupon-middle .coupon-expire.data-v-f67ec2c2 {
  color: #757575;
  font-size: 24rpx;
}
.popup-content .coupon-content .coupon-expand.data-v-f67ec2c2 {
  display: flex;
  align-items: center;
  color: #9e9e9e;
  font-size: 24rpx;
}
.popup-content .coupon-content .coupon-expand-arrow.data-v-f67ec2c2 {
  margin-top: 4rpx;
  font-size: 24rpx;
  display: inline-block;
  vertical-align: initial;
  transform: rotate(0);
  margin-left: 8rpx;
  transition: all 0.15s ease-in-out;
}
.popup-content .coupon-content .coupon-expand-arrow.expand.data-v-f67ec2c2 {
  transform: rotate(180deg);
}
.popup-content .coupon-right.data-v-f67ec2c2 {
  padding-right: 38rpx;
}
.popup-content .coupon-right .btn-receive.data-v-f67ec2c2,
.popup-content .coupon-right .state-text.data-v-f67ec2c2 {
  text-align: center;
  width: 100rpx;
  padding: 15rpx 0;
}
.popup-content .coupon-right .btn-receive.data-v-f67ec2c2 {
  font-size: 23rpx;
  line-height: 1;
  font-weight: 500;
  border-radius: 8rpx;
  cursor: pointer;
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
}
.popup-content .coupon-expand-rules.data-v-f67ec2c2 {
  display: none;
  position: relative;
}
.popup-content .coupon-expand-rules.expand.data-v-f67ec2c2 {
  top: -30rpx;
  display: block;
}
.popup-content .coupon-expand-rules-content.data-v-f67ec2c2 {
  padding: 8rpx 30rpx 8rpx 242rpx;
  font-weight: 400;
  color: #9e9e9e;
  line-height: 36rpx;
}
.popup-content .coupon-expand-rules-content .pre.data-v-f67ec2c2 {
  font-family: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
}
.pops-content.data-v-f67ec2c2 {
  padding: 30rpx 48rpx;
  font-size: 28rpx;
  line-height: 44rpx;
  text-align: left;
  color: #606266;
  max-height: 640rpx;
  box-sizing: border-box;
}