
page {
		background: #F2D8B2;
		width: 100%;
		height: 100%;
}
.upbtn {
		margin-top: 10rpx;
		width: 90%;
		margin-left: 5%;
		height: 110rpx;
		line-height: 90rpx;
		text-align: center;
		color: #fff;
		font-size: 32rpx;
}
.container {
		width: 100%;
		height: auto;
		min-height: 100%;
		padding-bottom: 10rpx;
}
.banner {
		width: 100%;
		background: #fff;
		height: 400rpx;
		display: table
}
.contentbox {
		width: 94%;
		margin: 0 3%;
		padding: 20rpx 40rpx;
		border-radius: 20rpx;
		background: #fff;
		color: #B17D2D;
		margin-bottom: 30rpx;
		display: flex;
		flex-direction: column;
		margin-bottom: 10px
}
.title {
		height: 50rpx;
		line-height: 50rpx
}
.flex1 {
		flex-grow: 1;
		flex-shrink: 1;
}
.user-level {
		margin-left: 10rpx;
		display: flex;
}
.user-level image {
		width: 44rpx;
		height: 44rpx;
		margin-right: 10rpx;
		margin-left: -4rpx;
}
.level-name {
		height: 36rpx;
		border-radius: 18rpx;
		font-size: 24rpx;
		color: #fff;
		background-color: #5c5652;
		padding: 0 16rpx 0 0;
		display: flex;
		align-items: flex-end;
}
.level-name .name {
		display: flex;
		align-items: center;
		height: 100%
}
.noup {
		width: 100%;
		text-align: center;
		font-size: 32rpx;
		color: green
}
.form-item1 {
		width: 100%;
		display: flex;
		flex-direction: column;
		color: #333
}
.form-item1 .panel {
		width: 100%;
		font-size: 32rpx;
		color: #B17D2D;
}
.form-item1 radio-group {
		width: 100%;
		background: #fff;
		padding-left: 10rpx;
}
.form-item1 .radio-item {
		display: flex;
		width: 100%;
		color: #000;
		align-items: center;
		background: #fff;
		padding: 12rpx 0;
}
.form-item1 .radio-item:last-child {
		border: 0
}
.radio-item .user-level {
		flex: 1
}
.form-item1 radio {
		transform: scale(0.8);
}
.applytj {
		width: 100%;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-6073b5bd {
  min-height: 100vh;
}
.address-pop.data-v-6073b5bd {
  padding: 20rpx 30rpx;
}
.address-pop .tips.data-v-6073b5bd {
  padding: 10rpx 0;
  font-size: 24rpx;
  color: #ff0000;
}
.address-pop .title.data-v-6073b5bd {
  text-align: center;
  font-weight: bold;
}
.address-pop .footer.data-v-6073b5bd {
  height: 80rpx;
  line-height: 80rpx;
  width: 300rpx;
  text-align: center;
  background-color: #F2D8B2;
  margin: 0 auto;
  margin-top: 30rpx;
  border-radius: 50rpx;
}
.article-list.data-v-6073b5bd {
  padding-top: 20rpx;
  line-height: 1;
  background: #f7f7f7;
}
.article-item.data-v-6073b5bd {
  margin-bottom: 20rpx;
  background: #fff;
}
.article-item.data-v-6073b5bd:last-child {
  margin-bottom: 0;
}
.article-item .article-item__title.data-v-6073b5bd {
  max-height: 74rpx;
  font-size: 28rpx;
  line-height: 38rpx;
  color: #333;
}
.article-item .article-item__image .image.data-v-6073b5bd {
  display: block;
}
.show-type__10.data-v-6073b5bd {
  display: flex;
}
.show-type__10 .article-item__left.data-v-6073b5bd {
  padding-right: 20rpx;
}
.show-type__10 .article-item__image .image.data-v-6073b5bd {
  width: 240rpx;
}
.show-type__20 .article-item__image .image.data-v-6073b5bd {
  width: 100%;
  height: 350rpx;
}