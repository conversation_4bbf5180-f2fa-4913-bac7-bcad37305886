
page {
    background: #fff;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.reason.data-v-b7926412 {
  padding-top: 40rpx;
  text-align: center;
  color: #ff0000;
  width: 80%;
  margin: 0 auto;
}
.dealer-bg .image.data-v-b7926412 {
  width: 100%;
}
.widget-body.data-v-b7926412 {
  position: relative;
}
.widget-body .widget.data-v-b7926412 {
  width: 88%;
  box-sizing: border-box;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.11);
  border-radius: 12rpx;
}
.widget__base.data-v-b7926412 {
  margin-top: -60rpx;
}
.widget__base .base__user.data-v-b7926412 {
  position: relative;
  padding: 15rpx 40rpx;
  border-bottom: 1rpx solid #e7e7e7;
}
.widget__base .base__user .user-avatar.data-v-b7926412 {
  position: absolute;
  top: -75rpx;
  right: 60rpx;
}
.widget__base .base__user .user-nickName.data-v-b7926412 {
  margin-top: 30rpx;
  margin-bottom: 10rpx;
}
.widget__base .base__capital.data-v-b7926412 {
  padding: 35rpx;
}
.widget__base .base__capital .capital-card.data-v-b7926412 {
  height: 200rpx;
  padding: 36rpx 0;
  background-color: #8e84fc;
  border-radius: 10rpx;
  box-sizing: border-box;
}
.widget__base .base__capital .capital-card .card-left.data-v-b7926412 {
  flex: 1;
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  flex-direction: column;
  padding-left: 32rpx;
}
.widget__base .base__capital .capital-card .card-right .withdraw-btn.data-v-b7926412 {
  width: 130rpx;
  height: 50rpx;
  background: #fff;
  color: #8e84fc;
  border-radius: 25rpx;
  margin-right: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.widget__base .base__capital .capital-already.data-v-b7926412 {
  padding: 20rpx;
  padding-bottom: 0;
}

/* 操作列表 */
.widget__operat.data-v-b7926412 {
  padding: 50rpx;
  margin-top: 40rpx;
}
.widget__operat .operat__item.data-v-b7926412 {
  width: 33.33333%;
  float: left;
  margin-bottom: 50rpx;
  text-align: center;
}
.widget__operat .operat__item.data-v-b7926412:last-child {
  margin-bottom: 0;
}
.widget__operat .operat__item .item__icon.data-v-b7926412 {
  margin-bottom: 8rpx;
  font-size: 58rpx;
}

/* 当前不是分销商 */
.no-dealer.data-v-b7926412 {
  padding-top: 150rpx;
}
.no-icon image.data-v-b7926412 {
  width: 420rpx;
  height: 240rpx;
}
.no-msg.data-v-b7926412 {
  padding: 86rpx 0;
}
.form-submit .button.data-v-b7926412 {
  font-size: 30rpx;
  background: #786cff;
  border: 1rpx solid #786cff;
  color: white;
  border-radius: 50rpx;
  padding: 22rpx 0;
  width: 470rpx;
  box-sizing: border-box;
  margin: 0 auto;
  text-align: center;
}
.form-submit .button.disabled.data-v-b7926412 {
  opacity: 0.6;
}