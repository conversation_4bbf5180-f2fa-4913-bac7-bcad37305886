"use strict";
const core_app = require("../../core/app.js");
const utils_color = require("../../utils/color.js");
require("../../common/enum/goods/GoodsType.js");
const common_enum_goods_SpecType = require("../../common/enum/goods/SpecType.js");
require("../../common/enum/goods/GoodsSource.js");
const api_cart = require("../../api/cart.js");
const api_goods_index = require("../../api/goods/index.js");
const common_vendor = require("../../common/vendor.js");
const GoodsSkuPopup = () => "../goods-sku-popup/index.js";
const _sfc_main = {
  components: {
    GoodsSkuPopup
  },
  props: {
    // 购物车按钮样式 1 2 3
    btnStyle: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      // 是否显示
      visible: false,
      // 主商品信息
      goods: {},
      // SKU商品信息
      localdata: {}
    };
  },
  computed: {
    // 规格按钮选中时的背景色
    activedBtnBackgroundColor() {
      return utils_color.hex2rgba(this.appTheme.mainBg, 0.1);
    }
  },
  methods: {
    // 加入购物车事件
    async handle(goods) {
      this.goods = goods;
      if (goods.spec_type == common_enum_goods_SpecType.SpecTypeEnum.SINGLE.value) {
        this.singleEvent();
      }
      if (goods.spec_type == common_enum_goods_SpecType.SpecTypeEnum.MULTI.value) {
        this.multiEvent();
      }
    },
    // 单规格商品事件
    singleEvent() {
      const { goods } = this;
      this.addCart({
        goods_id: goods.goods_id,
        goods_sku_id: "0",
        buy_num: 1
      });
    },
    // 多规格商品事件
    async multiEvent() {
      const app = this;
      const { goods } = app;
      const { data: { specData } } = await api_goods_index.specData(goods.goods_id);
      goods.skuList = specData.skuList;
      goods.specList = specData.specList;
      app.localdata = {
        _id: goods.goods_id,
        name: goods.goods_name,
        goods_thumb: goods.goods_image,
        sku_list: app.getSkuList(),
        spec_list: app.getSpecList()
      };
      this.visible = true;
    },
    // 监听组件显示隐藏
    onChangeValue(val) {
      this.visible = val;
    },
    // 整理商品SKU列表 (多规格)
    getSkuList() {
      const app = this;
      const { goods: { goods_name, goods_image, skuList } } = app;
      const skuData = [];
      skuList.forEach((item) => {
        skuData.push({
          _id: item.id,
          goods_sku_id: item.goods_sku_id,
          goods_id: item.goods_id,
          goods_name,
          image: item.image_url ? item.image_url : goods_image,
          price: item.goods_price * 100,
          stock: item.stock_num,
          spec_value_ids: item.spec_value_ids,
          sku_name_arr: app.getSkuNameArr(item.spec_value_ids)
        });
      });
      return skuData;
    },
    // 获取sku记录的规格值列表
    getSkuNameArr(specValueIds) {
      const app = this;
      const defaultData = ["默认"];
      const skuNameArr = [];
      if (specValueIds) {
        specValueIds.forEach((valueId, groupIndex) => {
          const specValueName = app.getSpecValueName(valueId, groupIndex);
          skuNameArr.push(specValueName);
        });
      }
      return skuNameArr.length ? skuNameArr : defaultData;
    },
    // 获取指定的规格值名称
    getSpecValueName(valueId, groupIndex) {
      const app = this;
      const { goods: { specList } } = app;
      const res = specList[groupIndex].valueList.find((specValue) => {
        return specValue.spec_value_id == valueId;
      });
      return res.spec_value;
    },
    // 整理规格数据 (多规格)
    getSpecList() {
      const { goods: { specList } } = this;
      const defaultData = [{ name: "默认", list: [{ name: "默认" }] }];
      const specData = [];
      specList.forEach((group) => {
        const children = [];
        group.valueList.forEach((specValue) => {
          children.push({ name: specValue.spec_value });
        });
        specData.push({
          name: group.spec_name,
          list: children
        });
      });
      return specData.length ? specData : defaultData;
    },
    // 加入购物车按钮
    addCart(selectShop) {
      const app = this;
      const { goods_id, goods_sku_id, buy_num } = selectShop;
      api_cart.add(goods_id, goods_sku_id, buy_num).then((result) => {
        app.$toast(result.message, 1e3, false);
        app.onChangeValue(false);
        const cartTotal = result.data.cartTotal;
        core_app.setCartTotalNum(cartTotal);
        app.$emit("addCart", cartTotal);
      });
    }
  }
};
if (!Array) {
  const _component_goods_sku_popup = common_vendor.resolveComponent("goods-sku-popup");
  _component_goods_sku_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o($options.onChangeValue),
    b: common_vendor.o($options.addCart),
    c: common_vendor.p({
      modelValue: $data.visible,
      ["border-radius"]: "20",
      localdata: $data.localdata,
      mode: 2,
      maskCloseAble: true,
      priceColor: _ctx.appTheme.mainBg,
      buyNowBackgroundColor: _ctx.appTheme.mainBg,
      addCartColor: _ctx.appTheme.viceText,
      addCartBackgroundColor: _ctx.appTheme.viceBg,
      activedStyle: {
        color: _ctx.appTheme.mainBg,
        borderColor: _ctx.appTheme.mainBg,
        backgroundColor: $options.activedBtnBackgroundColor
      },
      buyNowText: "立即购买"
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/add-cart-popup/index.js.map
