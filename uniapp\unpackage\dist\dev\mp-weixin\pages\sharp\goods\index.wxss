
page {
    background: #fafafa;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-dbd30a2e {
  padding-bottom: calc(constant(safe-area-inset-bottom) + 106rpx + 6rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 106rpx + 6rpx);
}
.goods-info.data-v-dbd30a2e {
  background: #fff;
  padding: 25rpx 30rpx;
}
.info-item__top.data-v-dbd30a2e {
  min-height: 40rpx;
  margin-bottom: 20rpx;
}
.info-item__top .active-tag.data-v-dbd30a2e {
  color: #fff;
  background: var(--main-bg);
  padding: 4rpx 10rpx;
  border-radius: 15rpx;
  font-size: 26rpx;
  text-align: center;
  margin-right: 15rpx;
}
.floor-price__samll.data-v-dbd30a2e {
  font-size: 26rpx;
  line-height: 1;
  color: var(--main-bg);
  margin-bottom: -10rpx;
}
.floor-price.data-v-dbd30a2e {
  color: var(--main-bg);
  margin-right: 15rpx;
  font-size: 42rpx;
  line-height: 1;
}
.original-price.data-v-dbd30a2e {
  font-size: 26rpx;
  line-height: 1;
  text-decoration: line-through;
  color: #959595;
  margin-bottom: -6rpx;
}
.goods-sales.data-v-dbd30a2e {
  font-size: 24rpx;
  color: #959595;
}
.info-item__name .goods-name.data-v-dbd30a2e {
  font-size: 30rpx;
}
.goods-share__line.data-v-dbd30a2e {
  border-left: 1rpx solid #f4f4f4;
  height: 60rpx;
  margin: 0 30rpx;
}
.goods-share .share-btn.data-v-dbd30a2e {
  line-height: normal;
  padding: 0;
  background: none;
  border-radius: 0;
  box-shadow: none;
  font-size: 8pt;
  border: none;
  color: #191919;
}
.goods-share .share-btn.data-v-dbd30a2e::after {
  border: none;
}
.goods-share .share__icon.data-v-dbd30a2e {
  font-size: 40rpx;
  margin-bottom: 5rpx;
}
.info-item_selling-point.data-v-dbd30a2e {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #808080;
}
.goods-choice.data-v-dbd30a2e {
  padding: 26rpx 30rpx;
  font-size: 28rpx;
}
.goods-choice .spec-list.data-v-dbd30a2e {
  display: flex;
  align-items: center;
}
.goods-choice .spec-list .spec-name.data-v-dbd30a2e {
  margin-right: 10rpx;
}
.goods-content .item-title.data-v-dbd30a2e {
  padding: 26rpx 30rpx;
  font-size: 28rpx;
}
.footer-fixed.data-v-dbd30a2e {
  position: fixed;
  bottom: var(--window-bottom);
  left: 0;
  right: 0;
  display: flex;
  z-index: 11;
  box-shadow: 0 -4rpx 40rpx 0 rgba(151, 151, 151, 0.24);
  background: #fff;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.footer-container.data-v-dbd30a2e {
  width: 100%;
  display: flex;
  height: 106rpx;
}
.foo-item-fast.data-v-dbd30a2e {
  box-sizing: border-box;
  width: 256rpx;
  line-height: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.foo-item-fast .fast-item.data-v-dbd30a2e {
  position: relative;
  padding: 4rpx 10rpx;
  line-height: 1;
}
.foo-item-fast .fast-item .fast-icon.data-v-dbd30a2e {
  margin-bottom: 6rpx;
}
.foo-item-fast .fast-item--home.data-v-dbd30a2e {
  margin-right: 30rpx;
}
.foo-item-fast .fast-item--cart .fast-icon.data-v-dbd30a2e {
  padding-left: 3px;
}
.foo-item-fast .fast-item .fast-badge.data-v-dbd30a2e {
  display: inline-block;
  box-sizing: border-box;
  min-width: 16px;
  padding: 0 3px;
  color: #fff;
  font-weight: 500;
  font-size: 12px;
  font-family: -apple-system-font, Helvetica Neue, Arial, sans-serif;
  line-height: 1.2;
  text-align: center;
  background-color: #ee0a24;
  border: 1px solid #fff;
  border-radius: 999px;
}
.foo-item-fast .fast-item .fast-badge--fixed.data-v-dbd30a2e {
  position: absolute;
  top: 0;
  right: 0;
  transform-origin: 100%;
}
.foo-item-fast .fast-item .fast-icon.data-v-dbd30a2e {
  font-size: 46rpx;
}
.foo-item-fast .fast-item .fast-text.data-v-dbd30a2e {
  font-size: 24rpx;
}
.foo-item-btn.data-v-dbd30a2e {
  flex: 1;
}
.foo-item-btn .btn-wrapper.data-v-dbd30a2e {
  height: 100%;
  display: flex;
  align-items: center;
}
.foo-item-btn .btn-item.data-v-dbd30a2e {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0;
}
.foo-item-btn .btn-item.data-v-dbd30a2e {
  flex: 1;
  height: 100%;
  font-size: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.foo-item-btn .btn-item.btn--main.data-v-dbd30a2e {
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
}
.foo-item-btn .btn-item.btn--gray.data-v-dbd30a2e {
  background-color: #ccc;
  color: #fff;
}
.info-item_status.data-v-dbd30a2e {
  margin-top: 20rpx;
  padding: 15rpx 20rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
}
.info-item_status .countdown-icon.data-v-dbd30a2e {
  font-size: 28rpx;
  margin-right: 15rpx;
}
.info-item_countdown.data-v-dbd30a2e {
  background: #f0f9ff;
  color: #8f8f8f;
}
.info-item_countdown .countdown-icon.data-v-dbd30a2e {
  color: #1397d8;
}
.info-item_end.data-v-dbd30a2e {
  background: #ccc;
  color: #fff;
}