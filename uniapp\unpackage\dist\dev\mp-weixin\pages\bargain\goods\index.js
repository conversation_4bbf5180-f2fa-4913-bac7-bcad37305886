"use strict";
const core_mixins_wxofficial = require("../../../core/mixins/wxofficial.js");
const core_app = require("../../../core/app.js");
const api_goods_index = require("../../../api/goods/index.js");
const api_cart = require("../../../api/cart.js");
const api_bargain_active = require("../../../api/bargain/active.js");
const common_model_Setting = require("../../../common/model/Setting.js");
const common_vendor = require("../../../common/vendor.js");
const ShareSheet = () => "../../../components/share-sheet/index.js";
const CustomerBtn = () => "../../../components/customer-btn/index.js";
const SkuPopup = () => "./components/SkuPopup.js";
const SlideImage = () => "../../goods/components/SlideImage.js";
const Comment = () => "../../goods/components/Comment.js";
const CountDown = () => "../../../components/countdown/index.js";
const _sfc_main = {
  components: {
    ShareSheet,
    CustomerBtn,
    // Shortcut,
    SlideImage,
    SkuPopup,
    Comment,
    // Service,
    CountDown
  },
  mixins: [core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 正在加载
      isLoading: true,
      // 显示/隐藏SKU弹窗
      showSkuPopup: false,
      // 模式 1:都显示 2:只显示购物车 3:只显示立即购买
      skuMode: 3,
      // 显示/隐藏分享菜单
      showShareSheet: false,
      // 显示砍价规则
      showRules: false,
      // 获取商品海报图api方法
      posterApiCall: api_bargain_active.poster,
      // 当前活动ID
      activeId: null,
      // 当前商品ID
      goodsId: null,
      // 活动详情
      active: {},
      // 商品详情
      goods: {},
      // 砍价设置
      setting: null,
      // 标记当前用户是否正在参与
      isPartake: null,
      // 砍价任务ID (当前用户参与的话才有值)
      taskId: null,
      // 购物车总数量
      cartTotal: 0,
      // 是否显示在线客服按钮
      isShowCustomerBtn: false
    };
  },
  computed: {
    // 当前页面链接
    pagePath() {
      const params = this.$getShareUrlParams({
        activeId: this.activeId,
        goodsId: this.goodsId
      });
      return `/pages/bargain/goods/index?${params}`;
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    this.onRecordQuery(options);
    this.onRefreshPage();
    this.isShowCustomerBtn = await common_model_Setting.SettingModel.isShowCustomerBtn();
  },
  methods: {
    // 记录query参数
    onRecordQuery(query) {
      const scene = core_app.getSceneData(query);
      this.activeId = query.activeId ? parseInt(query.activeId) : parseInt(scene.aid);
      this.goodsId = query.goodsId ? parseInt(query.goodsId) : parseInt(scene.gid);
    },
    // 刷新页面数据
    onRefreshPage() {
      const app = this;
      app.isLoading = true;
      Promise.all([app.getActiveDetail(), app.getGoodsDetail(), app.getCartTotal()]).then(() => app.setWxofficialShareData()).finally(() => app.isLoading = false);
    },
    // 获取砍价活动详情
    getActiveDetail() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_bargain_active.detail(app.activeId).then((result) => {
          app.active = result.data.active;
          app.setting = result.data.setting;
          app.isPartake = result.data.isPartake;
          app.taskId = result.data.taskId;
          resolve(result);
        }).catch(reject);
      });
    },
    // 获取商品信息
    getGoodsDetail() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_goods_index.detail(app.goodsId, false).then((result) => {
          app.goods = result.data.detail;
          resolve(result);
        }).catch(reject);
      });
    },
    // 获取购物车总数量
    getCartTotal() {
      const app = this;
      return new Promise((resolve, reject) => {
        api_cart.total().then((result) => {
          app.cartTotal = result.data.cartTotal;
          resolve(result);
        }).catch(reject);
      });
    },
    /**
     * 显示/隐藏SKU弹窗
     */
    onShowSkuPopup() {
      this.showSkuPopup = !this.showSkuPopup;
    },
    // 显示隐藏分享菜单
    onShowShareSheet() {
      this.showShareSheet = !this.showShareSheet;
    },
    // 显示砍价规则
    handleShowRules() {
      this.showRules = true;
    },
    // 跳转到首页
    onTargetHome(e) {
      this.$navTo("pages/index/index");
    },
    // 跳转到购物车页
    onTargetCart() {
      this.$navTo("pages/cart/index");
    },
    // 点击主按钮
    handleMainBtn() {
      const app = this;
      if (!app.isPartake) {
        return app.onShowSkuPopup();
      }
      app.$navTo("pages/bargain/task", { taskId: app.taskId });
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      const { goods } = this;
      this.updateShareCardData({
        title: goods.goods_name,
        desc: goods.selling_point,
        imgUrl: goods.goods_image
      });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    return {
      title: this.goods.goods_name,
      path: this.pagePath
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    return {
      title: this.goods.goods_name,
      path: this.pagePath
    };
  }
};
if (!Array) {
  const _component_SlideImage = common_vendor.resolveComponent("SlideImage");
  const _component_count_down = common_vendor.resolveComponent("count-down");
  const _component_SkuPopup = common_vendor.resolveComponent("SkuPopup");
  const _component_Comment = common_vendor.resolveComponent("Comment");
  const _easycom_mp_html2 = common_vendor.resolveComponent("mp-html");
  const _component_customer_btn = common_vendor.resolveComponent("customer-btn");
  const _component_share_sheet = common_vendor.resolveComponent("share-sheet");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  (_component_SlideImage + _component_count_down + _component_SkuPopup + _component_Comment + _easycom_mp_html2 + _component_customer_btn + _component_share_sheet + _easycom_u_modal2)();
}
const _easycom_mp_html = () => "../../../uni_modules/mp-html/components/mp-html/mp-html.js";
const _easycom_u_modal = () => "../../../uni_modules/vk-uview-ui/components/u-modal/u-modal.js";
if (!Math) {
  (_easycom_mp_html + _easycom_u_modal)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? {
    b: common_vendor.p({
      video: $data.goods.video,
      videoCover: $data.goods.videoCover,
      images: $data.goods.goods_images
    })
  } : {}, {
    c: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    d: common_vendor.t($data.active.floor_price),
    e: common_vendor.t($data.goods.goods_price_min),
    f: common_vendor.t($data.active.active_sales),
    g: common_vendor.t($data.goods.goods_name),
    h: common_vendor.o(($event) => $options.onShowShareSheet()),
    i: $data.goods.selling_point
  }, $data.goods.selling_point ? {
    j: common_vendor.t($data.goods.selling_point)
  } : {}, {
    k: $data.active.is_end == false
  }, $data.active.is_end == false ? {
    l: common_vendor.p({
      date: $data.active.end_time,
      separator: "zh",
      theme: "text"
    })
  } : {}, {
    m: $data.active.is_end == true
  }, $data.active.is_end == true ? {} : {}) : {}, {
    n: common_vendor.o(($event) => $options.handleShowRules()),
    o: $data.goods.spec_type == 20
  }, $data.goods.spec_type == 20 ? {
    p: common_vendor.f($data.goods.specList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.spec_name),
        b: index
      };
    }),
    q: common_vendor.o(($event) => $options.onShowSkuPopup())
  } : {}, {
    r: !$data.isLoading
  }, !$data.isLoading ? {
    s: common_vendor.o(($event) => $data.showSkuPopup = $event),
    t: common_vendor.p({
      skuMode: $data.skuMode,
      active: $data.active,
      goods: $data.goods,
      modelValue: $data.showSkuPopup
    })
  } : {}, {
    v: !$data.isLoading
  }, !$data.isLoading ? {
    w: common_vendor.p({
      ["goods-id"]: $data.goodsId,
      limit: 2
    })
  } : {}, {
    x: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    y: $data.goods.content != ""
  }, $data.goods.content != "" ? {
    z: common_vendor.p({
      content: $data.goods.content
    })
  } : {}) : {}, {
    A: common_vendor.o((...args) => $options.onTargetHome && $options.onTargetHome(...args)),
    B: $data.isShowCustomerBtn
  }, $data.isShowCustomerBtn ? {
    C: common_vendor.p({
      showCard: true,
      cardTitle: $data.goods.goods_name,
      cardImage: $data.goods.goods_image,
      cardPath: $options.pagePath
    })
  } : {}, {
    D: !$data.isShowCustomerBtn
  }, !$data.isShowCustomerBtn ? common_vendor.e({
    E: $data.cartTotal > 0
  }, $data.cartTotal > 0 ? {
    F: common_vendor.t($data.cartTotal > 99 ? "99+" : $data.cartTotal)
  } : {}, {
    G: common_vendor.o((...args) => $options.onTargetCart && $options.onTargetCart(...args))
  }) : {}, {
    H: $data.active.is_start && !$data.active.is_end
  }, $data.active.is_start && !$data.active.is_end ? {
    I: common_vendor.t($data.isPartake ? "继续砍价" : "立即砍价"),
    J: common_vendor.o(($event) => $options.handleMainBtn(3))
  } : {
    K: common_vendor.t($data.active.is_end ? "活动已结束" : "活动未开启")
  }, {
    L: common_vendor.o(($event) => $data.showShareSheet = $event),
    M: common_vendor.p({
      shareTitle: $data.goods.goods_name,
      shareImageUrl: $data.goods.goods_image,
      posterApiCall: $data.posterApiCall,
      posterApiParam: {
        activeId: $data.activeId
      },
      modelValue: $data.showShareSheet
    }),
    N: !$data.isLoading
  }, !$data.isLoading ? {
    O: common_vendor.t($data.setting.rulesDesc),
    P: common_vendor.o(($event) => $data.showRules = $event),
    Q: common_vendor.p({
      title: "砍价规则",
      modelValue: $data.showRules
    })
  } : {}, {
    R: !$data.isLoading,
    S: common_vendor.s(_ctx.appThemeStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-6c6b367a"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/bargain/goods/index.js.map
