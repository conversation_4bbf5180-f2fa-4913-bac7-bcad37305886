"use strict";
const common_vendor = require("../../common/vendor.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const api_xj_message = require("../../api/xj/message.js");
const core_app = require("../../core/app.js");
const pageSize = 15;
const _sfc_main = {
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin],
  data() {
    return {
      // 列表数据
      list: core_app.getEmptyPaginateObj(),
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: { size: pageSize },
        // 数量要大于12条才显示无更多数据
        noMoreSize: 12,
        // 空布局
        empty: {
          tip: "暂无消息"
        }
      }
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },
  methods: {
    toVip(logId) {
      api_xj_message.detail(logId).then((result) => {
        common_vendor.index.navigateTo({
          url: "/pages/vip/index"
        });
      });
    },
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getHelpList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 获取帮助列表
    getHelpList(pageNo = 1) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_xj_message.list({ page: pageNo }).then((result) => {
          const newList = result.data.list;
          app.list.data = core_app.getMoreListData(newList, app.list, pageNo);
          resolve(newList);
        });
      });
    }
  }
};
if (!Array) {
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  _easycom_mescroll_body2();
}
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  _easycom_mescroll_body();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.list.data, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.describe),
        c: common_vendor.t(item.create_time),
        d: index,
        e: common_vendor.o(($event) => $options.toVip(item.log_id), index)
      };
    }),
    b: common_vendor.sr("mescrollRef", "83b6b7b2-0"),
    c: common_vendor.o(_ctx.mescrollInit),
    d: common_vendor.o($options.upCallback),
    e: common_vendor.p({
      sticky: true,
      down: {
        use: false
      },
      up: $data.upOption
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-83b6b7b2"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesNew/message/index.js.map
