"use strict";
require("../../store/index.js");
require("../../utils/request/index.js");
require("../../common/vendor.js");
require("../config/index.js");
require("../../common/enum/setting/Key.js");
const WxofficialMixin = {
  data() {
    return {
      // 微信公众号链接分享卡片内容
      wxofficialShareData: void 0
    };
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onShow() {
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
  },
  methods: {
    // 更新分享卡片内容
    updateShareCardData(param) {
    }
  }
};
exports.WxofficialMixin = WxofficialMixin;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/core/mixins/wxofficial.js.map
