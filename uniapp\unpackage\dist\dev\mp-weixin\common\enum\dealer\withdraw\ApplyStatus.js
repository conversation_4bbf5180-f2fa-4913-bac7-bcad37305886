"use strict";
const common_enum_enum = require("../../enum.js");
const ApplyStatusEnum = new common_enum_enum.Enum([
  { key: "WAIT", name: "待审核", value: 10 },
  { key: "PASSED", name: "审核通过", value: 20 },
  { key: "REJECT", name: "驳回", value: 30 },
  { key: "PAYMENT", name: "已打款", value: 40 },
  { key: "SUCCESS", name: "已完成", value: 50 },
  { key: "FAIL", name: "未收款", value: 60 }
]);
exports.ApplyStatusEnum = ApplyStatusEnum;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/common/enum/dealer/withdraw/ApplyStatus.js.map
