"use strict";
const common_vendor = require("../../common/vendor.js");
const core_platform = require("../platform.js");
const utils_storage = require("../../utils/storage.js");
const common_enum_Client = require("../../common/enum/Client.js");
const common_enum_payment_Method = require("../../common/enum/payment/Method.js");
const paymentAsH5 = (option) => {
  const options = { formHtml: "", ...option };
  utils_storage.storage.set("tempUnifyData_" + options.orderKey, {
    method: common_enum_payment_Method.PayMethodEnum.ALIPAY.value,
    outTradeNo: options.out_trade_no
  }, 60 * 60);
  return new Promise((resolve, reject) => {
    if (options.formHtml) {
      const div = document.createElement("div");
      div.innerHTML = options.formHtml;
      document.body.appendChild(div);
      document.forms[0].submit();
    }
  });
};
const paymentAsApp = (options) => {
  return new Promise((resolve, reject) => {
    common_vendor.index.requestPayment({
      provider: "alipay",
      orderInfo: options.orderInfo,
      success(res) {
        const option = {
          isRequireQuery: true,
          outTradeNo: options.out_trade_no,
          method: "alipay"
        };
        resolve({ res, option });
      },
      fail: (res) => reject(res)
    });
  });
};
const paymentAsMpAlipayApp = (options) => {
  return new Promise((resolve, reject) => {
    common_vendor.index.requestPayment({
      provider: "alipay",
      orderInfo: options.orderInfo,
      success(res) {
        const option = {
          isRequireQuery: true,
          outTradeNo: options.out_trade_no,
          method: "alipay"
        };
        resolve({ res, option });
      },
      fail: (res) => reject(res)
    });
  });
};
const payment = (option) => {
  const events = {
    [common_enum_Client.ClientEnum.H5.value]: paymentAsH5,
    [common_enum_Client.ClientEnum.APP.value]: paymentAsApp,
    [common_enum_Client.ClientEnum.MP_ALIPAY.value]: paymentAsMpAlipayApp
  };
  return events[core_platform.platfrom](option);
};
const extraAsUnify = () => {
  const extra = {};
  return extra;
};
exports.extraAsUnify = extraAsUnify;
exports.payment = payment;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/core/payment/alipay.js.map
