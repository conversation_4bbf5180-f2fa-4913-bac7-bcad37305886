"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "goods-poster-popup",
  emits: ["update:modelValue"],
  props: {
    // 控制组件显示隐藏
    modelValue: {
      Type: Boolean,
      default: false
    },
    // 获取海报图的api方法
    apiCall: {
      type: Function,
      default: () => {
      }
    },
    // 获取海报图的api参数
    apiParam: {
      type: Object,
      default: () => {
      }
    }
  },
  watch: {
    // 监听海报图弹层显示隐藏
    modelValue: {
      immediate: true,
      handler(val) {
        val ? this.onShowPopup() : this.show = false;
      }
    }
  },
  data() {
    return {
      // 是否显示弹窗
      show: false,
      // 图片url地址
      imageUrl: ""
    };
  },
  methods: {
    // 显示海报弹窗
    onShowPopup() {
      const app = this;
      app.apiCall({ ...app.apiParam, channel: app.platform }).then((result) => {
        app.imageUrl = result.data.imageUrl;
        app.show = true;
      }).catch((err) => app.onClose());
    },
    // 关闭弹窗
    onClose() {
      this.$emit("update:modelValue", false);
    },
    // 预览图片
    handlePreviewImage() {
      common_vendor.index.previewImage({ urls: [this.imageUrl] });
    },
    // 保存海报图片
    handleDownload() {
      const app = this;
      common_vendor.index.showLoading({ title: "加载中" });
      common_vendor.index.downloadFile({
        url: app.imageUrl,
        success(res) {
          common_vendor.index.__f__("log", "at components/goods-poster-popup/index.vue:84", res);
          common_vendor.index.hideLoading();
          app.onSaveImage(res.tempFilePath);
        },
        fail(res) {
          common_vendor.index.__f__("log", "at components/goods-poster-popup/index.vue:90", "fail", res);
          common_vendor.index.hideLoading();
          app.$toast("很抱歉，自动保存失败 请点击图片后长按手动保存", 3e3);
        }
      });
    },
    // 图片保存到相册
    onSaveImage(tempFilePath) {
      const app = this;
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: tempFilePath,
        success(data) {
          app.$success("保存成功");
          app.onClose();
        },
        fail(err) {
          if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
            app.$toast("请允许访问相册后重试 (右上角菜单 - 设置 - 相册)", 3e3);
          }
        }
      });
    }
    // 兼容H5端下载图片
  }
};
if (!Array) {
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  _easycom_u_popup2();
}
const _easycom_u_popup = () => "../../uni_modules/vk-uview-ui/components/u-popup/u-popup.js";
if (!Math) {
  _easycom_u_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.imageUrl
  }, $data.imageUrl ? {
    b: $data.imageUrl
  } : {}, {
    c: common_vendor.o(($event) => $options.handlePreviewImage()),
    d: common_vendor.o(($event) => $options.handleDownload()),
    e: common_vendor.o(($event) => $options.onClose()),
    f: common_vendor.o(($event) => $data.show = $event),
    g: common_vendor.p({
      mode: "center",
      maskCloseAble: false,
      closeable: true,
      maskCustomStyle: {
        background: "rgba(0, 0, 0, 0.5)"
      },
      ["border-radius"]: "18",
      ["z-index"]: 12,
      modelValue: $data.show
    })
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8c4dbe0a"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/goods-poster-popup/index.js.map
