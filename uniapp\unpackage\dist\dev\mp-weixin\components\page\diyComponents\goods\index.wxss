/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.diy-goods.data-v-be2a91ee {
  display: flex;
  flex-direction: column;
}
.diy-goods .goods-list.data-v-be2a91ee {
  margin-bottom: -10px;
}
.diy-goods .goods-list.display-slide.data-v-be2a91ee {
  display: flex;
  width: -webkit-max-content;
  width: max-content;
}
.diy-goods .goods-list.display-slide .goods-item.data-v-be2a91ee {
  flex-shrink: 1;
  margin-right: 20rpx !important;
}
.diy-goods .goods-list.display-slide .goods-item.data-v-be2a91ee:last-child {
  margin-right: 0 !important;
}
.diy-goods .goods-list.display-slide.column-2 .goods-item.data-v-be2a91ee {
  width: 160px;
}
.diy-goods .goods-list.display-slide.column-3 .goods-item.data-v-be2a91ee {
  width: 220px;
}
.diy-goods .goods-list.display-list.data-v-be2a91ee {
  display: flex;
  flex-wrap: wrap;
}
.diy-goods .goods-list.column-1.data-v-be2a91ee {
  margin-bottom: 0;
}
.diy-goods .goods-list.column-1 .goods-item.data-v-be2a91ee {
  margin-bottom: 12rpx;
  padding: 20rpx;
  box-sizing: border-box;
  background: #fff;
  flex-basis: 100%;
  margin-right: 0;
}
.diy-goods .goods-list.column-1 .goods-item.data-v-be2a91ee:last-child {
  margin-bottom: 0 !important;
}
.diy-goods .goods-list.column-1 .goods-item .goods-info.data-v-be2a91ee {
  padding: 0;
}
.diy-goods .goods-list.column-1 .goods-item .goods-info .goods-name.data-v-be2a91ee {
  font-size: 27rpx;
}
.diy-goods .goods-list.column-1 .goods-item .goods-info .footer.data-v-be2a91ee {
  position: absolute;
  bottom: 16rpx;
  width: 100%;
}
.diy-goods .goods-list.column-1 .goods-item-left.data-v-be2a91ee {
  background: #fff;
  margin-right: 20rpx;
}
.diy-goods .goods-list.column-1 .goods-item-left .image.data-v-be2a91ee {
  display: block;
  width: 240rpx;
  height: 240rpx;
}
.diy-goods .goods-list.column-1 .goods-item-right.data-v-be2a91ee {
  position: relative;
  flex: 1;
  width: 0;
}
.diy-goods .goods-list.column-2 .goods-item.data-v-be2a91ee {
  flex-basis: 48.6%;
}
.diy-goods .goods-list.column-2 .goods-item.data-v-be2a91ee:nth-child(2n) {
  margin-right: 0;
}
.diy-goods .goods-list.column-3 .goods-item.data-v-be2a91ee {
  flex-basis: 31.46666%;
}
.diy-goods .goods-list.column-3 .goods-item.data-v-be2a91ee:nth-child(3n) {
  margin-right: 0;
}
.diy-goods .goods-list .goods-item.data-v-be2a91ee {
  flex-shrink: 0;
  background-color: #fff;
  flex: 0 1 48.6%;
  margin-right: 2.8%;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}
.diy-goods .goods-list .goods-item.display-card.data-v-be2a91ee {
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.07);
}
.diy-goods .goods-list .goods-item .goods-image.data-v-be2a91ee {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  overflow: hidden;
}
.diy-goods .goods-list .goods-item .goods-image.data-v-be2a91ee:after {
  content: "";
  display: block;
  margin-top: 100%;
}
.diy-goods .goods-list .goods-item .goods-image .image.data-v-be2a91ee {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.diy-goods .goods-list .goods-item .goods-info.data-v-be2a91ee {
  padding: 20rpx;
}
.diy-goods .goods-list .goods-item .goods-info .goods-name.data-v-be2a91ee {
  font-size: 26rpx;
  color: #000;
  margin-bottom: 8rpx;
  line-height: 1.3;
}
.diy-goods .goods-list .goods-item .goods-info .goods-name.row-two.data-v-be2a91ee {
  min-height: 68rpx;
}
.diy-goods .goods-list .goods-item .goods-info .goods-selling.data-v-be2a91ee {
  display: flex;
  font-size: 24rpx;
  margin-bottom: 8rpx;
  height: 38rpx;
}
.diy-goods .goods-list .goods-item .goods-info .goods-sales.data-v-be2a91ee {
  font-size: 24rpx;
  color: #959595;
  margin-bottom: 8rpx;
}
.diy-goods .goods-list .goods-item .goods-info .goods-sales .line.data-v-be2a91ee {
  margin: 0 8rpx;
}
.diy-goods .goods-list .goods-item .footer.data-v-be2a91ee {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.diy-goods .goods-list .goods-item .footer .goods-price.data-v-be2a91ee {
  position: relative;
  color: #ff1051;
}
.diy-goods .goods-list .goods-item .footer .goods-price .unit.data-v-be2a91ee {
  font-size: 20rpx;
}
.diy-goods .goods-list .goods-item .footer .goods-price .value.data-v-be2a91ee {
  font-size: 30rpx;
}
.diy-goods .goods-list .goods-item .footer .goods-price .unit2.data-v-be2a91ee {
  margin-left: 4rpx;
  font-size: 22rpx;
}
.diy-goods .goods-list .goods-item .footer .goods-price .line-price.data-v-be2a91ee {
  margin-left: 6rpx;
  color: #959595;
}
.diy-goods .goods-list .goods-item .footer .goods-price .line-price .unit.data-v-be2a91ee {
  text-decoration: line-through;
  font-size: 22rpx;
}
.diy-goods .goods-list .goods-item .footer .goods-price .line-price .value.data-v-be2a91ee {
  text-decoration: line-through;
  font-size: 22rpx;
}
.diy-goods .goods-list .goods-item .footer .action .btn-cart.data-v-be2a91ee {
  text-align: center;
}
.diy-goods .goods-list .goods-item .footer .action .btn-cart .cart-icon.data-v-be2a91ee {
  font-size: 36rpx;
}