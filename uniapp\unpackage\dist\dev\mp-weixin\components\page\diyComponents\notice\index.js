"use strict";
const common_vendor = require("../../../../common/vendor.js");
const components_page_diyComponents_mixin = require("../mixin.js");
const api_xj_message = require("../../../../api/xj/message.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemStyle: Object,
    params: Object
  },
  mixins: [components_page_diyComponents_mixin.mixin],
  data() {
    return {
      message: null
    };
  },
  async created() {
    this.getMessage();
  },
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {
    toMessage() {
      common_vendor.index.navigateTo({
        url: "/pagesNew/message/index"
      });
    },
    getMessage() {
      const app = this;
      api_xj_message.getMessage().then((result) => {
        app.message = result.data.detail;
      });
    }
  }
};
if (!Array) {
  const _easycom_u_notice_bar2 = common_vendor.resolveComponent("u-notice-bar");
  _easycom_u_notice_bar2();
}
const _easycom_u_notice_bar = () => "../../../../uni_modules/vk-uview-ui/components/u-notice-bar/u-notice-bar.js";
if (!Math) {
  _easycom_u_notice_bar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      padding: "10rpx 24rpx",
      ["volume-icon"]: $props.params.showIcon,
      autoplay: $props.params.scrollable,
      ["bg-color"]: $props.itemStyle.background,
      color: $props.itemStyle.textColor,
      list: [$props.params.text]
    }),
    b: `${$props.itemStyle.paddingTop * 2}rpx`,
    c: `${$props.itemStyle.paddingTop * 2}rpx`,
    d: common_vendor.o(($event) => _ctx.onLink($props.params.link)),
    e: $data.message
  }, $data.message ? {
    f: common_assets._imports_0$6,
    g: common_vendor.t($data.message.title),
    h: common_vendor.t($data.message.create_time),
    i: common_vendor.o((...args) => $options.toMessage && $options.toMessage(...args))
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-06d3f0f9"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/components/page/diyComponents/notice/index.js.map
