/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.pop-poster.data-v-8c4dbe0a {
  width: 560rpx;
  position: relative;
  background: #fff;
  padding: 76rpx 76rpx 40rpx 76rpx;
  border-radius: 10rpx;
}
.image__container .image.data-v-8c4dbe0a {
  display: block;
  width: 420rpx;
  height: 636rpx;
  box-shadow: 0 0 25rpx rgba(0, 0, 0, 0.15);
}
.save-btn__container.data-v-8c4dbe0a {
  margin-top: 30rpx;
}
.save-btn__container .save-btn.data-v-8c4dbe0a {
  color: white;
  color: var(--main-text);
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  font-weight: 500;
  font-size: 28rpx;
  border-radius: 38rpx;
  height: 76rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}