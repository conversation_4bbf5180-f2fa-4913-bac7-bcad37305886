"use strict";
const common_vendor = require("../../common/vendor.js");
const store_index = require("../../store/index.js");
const utils_request_upload_upload = require("./upload/upload.js");
const core_config_index = require("../../core/config/index.js");
const apiUrl = core_config_index.Config.get("apiUrl");
const $http = new utils_request_upload_upload.fileUpload({
  // 接口请求地址
  baseUrl: apiUrl,
  // 服务器本地上传文件地址
  fileUrl: apiUrl,
  // 服务器上传图片默认url
  defaultUploadUrl: "upload/image",
  // 设置请求头（如果使用报错跨域问题，可能是content-type请求类型和后台那边设置的不一致）
  header: {
    "content-type": "application/json;charset=utf-8"
  },
  // 请求超时时间, 单位ms（默认15000）
  timeout: 15e3,
  // 默认配置（可不写）
  config: {
    // 是否自动提示错误
    isPrompt: true,
    // 是否显示加载动画
    load: true,
    // 是否使用数据工厂
    isFactory: true
  }
});
let requestNum = 0;
let isShowingLoading = false;
$http.requestStart = (options) => {
  if (options.load) {
    if (requestNum <= 0 && !isShowingLoading) {
      try {
        common_vendor.index.showLoading({
          title: "加载中",
          mask: true
        });
        isShowingLoading = true;
      } catch (e) {
        common_vendor.index.__f__("log", "at utils/request/index.js:53", "showLoading error:", e);
      }
    }
    requestNum += 1;
  }
  if (options.method == "FILE" && options.maxSize) {
    const maxSize = options.maxSize;
    for (let item of options.files) {
      if (item.size > maxSize) {
        setTimeout(() => {
          common_vendor.index.showToast({
            title: "图片过大，请重新上传",
            icon: "none"
          });
        }, 10);
        return false;
      }
    }
  }
  options.header["storeId"] = store_index.store.getters.storeId;
  options.header["platform"] = store_index.store.getters.platform;
  options.header["Access-Token"] = store_index.store.getters.token;
  return options;
};
$http.requestEnd = (options) => {
  if (options.load) {
    requestNum = requestNum - 1;
    if (requestNum <= 0 && isShowingLoading) {
      try {
        common_vendor.index.hideLoading();
        isShowingLoading = false;
      } catch (e) {
        common_vendor.index.__f__("log", "at utils/request/index.js:95", "hideLoading error:", e);
        isShowingLoading = false;
      }
    }
  }
};
let loginModal = false;
$http.dataFactory = async (res) => {
  if (!res.response.statusCode || res.response.statusCode != 200) {
    return Promise.reject({
      statusCode: res.response.statusCode,
      errMsg: "http状态码错误"
    });
  }
  let httpData = res.response.data;
  if (typeof httpData == "string") {
    try {
      httpData = JSON.parse(httpData);
    } catch (error) {
      httpData = false;
    }
  }
  if (httpData === false || typeof httpData !== "object") {
    return Promise.reject({
      statusCode: res.response.statusCode,
      errMsg: "请检查api地址能否访问正常"
    });
  }
  if (httpData.status == 200) {
    return Promise.resolve(httpData);
  }
  if (httpData.status == 401) {
    store_index.store.dispatch("Logout");
    if (!loginModal) {
      loginModal = true;
      common_vendor.index.showModal({
        title: "温馨提示",
        content: "此时此刻需要您登录喔~",
        // showCancel: false,
        confirmText: "去登录",
        cancelText: "再逛会",
        success: (res2) => {
          if (res2.confirm) {
            common_vendor.index.reLaunch({
              url: "/pages/user/index?showLogin=true"
            });
          }
          if (res2.cancel && getCurrentPages().length > 1) {
            common_vendor.index.navigateBack();
          }
          loginModal = false;
        }
      });
    }
    return Promise.reject({
      statusCode: 0,
      errMsg: httpData.message,
      result: httpData
    });
  }
  if (httpData.status == 500) {
    if (res.isPrompt) {
      setTimeout(() => {
        common_vendor.index.showToast({
          title: httpData.message,
          icon: "none",
          duration: 2500
        });
      }, 10);
    }
    return Promise.reject({
      statusCode: 0,
      errMsg: httpData.message,
      result: httpData
    });
  }
};
$http.requestError = (e) => {
  if (e.statusCode === 0) {
    throw e;
  } else {
    setTimeout(() => showRequestError(e), 10);
  }
};
const showRequestError = (e) => {
  let errMsg = `网络请求出错：${e.errMsg}`;
  if (e.errMsg === "request:fail url not in domain list") {
    errMsg = "当前API域名未添加到微信小程序授权名单 " + e.errMsg;
  }
  if (e.errMsg === "request:fail") {
    errMsg = "网络请求错误：请检查api地址能否访问正常";
  }
  common_vendor.index.showToast({
    title: errMsg,
    icon: "none",
    duration: 3500
  });
};
exports.$http = $http;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/utils/request/index.js.map
