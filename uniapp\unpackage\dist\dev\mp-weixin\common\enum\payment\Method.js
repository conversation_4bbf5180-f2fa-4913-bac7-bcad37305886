"use strict";
const common_enum_enum = require("../enum.js");
const PayMethodEnum = new common_enum_enum.Enum([
  { key: "WECHAT", name: "微信支付", value: "wechat" },
  { key: "ALIPAY", name: "支付宝支付", value: "alipay" },
  { key: "BALANCE", name: "余额支付", value: "balance" },
  { key: "OFFLINE", name: "线下支付", value: "offline" }
]);
exports.PayMethodEnum = PayMethodEnum;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/common/enum/payment/Method.js.map
