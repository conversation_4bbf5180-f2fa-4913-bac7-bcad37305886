/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
page.data-v-26c9e6d7,
.container.data-v-26c9e6d7 {
  background: #fff;
}
.container.data-v-26c9e6d7 {
  padding-bottom: 70rpx;
}
.m-top60.data-v-26c9e6d7 {
  margin-top: 60rpx;
}
.account-panel.data-v-26c9e6d7 {
  width: 650rpx;
  height: 180rpx;
  margin: 50rpx auto;
  padding: 0 60rpx;
  box-sizing: border-box;
  border-radius: 12rpx;
  color: #fff;
  background: linear-gradient(-125deg, #a46bff, #786cff);
  box-shadow: 0 5px 22px 0 rgba(0, 0, 0, 0.26);
}
.panel-lable.data-v-26c9e6d7 {
  font-size: 32rpx;
}
.recharge-label.data-v-26c9e6d7 {
  color: #333333;
  font-size: 30rpx;
  margin-bottom: 25rpx;
}
.panel-balance.data-v-26c9e6d7 {
  text-align: right;
  font-size: 46rpx;
}
.recharge-panel.data-v-26c9e6d7 {
  margin-top: 60rpx;
  padding: 0 60rpx;
}
.recharge-plan.data-v-26c9e6d7 {
  margin-bottom: -20rpx;
}
.recharge-plan .recharge-plan_item.data-v-26c9e6d7 {
  width: 192rpx;
  padding: 15rpx 0;
  float: left;
  text-align: center;
  color: #888;
  border: 1rpx solid #e4e4e4;
  border-radius: 10rpx;
  margin: 0 20rpx 20rpx 0;
}
.recharge-plan .recharge-plan_item.data-v-26c9e6d7:nth-child(3n+0) {
  margin-right: 0;
}
.recharge-plan .recharge-plan_item.active.data-v-26c9e6d7 {
  color: #786cff;
  border: 1rpx solid #786cff;
}
.recharge-plan .recharge-plan_item.active .plan_money.data-v-26c9e6d7 {
  color: #786cff;
}
.plan_money.data-v-26c9e6d7 {
  font-size: 32rpx;
  color: #525252;
}
.plan_gift.data-v-26c9e6d7 {
  font-size: 25rpx;
}
.recharge-input.data-v-26c9e6d7 {
  margin-top: 40rpx;
}
.recharge-input .input.data-v-26c9e6d7 {
  display: block;
  border: 1rpx solid #e4e4e4;
  border-radius: 10rpx;
  padding: 20rpx 26rpx;
  font-size: 28rpx;
}
.recharge-submit.data-v-26c9e6d7 {
  margin-top: 70rpx;
}
.btn-submit .button.data-v-26c9e6d7 {
  font-size: 30rpx;
  background: #786cff;
  border: none;
  color: white;
  border-radius: 50rpx;
  padding: 0 120rpx;
  line-height: 3;
}
.btn-submit .button[disabled].data-v-26c9e6d7 {
  background: #a098ff;
  border-color: #a098ff;
  color: white;
}
.describe-panel.data-v-26c9e6d7 {
  margin-top: 50rpx;
  padding: 0 60rpx;
}
.describe-panel .content.data-v-26c9e6d7 {
  font-size: 26rpx;
  line-height: 1.6;
  color: #888;
}
.payment-method .pay-item.data-v-26c9e6d7 {
  padding: 14rpx 0;
  font-size: 26rpx;
}
.payment-method .pay-item .item-left_icon.data-v-26c9e6d7 {
  margin-right: 20rpx;
  font-size: 44rpx;
}
.payment-method .pay-item .item-left_icon.wechat.data-v-26c9e6d7 {
  color: #00c800;
}
.payment-method .pay-item .item-left_icon.alipay.data-v-26c9e6d7 {
  color: #009fe8;
}
.payment-method .pay-item .item-left_text.data-v-26c9e6d7 {
  font-size: 30rpx;
}
.payment-method .pay-item .item-right.data-v-26c9e6d7 {
  font-size: 30rpx;
}
.payment-method .pay-item .user-balance.data-v-26c9e6d7 {
  margin-left: 20rpx;
  font-size: 26rpx;
}
.modal-content.data-v-26c9e6d7 {
  padding: 40rpx 48rpx;
  font-size: 30rpx;
  line-height: 50rpx;
  text-align: left;
  color: #606266;
  box-sizing: border-box;
}