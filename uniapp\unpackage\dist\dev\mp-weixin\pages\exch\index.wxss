
.exchange-page.data-v-5a4b5f19 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}
.page-header.data-v-5a4b5f19 {
  text-align: center;
  padding: 40rpx 0;
}
.page-title.data-v-5a4b5f19 {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
}
.status-section.data-v-5a4b5f19 {
  margin: 30rpx 0;
}
.status-card.data-v-5a4b5f19 {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  justify-content: space-around;
}
.status-item.data-v-5a4b5f19 {
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}
.status-label.data-v-5a4b5f19 {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}
.status-value.data-v-5a4b5f19 {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}
.action-btn.data-v-5a4b5f19 {
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #4caf50, #8bc34a);
  color: #fff;
  font-size: 24rpx;
  border-radius: 20rpx;
  border: none;
  margin-top: 10rpx;
  min-width: 120rpx;
}
.action-btn.data-v-5a4b5f19:active {
  transform: scale(0.95);
  transition: transform 0.1s;
}
.product-list.data-v-5a4b5f19 {
  margin-top: 30rpx;
}
.product-card.data-v-5a4b5f19 {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  border: 3rpx solid transparent;
}
.product-card.available.data-v-5a4b5f19 {
  border-color: #4caf50;
}
.product-card.exchanged.data-v-5a4b5f19 {
  opacity: 0.6;
  border-color: #ccc;
}
.product-content.data-v-5a4b5f19 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
}
.product-left.data-v-5a4b5f19 {
  flex: 1;
}
.product-title.data-v-5a4b5f19 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}
.product-desc.data-v-5a4b5f19 {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 15rpx;
}
.product-conditions.data-v-5a4b5f19 {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}
.condition-text.data-v-5a4b5f19 {
  font-size: 26rpx;
  color: #666;
}
.product-right.data-v-5a4b5f19 {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 15rpx;
}
.exchange-btn.data-v-5a4b5f19 {
  padding: 15rpx 30rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  border-radius: 25rpx;
  border: none;
  min-width: 150rpx;
}
.exchange-btn.disabled.data-v-5a4b5f19 {
  background: #ccc;
  color: #999;
}
.exchange-btn.data-v-5a4b5f19:not(.disabled):active {
  transform: scale(0.95);
  transition: transform 0.1s;
}
.progress-section.data-v-5a4b5f19 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.progress-item.data-v-5a4b5f19 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.progress-label.data-v-5a4b5f19 {
  font-size: 26rpx;
  color: #666;
  min-width: 120rpx;
}
.progress-bar.data-v-5a4b5f19 {
  flex: 1;
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
}
.progress-fill.data-v-5a4b5f19 {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #8bc34a);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}
.progress-text.data-v-5a4b5f19 {
  font-size: 24rpx;
  color: #999;
  min-width: 100rpx;
  text-align: right;
}

/* 弹窗样式 */
.modal-overlay.data-v-5a4b5f19 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.modal-content.data-v-5a4b5f19 {
  background: #fff;
  border-radius: 20rpx;
  width: 650rpx;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
}
.modal-header.data-v-5a4b5f19 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 30rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}
.modal-title.data-v-5a4b5f19 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.modal-close.data-v-5a4b5f19 {
  font-size: 50rpx;
  color: #999;
  cursor: pointer;
  line-height: 1;
}
.modal-body.data-v-5a4b5f19 {
  padding: 40rpx 30rpx;
}
.form-item.data-v-5a4b5f19 {
  margin-bottom: 40rpx;
}
.form-label.data-v-5a4b5f19 {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}
.form-input.data-v-5a4b5f19, .form-textarea.data-v-5a4b5f19 {
  height: 50rpx;
  width: 100%;
  padding: 0rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 30rpx;
  box-sizing: border-box;
  background: #fafafa;
  transition: border-color 0.3s;
}
.form-input.data-v-5a4b5f19:focus, .form-textarea.data-v-5a4b5f19:focus {
  border-color: #ff6b6b;
  background: #fff;
}
.form-textarea.data-v-5a4b5f19 {
  height: 150rpx;
  resize: none;
  line-height: 1.5;
}
.modal-footer.data-v-5a4b5f19 {
  display: flex;
  padding: 30rpx 30rpx 40rpx 30rpx;
  gap: 30rpx;
  border-top: 1rpx solid #eee;
  background: #fafafa;
}
.modal-btn.data-v-5a4b5f19 {
  flex: 1;
  height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}
.cancel-btn.data-v-5a4b5f19 {
  background: #f0f0f0;
  color: #666;
  border: 2rpx solid #e0e0e0;
}
.cancel-btn.data-v-5a4b5f19:active {
  background: #e8e8e8;
  transform: scale(0.98);
}
.confirm-btn.data-v-5a4b5f19 {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}
.confirm-btn.data-v-5a4b5f19:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);
}
