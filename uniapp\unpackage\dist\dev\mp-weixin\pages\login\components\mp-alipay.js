"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_index = require("../../../store/index.js");
const utils_util = require("../../../utils/util.js");
const common_model_Setting = require("../../../common/model/Setting.js");
const _sfc_main = {
  data() {
    return {
      // 商城基本信息
      storeInfo: void 0,
      // 支付宝小程序登录凭证 (code)
      // 提交到后端，用于换取openid
      code: ""
    };
  },
  created() {
    this.getStoreInfo();
  },
  methods: {
    // 获取商城基本信息
    getStoreInfo() {
      common_model_Setting.SettingModel.item("store").then((store) => this.storeInfo = store);
    },
    // 获取authCode
    // https://opendocs.alipay.com/mini/api/openapi-authorize
    getCode() {
      return new Promise((resolve, reject) => {
        my.canIUse("getAuthCode") && my.getAuthCode({
          scopes: "auth_base",
          // auth_base auth_user
          success({ authCode }) {
            common_vendor.index.__f__("log", "at pages/login/components/mp-alipay.vue:59", "getAuthCode success", authCode);
            resolve(authCode);
          },
          fail: reject
        });
      });
    },
    // 获取支付宝用户信息
    getUserProfile() {
      const app = this;
      my.canIUse("getOpenUserInfo") && my.getOpenUserInfo({
        success(res) {
          const response = JSON.parse(res.response).response;
          common_vendor.index.__f__("log", "at pages/login/components/mp-alipay.vue:73", "用户同意了授权", response);
          if (response.code == 1e4) {
            app.onAuthSuccess({ nickName: response.nickName, avatarUrl: response.avatar });
            return;
          }
          const errorMsg = response.code == 40006 ? "my.getOpenUserInfo接口权限不足，请在支付宝小程序控制台绑定产品 “获取会员基础信息”" : response.subMsg;
          app.$error(response.code + " " + errorMsg);
        },
        fail(err) {
          common_vendor.index.__f__("error", "at pages/login/components/mp-alipay.vue:84", "getOpenUserInfo fail", err);
        }
      });
    },
    // 用户取消授权
    handleAuthError({ detail }) {
      common_vendor.index.__f__("log", "at pages/login/components/mp-alipay.vue:91", "handleAuthError", detail);
    },
    // 授权成功事件
    // 这里分为两个逻辑:
    // 1.将authCode和userInfo提交到后端，如果存在该用户 则实现自动登录，无需再填写手机号
    // 2.如果不存在该用户, 则显示注册页面, 需填写手机号
    // 3.如果后端报错了, 则显示错误信息
    async onAuthSuccess(userInfo) {
      const app = this;
      store_index.store.dispatch("LoginMpAlipay", {
        partyData: {
          code: await app.getCode(),
          oauth: "MP-ALIPAY",
          userInfo
        },
        refereeId: store_index.store.getters.refereeId
      }).then((result) => {
        app.$toast(result.message);
        common_vendor.index.$emit("syncRefresh", true);
        setTimeout(() => app.onNavigateBack(), 2e3);
      }).catch((err) => {
        const resultData = err.result.data;
        if (utils_util.isEmpty(resultData)) {
          app.$toast(err.result.message);
        }
        if (resultData.isBack) {
          setTimeout(() => app.onNavigateBack(1), 2e3);
        }
        if (resultData.isBindMobile) {
          app.onEmitSuccess(userInfo);
        }
      });
    },
    // 将oauth提交给父级
    // 这里要重新获取code, 因为上一次获取的code不能复用(会报错)
    async onEmitSuccess(userInfo) {
      const app = this;
      app.$emit("success", {
        oauth: "MP-ALIPAY",
        // 第三方登录类型: MP-ALIPAY
        code: await app.getCode(),
        // 支付宝登录的authCode, 用于换取openid
        userInfo
        // 支付宝用户信息
      });
    },
    /**
     * 暂不登录
     */
    handleCancel() {
      this.onNavigateBack();
    },
    /**
     * 登录成功-跳转回原页面
     */
    onNavigateBack(delta = 1) {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        common_vendor.index.navigateBack({
          delta: Number(delta || 1)
        });
      } else {
        this.$navTo("pages/index/index");
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.storeInfo && $data.storeInfo.image_url ? $data.storeInfo.image_url : "/static/default-avatar.png",
    b: common_vendor.o((...args) => $options.getUserProfile && $options.getUserProfile(...args)),
    c: common_vendor.o((...args) => $options.handleAuthError && $options.handleAuthError(...args)),
    d: common_vendor.o(($event) => $options.handleCancel())
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0f9e5713"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/login/components/mp-alipay.js.map
