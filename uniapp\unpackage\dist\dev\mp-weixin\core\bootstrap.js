"use strict";
const store_index = require("../store/index.js");
const utils_storage = require("../utils/storage.js");
const core_config_index = require("./config/index.js");
const core_platform = require("./platform.js");
const store_mutationTypes = require("../store/mutation-types.js");
function YoShop2() {
  store_index.store.commit("SET_STORE_ID", core_config_index.Config.getStoreId());
  store_index.store.commit("SET_PLATFORM", core_platform.platfrom);
  store_index.store.commit("SET_TOKEN", utils_storage.storage.get(store_mutationTypes.ACCESS_TOKEN));
  store_index.store.commit("SET_USER_ID", utils_storage.storage.get(store_mutationTypes.USER_ID));
  store_index.store.commit("SET_REFEREE_ID", utils_storage.storage.get(store_mutationTypes.REFEREE_ID));
  store_index.store.commit("SET_APP_THEME", utils_storage.storage.get(store_mutationTypes.APP_THEME));
  store_index.store.commit("SET_MODULES", utils_storage.storage.get(store_mutationTypes.MODULES));
}
exports.YoShop2 = YoShop2;
//# sourceMappingURL=../../.sourcemap/mp-weixin/core/bootstrap.js.map
