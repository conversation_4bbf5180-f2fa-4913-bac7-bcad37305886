/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.diy-coupon .coupon-wrapper.data-v-1a1beb79 {
  display: flex;
  width: -webkit-max-content;
  width: max-content;
  height: 130rpx;
  padding: 0 24rpx;
}
.diy-coupon .coupon-wrapper.data-v-1a1beb79::-webkit-scrollbar {
  display: none;
}
.coupon-item.data-v-1a1beb79 {
  flex-shrink: 0;
  width: 300rpx;
  height: 130rpx;
  position: relative;
  color: #fff;
  overflow: hidden;
  box-sizing: border-box;
  margin-right: 40rpx;
  display: flex;
}
.coupon-item.data-v-1a1beb79:last-child {
  margin-right: 0 !important;
}
.coupon-item.disable .left-content.data-v-1a1beb79 {
  background: linear-gradient(-113deg, #bdbdbd, #a2a1a2) !important;
}
.coupon-item.disable .right-receive.data-v-1a1beb79 {
  background-color: #949494 !important;
}
.coupon-item .before.data-v-1a1beb79 {
  content: "";
  position: absolute;
  z-index: 1;
  width: 32rpx;
  height: 32rpx;
  top: 45%;
  left: -16rpx;
  transform: translateY(-50%);
  border-radius: 80%;
  background-color: #fff;
}
.coupon-item .left-content.data-v-1a1beb79 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 70%;
  height: 100%;
  background-color: #E5004F;
  font-size: 24rpx;
}
.coupon-item .left-content .content-top .unit.data-v-1a1beb79 {
  font-size: 15px;
}
.coupon-item .left-content .content-top .price.data-v-1a1beb79 {
  font-size: 44rpx;
}
.coupon-item .right-receive.data-v-1a1beb79 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 30%;
  height: 100%;
  background-color: #4e4e4e;
  text-align: center;
  font-size: 24rpx;
}