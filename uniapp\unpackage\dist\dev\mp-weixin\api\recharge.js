"use strict";
const utils_request_index = require("../utils/request/index.js");
const api = {
  center: "recharge/center",
  submit: "recharge/submit",
  tradeQuery: "recharge/tradeQuery"
};
const center = (param) => {
  return utils_request_index.$http.get(api.center, param);
};
const submit = (data) => {
  return utils_request_index.$http.post(api.submit, { form: data });
};
function tradeQuery(param) {
  return utils_request_index.$http.get(api.tradeQuery, param);
}
exports.center = center;
exports.submit = submit;
exports.tradeQuery = tradeQuery;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/recharge.js.map
