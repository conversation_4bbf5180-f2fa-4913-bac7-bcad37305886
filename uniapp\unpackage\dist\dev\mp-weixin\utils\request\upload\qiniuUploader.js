"use strict";
const common_vendor = require("../../../common/vendor.js");
var config = {
  qiniuRegion: "",
  qiniuImageURLPrefix: "",
  qiniuUploadToken: "",
  qiniuUploadTokenURL: "",
  qiniuUploadTokenFunction: null,
  qiniuShouldUseQiniuFileName: false
};
function updateConfigWithOptions(options) {
  if (options.region) {
    config.qiniuRegion = options.region;
  } else {
    common_vendor.index.__f__("error", "at utils/request/upload/qiniuUploader.js:30", "qiniu uploader need your bucket region");
  }
  if (options.uptoken) {
    config.qiniuUploadToken = options.uptoken;
  } else if (options.uptokenURL) {
    config.qiniuUploadTokenURL = options.uptokenURL;
  } else if (options.uptokenFunc) {
    config.qiniuUploadTokenFunction = options.uptokenFunc;
  }
  if (options.domain) {
    config.qiniuImageURLPrefix = options.domain;
  }
  config.qiniuShouldUseQiniuFileName = options.shouldUseQiniuFileName;
}
function upload(filePath, success, fail, options, progress, cancelTask) {
  if (null == filePath) {
    common_vendor.index.__f__("error", "at utils/request/upload/qiniuUploader.js:47", "qiniu uploader need filePath to upload");
    return;
  }
  if (options) {
    updateConfigWithOptions(options);
  }
  if (config.qiniuUploadToken) {
    doUpload(filePath, success, fail, options, progress, cancelTask);
  } else if (config.qiniuUploadTokenURL) {
    getQiniuToken(function() {
      doUpload(filePath, success, fail, options, progress, cancelTask);
    });
  } else if (config.qiniuUploadTokenFunction) {
    config.qiniuUploadToken = config.qiniuUploadTokenFunction();
    if (null == config.qiniuUploadToken && config.qiniuUploadToken.length > 0) {
      common_vendor.index.__f__("error", "at utils/request/upload/qiniuUploader.js:62", "qiniu UploadTokenFunction result is null, please check the return value");
      return;
    }
    doUpload(filePath, success, fail, options, progress, cancelTask);
  } else {
    common_vendor.index.__f__("error", "at utils/request/upload/qiniuUploader.js:67", "qiniu uploader need one of [uptoken, uptokenURL, uptokenFunc]");
    return;
  }
}
function doUpload(filePath, success, fail, options, progress, cancelTask) {
  if (null == config.qiniuUploadToken && config.qiniuUploadToken.length > 0) {
    common_vendor.index.__f__("error", "at utils/request/upload/qiniuUploader.js:74", "qiniu UploadToken is null, please check the init config or networking");
    return;
  }
  var url = uploadURLFromRegionCode(config.qiniuRegion);
  var fileName = filePath.split("//")[1];
  if (options && options.key) {
    fileName = options.key;
  }
  var formData = {
    "token": config.qiniuUploadToken
  };
  if (!config.qiniuShouldUseQiniuFileName) {
    formData["key"] = fileName;
  }
  var uploadTask = common_vendor.wx$1.uploadFile({
    url,
    filePath,
    name: "file",
    formData,
    success: function(res) {
      var dataString = res.data;
      if (res.data.hasOwnProperty("type") && res.data.type === "Buffer") {
        dataString = String.fromCharCode.apply(null, res.data.data);
      }
      try {
        var dataObject = JSON.parse(dataString);
        var imageUrl = config.qiniuImageURLPrefix + "/" + dataObject.key;
        dataObject.imageURL = imageUrl;
        if (success) {
          success(dataObject);
        }
      } catch (e) {
        common_vendor.index.__f__("log", "at utils/request/upload/qiniuUploader.js:107", "parse JSON failed, origin String is: " + dataString);
        if (fail) {
          fail(e);
        }
      }
    },
    fail: function(error) {
      common_vendor.index.__f__("error", "at utils/request/upload/qiniuUploader.js:114", error);
      if (fail) {
        fail(error);
      }
    }
  });
  uploadTask.onProgressUpdate((res) => {
    progress && progress(res);
  });
  cancelTask && cancelTask(() => {
    uploadTask.abort();
  });
}
function getQiniuToken(callback) {
  common_vendor.wx$1.request({
    url: config.qiniuUploadTokenURL,
    success: function(res) {
      var token = res.data.uptoken;
      if (token && token.length > 0) {
        config.qiniuUploadToken = token;
        if (callback) {
          callback();
        }
      } else {
        common_vendor.index.__f__("error", "at utils/request/upload/qiniuUploader.js:141", "qiniuUploader cannot get your token, please check the uptokenURL or server");
      }
    },
    fail: function(error) {
      common_vendor.index.__f__("error", "at utils/request/upload/qiniuUploader.js:145", "qiniu UploadToken is null, please check the init config or networking: " + error);
    }
  });
}
function uploadURLFromRegionCode(code) {
  var uploadURL = null;
  switch (code) {
    case "ECN":
      uploadURL = "https://up.qbox.me";
      break;
    case "NCN":
      uploadURL = "https://up-z1.qbox.me";
      break;
    case "SCN":
      uploadURL = "https://up-z2.qbox.me";
      break;
    case "NA":
      uploadURL = "https://up-na0.qbox.me";
      break;
    case "ASG":
      uploadURL = "https://up-as0.qbox.me";
      break;
    default:
      common_vendor.index.__f__("error", "at utils/request/upload/qiniuUploader.js:158", "please make the region is with one of [ECN, SCN, NCN, NA, ASG]");
  }
  return uploadURL;
}
exports.upload = upload;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/utils/request/upload/qiniuUploader.js.map
