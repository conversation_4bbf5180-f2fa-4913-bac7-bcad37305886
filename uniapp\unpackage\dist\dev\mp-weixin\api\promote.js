"use strict";
const utils_request_index = require("../utils/request/index.js");
const api = {
  activeList: "promote/activeList",
  updateViewsNum: "promote/updateViewsNum",
  updateClickNum: "promote/updateClickNum"
};
const activeList = (param) => {
  return utils_request_index.$http.get(api.activeList, param, { load: false, isPrompt: false });
};
const updateViewsNum = (promoteId) => {
  return utils_request_index.$http.post(api.updateViewsNum, { promoteId }, { load: false, isPrompt: false });
};
const updateClickNum = (promoteId) => {
  return utils_request_index.$http.post(api.updateClickNum, { promoteId }, { load: false, isPrompt: false });
};
exports.activeList = activeList;
exports.updateClickNum = updateClickNum;
exports.updateViewsNum = updateViewsNum;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/promote.js.map
