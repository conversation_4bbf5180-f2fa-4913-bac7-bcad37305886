/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.detail-header.data-v-ae660660 {
  position: relative;
  width: 100%;
  height: 140rpx;
}
.detail-header .header-backdrop.data-v-ae660660 {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
}
.detail-header .header-backdrop .image.data-v-ae660660 {
  display: block;
  width: 100%;
  height: 140rpx;
}
.header-state.data-v-ae660660 {
  z-index: 1;
  padding: 0 50rpx;
}

/* 商品详情 */
.detail-goods.data-v-ae660660 {
  padding: 24rpx 20rpx;
}
.detail-goods .left .goods-image.data-v-ae660660 {
  display: block;
  width: 150rpx;
  height: 150rpx;
}
.detail-goods .right.data-v-ae660660 {
  padding-left: 20rpx;
}
.detail-goods .goods-props.data-v-ae660660 {
  margin-top: 14rpx;
  color: #ababab;
  font-size: 24rpx;
  overflow: hidden;
}
.detail-goods .goods-props .goods-props-item.data-v-ae660660 {
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
  background-color: #fcfcfc;
}
.detail-order.data-v-ae660660 {
  padding: 15rpx 20rpx;
  font-size: 26rpx;
}
.detail-order .item.data-v-ae660660 {
  margin-bottom: 10rpx;
}
.detail-order .item.data-v-ae660660:last-child {
  margin-bottom: 0;
}

/* 售后详情 */
.detail-refund.data-v-ae660660 {
  padding: 15rpx 20rpx;
}
.detail-refund__row.data-v-ae660660 {
  margin: 20rpx 0;
}

/* 申请凭证 */
.image-list.data-v-ae660660 {
  margin-bottom: -15rpx;
}
.image-list .image-preview.data-v-ae660660 {
  margin: 0 15rpx 15rpx 0;
  float: left;
}
.image-list .image-preview .image.data-v-ae660660 {
  display: block;
  width: 180rpx;
  height: 180rpx;
}
.image-list .image-preview.data-v-ae660660:nth-child(3n+0) {
  margin-right: 0;
}

/* 商家收货地址 */
.detail-address.data-v-ae660660 {
  padding: 20rpx 34rpx;
}
.address-details.data-v-ae660660 {
  padding: 8rpx 0;
  border-bottom: 1px solid #eee;
}
.address-details .address-details__row.data-v-ae660660 {
  margin: 14rpx 0;
}
.address-tips.data-v-ae660660 {
  margin-top: 16rpx;
  line-height: 46rpx;
}

/* 填写物流信息 */
.detail-express.data-v-ae660660 {
  padding: 10rpx 30rpx;
}
.form-group.data-v-ae660660 {
  height: 60rpx;
  margin: 14rpx 0;
}
.form-group .input.data-v-ae660660 {
  display: block;
  height: 100%;
  font-size: 28rpx;
  padding-left: 0;
}

/* 底部操作栏 */
.footer.data-v-ae660660 {
  margin-top: 60rpx;
}
.footer .btn-wrapper.data-v-ae660660 {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}
.footer .btn-item.data-v-ae660660 {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  color: #fff;
  border-radius: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.footer .btn-item-main.data-v-ae660660 {
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
}
.footer .btn-item-main.disabled.data-v-ae660660 {
  opacity: 0.6;
}