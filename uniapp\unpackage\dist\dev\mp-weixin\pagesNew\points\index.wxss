/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.header.data-v-f6877ae0 {
  display: flex;
  align-items: center;
  background-color: #fff;
}
.header .search.data-v-f6877ae0 {
  width: 90%;
  margin: 0 10rpx;
}
.header .show-view.data-v-f6877ae0 {
  width: 60rpx;
  height: 60rpx;
  font-size: 36rpx;
  color: #505050;
  display: flex;
  align-items: center;
}
.store-sort.data-v-f6877ae0 {
  position: -webkit-sticky;
  position: sticky;
  top: var(--window-top);
  display: flex;
  padding: 20rpx 0;
  font-size: 28rpx;
  background: #fff;
  color: #000;
  z-index: 99;
}
.store-sort .sort-item.data-v-f6877ae0 {
  flex-basis: 33.3333%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50rpx;
}
.store-sort .sort-item.active.data-v-f6877ae0 {
  color: var(--main-bg);
}
.store-sort .sort-item-price .price-arrow.data-v-f6877ae0 {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #000;
}
.store-sort .sort-item-price .price-arrow .icon.active.data-v-f6877ae0 {
  color: var(--main-bg);
}
.store-sort .sort-item-price .price-arrow .icon.up.data-v-f6877ae0 {
  margin-bottom: -16rpx;
}
.store-sort .sort-item-price .price-arrow .icon.down.data-v-f6877ae0 {
  margin-top: -16rpx;
}
.goods-list.data-v-f6877ae0 {
  padding: 4rpx;
  box-sizing: border-box;
}
.goods-list.column-1 .goods-item.data-v-f6877ae0 {
  width: 100%;
  height: 280rpx;
  margin-bottom: 12rpx;
  padding: 20rpx;
  box-sizing: border-box;
  background: #fff;
  line-height: 1.6;
}
.goods-list.column-1 .goods-item.data-v-f6877ae0:last-child {
  margin-bottom: 0;
}
.goods-list.column-1 .goods-item-left.data-v-f6877ae0 {
  display: flex;
  width: 300rpx;
  background: #fff;
  align-items: center;
}
.goods-list.column-1 .goods-item-left .image.data-v-f6877ae0 {
  display: block;
  width: 240rpx;
  height: 240rpx;
}
.goods-list.column-1 .goods-item-right.data-v-f6877ae0 {
  position: relative;
  flex: 1;
}
.goods-list.column-1 .goods-item-right .goods-name.data-v-f6877ae0 {
  margin-top: 10rpx;
  white-space: normal;
  color: #484848;
  font-size: 26rpx;
  min-height: 68rpx;
  line-height: 1.3;
}
.goods-list.column-1 .goods-item-desc.data-v-f6877ae0 {
  margin-top: 8rpx;
}
.goods-list.column-1 .desc-selling-point.data-v-f6877ae0 {
  width: 400rpx;
  font-size: 24rpx;
  color: #e49a3d;
}
.goods-list.column-1 .desc-goods-sales.data-v-f6877ae0 {
  color: #999;
  font-size: 24rpx;
}
.goods-list.column-1 .desc-footer.data-v-f6877ae0 {
  font-size: 24rpx;
}
.goods-list.column-1 .desc-footer .price-x.data-v-f6877ae0 {
  margin-right: 16rpx;
  color: var(--main-bg);
  font-size: 30rpx;
}
.goods-list.column-1 .desc-footer .price-y.data-v-f6877ae0 {
  text-decoration: line-through;
}
.goods-list.column-2 .goods-item.data-v-f6877ae0 {
  width: 50%;
}
.goods-item.data-v-f6877ae0 {
  float: left;
  box-sizing: border-box;
  padding: 6rpx;
}
.goods-item .goods-image.data-v-f6877ae0 {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  overflow: hidden;
  background: #fff;
}
.goods-item .goods-image.data-v-f6877ae0:after {
  content: "";
  display: block;
  margin-top: 100%;
}
.goods-item .goods-image .image.data-v-f6877ae0 {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  object-fit: cover;
}
.goods-item .detail.data-v-f6877ae0 {
  padding: 8rpx;
  background: #fff;
}
.goods-item .detail .goods-name.data-v-f6877ae0 {
  white-space: normal;
  color: #484848;
  font-size: 26rpx;
  margin-bottom: 4rpx;
  min-height: 68rpx;
  line-height: 1.3;
}
.goods-item .detail .detail-price .goods-price.data-v-f6877ae0 {
  margin-right: 8rpx;
}
.goods-item .detail .detail-price .line-price.data-v-f6877ae0 {
  text-decoration: line-through;
}