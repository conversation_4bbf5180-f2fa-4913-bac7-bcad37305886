"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "points.withdraw/list",
  getData: "points.log/getData",
  center: "points.log/center",
  submit: "points.withdraw/submit"
};
const list = (param) => {
  return utils_request_index.$http.get(api.list, param);
};
exports.list = list;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/points/withdraw.js.map
