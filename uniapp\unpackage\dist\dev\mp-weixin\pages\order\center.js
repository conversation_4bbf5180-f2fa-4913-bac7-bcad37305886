"use strict";
const api_user = require("../../api/user.js");
const core_app = require("../../core/app.js");
const common_vendor = require("../../common/vendor.js");
const orderList = core_app.filterModules([
  {
    name: "商城订单",
    icon: "icon-qpdingdan",
    color: "rgb(253 65 0)",
    path: "pages/order/index"
  },
  {
    name: "充值订单",
    icon: "icon-jifen",
    color: "rgb(191, 150, 7)",
    path: "pages/wallet/recharge/order",
    moduleKey: "market-recharge"
  },
  {
    name: "我的拼团",
    icon: "icon-qpdingdan",
    color: "rgb(16, 183, 52)",
    path: "pages/groupon/index?tab=1",
    moduleKey: "apps-groupon"
  },
  {
    name: "我的砍价",
    icon: "icon-qpdingdan",
    color: "rgb(37, 189, 221)",
    path: "pages/bargain/index?tab=1",
    moduleKey: "apps-bargain"
  }
]);
const _sfc_main = {
  data() {
    return {
      orderList
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getUserInfo();
  },
  methods: {
    // 获取当前用户信息（验证是否登录）
    getUserInfo() {
      api_user.info();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.orderList, (item, index, i0) => {
      return {
        a: common_vendor.n(item.icon),
        b: item.color,
        c: common_vendor.t(item.name),
        d: index,
        e: common_vendor.o(($event) => _ctx.$navTo(item.path), index)
      };
    }),
    b: common_vendor.o(($event) => _ctx.$navTo("pages/index/index")),
    c: common_vendor.s(_ctx.appThemeStyle)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a6b0dc36"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/center.js.map
