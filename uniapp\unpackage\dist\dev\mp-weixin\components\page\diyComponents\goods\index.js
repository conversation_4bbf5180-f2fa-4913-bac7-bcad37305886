"use strict";
const utils_util = require("../../../../utils/util.js");
const core_app = require("../../../../core/app.js");
const common_vendor = require("../../../../common/vendor.js");
const AddCartPopup = () => "../../../add-cart-popup/index.js";
const _sfc_main = {
  components: {
    AddCartPopup
  },
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object,
    dataList: Array
  },
  data() {
    return { inArray: utils_util.inArray };
  },
  methods: {
    // 跳转商品详情页
    handleGoodsItem(goodsId) {
      this.$navTo(`pages/goods/detail`, { goodsId });
    },
    // 点击加入购物车
    handleAddCart(item) {
      this.$refs.AddCartPopup.handle(item);
    },
    // 更新购物车角标
    onAddCart(total) {
      core_app.setCartTabBadge();
    }
  }
};
if (!Array) {
  const _component_AddCartPopup = common_vendor.resolveComponent("AddCartPopup");
  _component_AddCartPopup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.dataList, (dataItm, dataIdx, i0) => {
      return common_vendor.e($props.itemStyle.column === 1 ? common_vendor.e({
        a: dataItm.goods_image,
        b: $data.inArray("goodsName", $props.itemStyle.show)
      }, $data.inArray("goodsName", $props.itemStyle.show) ? {
        c: common_vendor.t(dataItm.goods_name),
        d: common_vendor.n($props.itemStyle.goodsNameRows == "two" ? "twoline-hide" : "oneline-hide"),
        e: common_vendor.n(`row-${$props.itemStyle.goodsNameRows}`)
      } : {}, {
        f: $data.inArray("sellingPoint", $props.itemStyle.show)
      }, $data.inArray("sellingPoint", $props.itemStyle.show) ? {
        g: common_vendor.t(dataItm.selling_point),
        h: $props.itemStyle.sellingColor
      } : {}, {
        i: $data.inArray("goodsSales", $props.itemStyle.show)
      }, $data.inArray("goodsSales", $props.itemStyle.show) ? {
        j: common_vendor.t(dataItm.goods_sales)
      } : {}, {
        k: $data.inArray("goodsPrice", $props.itemStyle.show)
      }, $data.inArray("goodsPrice", $props.itemStyle.show) ? {
        l: common_vendor.t(Number(dataItm.goods_price_min))
      } : {}, {
        m: $data.inArray("linePrice", $props.itemStyle.show)
      }, $data.inArray("linePrice", $props.itemStyle.show) ? {
        n: common_vendor.t(dataItm.line_price_min)
      } : {}, {
        o: $props.itemStyle.priceColor,
        p: common_vendor.n(`icon-jiagou${$props.itemStyle.btnCartStyle}`),
        q: $props.itemStyle.btnCartColor,
        r: common_vendor.o(($event) => $options.handleAddCart(dataItm), dataIdx),
        s: $data.inArray("cartBtn", $props.itemStyle.show) && $props.itemStyle.column < 3
      }) : common_vendor.e({
        t: dataItm.goods_image,
        v: $data.inArray("goodsName", $props.itemStyle.show)
      }, $data.inArray("goodsName", $props.itemStyle.show) ? {
        w: common_vendor.t(dataItm.goods_name),
        x: common_vendor.n($props.itemStyle.goodsNameRows == "two" ? "twoline-hide" : "oneline-hide"),
        y: common_vendor.n(`row-${$props.itemStyle.goodsNameRows}`)
      } : {}, {
        z: $data.inArray("sellingPoint", $props.itemStyle.show)
      }, $data.inArray("sellingPoint", $props.itemStyle.show) ? {
        A: common_vendor.t(dataItm.selling_point),
        B: $props.itemStyle.sellingColor
      } : {}, {
        C: $data.inArray("goodsSales", $props.itemStyle.show)
      }, $data.inArray("goodsSales", $props.itemStyle.show) ? {
        D: common_vendor.t(dataItm.goods_sales)
      } : {}, {
        E: $data.inArray("goodsPrice", $props.itemStyle.show)
      }, $data.inArray("goodsPrice", $props.itemStyle.show) ? {
        F: common_vendor.t(dataItm.goods_price_min),
        G: $props.itemStyle.priceColor
      } : {}, {
        H: common_vendor.n(`icon-jiagou${$props.itemStyle.btnCartStyle}`),
        I: $props.itemStyle.btnCartColor,
        J: common_vendor.o(($event) => $options.handleAddCart(dataItm), dataIdx),
        K: $data.inArray("cartBtn", $props.itemStyle.show) && $props.itemStyle.column < 3
      }), {
        L: dataIdx,
        M: common_vendor.o(($event) => $options.handleGoodsItem(dataItm.goods_id), dataIdx)
      });
    }),
    b: $props.itemStyle.column === 1,
    c: common_vendor.n(`display-${$props.itemStyle.cardType}`),
    d: `${$props.itemStyle.itemMargin * 2}rpx`,
    e: `${$props.itemStyle.borderRadius * 2}rpx`,
    f: common_vendor.n(`display-${$props.itemStyle.display}`),
    g: common_vendor.n(`column-${$props.itemStyle.column}`),
    h: `${$props.itemStyle.paddingY * 2}rpx ${$props.itemStyle.paddingX * 2}rpx`,
    i: common_vendor.sr("AddCartPopup", "be2a91ee-0"),
    j: common_vendor.o($options.onAddCart),
    k: $props.itemStyle.display === "slide",
    l: $props.itemStyle.background
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-be2a91ee"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/components/page/diyComponents/goods/index.js.map
