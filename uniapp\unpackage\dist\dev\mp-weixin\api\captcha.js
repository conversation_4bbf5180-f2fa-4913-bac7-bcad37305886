"use strict";
const utils_request_index = require("../utils/request/index.js");
const api = {
  image: "captcha/image",
  sendSmsCaptcha: "captcha/sendSmsCaptcha"
};
function image() {
  return utils_request_index.$http.get(api.image, {}, { load: false });
}
function sendSmsCaptcha(data) {
  return utils_request_index.$http.post(api.sendSmsCaptcha, data);
}
exports.image = image;
exports.sendSmsCaptcha = sendSmsCaptcha;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/captcha.js.map
