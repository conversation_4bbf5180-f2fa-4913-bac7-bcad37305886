"use strict";
const uni_modules_vkUviewUi_libs_util_emitter = require("../../uni_modules/vk-uview-ui/libs/util/emitter.js");
const common_vendor = require("../../common/vendor.js");
const common_model_Region = require("../../common/model/Region.js");
const findOptionsKey = (data, searchValue, deep = 1, keys = []) => {
  const index = data.findIndex((item) => item.value === searchValue[deep - 1]);
  if (index > -1) {
    keys.push(index);
    if (data[index].children) {
      findOptionsKey(data[index].children, searchValue, ++deep, keys);
    }
  }
  return keys;
};
const _sfc_main = {
  name: "SelectRegion",
  mixins: [uni_modules_vkUviewUi_libs_util_emitter.Emitter],
  emits: ["update:modelValue"],
  props: {
    // 当前选中的值
    modelValue: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 未选中时的提示文字
    placeholder: {
      type: String,
      default: "请选择省/市/区"
    }
  },
  data() {
    return {
      // 正在加载
      isLoading: true,
      // 是否显示
      show: false,
      // 默认选中的值
      defaultValue: [],
      // 选中项内容(文本展示)
      valueText: "",
      // 级联选择器数据
      options: []
    };
  },
  watch: {
    // 监听当前选中的值
    modelValue(val) {
      this.valueText = val.map((item) => item.label).join("/");
      this.setDefaultValue(val);
    }
  },
  created() {
    this.getTreeDataSync();
  },
  methods: {
    // 打开选择器
    handleSelect() {
      this.show = true;
    },
    // // 获取地区数据 (异步)
    // getTreeData() {
    //   const app = this
    //   app.isLoading = true
    //   RegionModel.getTreeData()
    //     .then(regions => {
    //       // 格式化级联选择器数据
    //       app.options = app.getOptions(regions)
    //       app.setDefaultValue(app.modelValue)
    //     })
    //     .finally(() => app.isLoading = false)
    // },
    // 获取地区数据 (同步)
    getTreeDataSync() {
      const app = this;
      const regions = common_model_Region.RegionModel.getTreeDataSync();
      app.options = app.getOptions(regions);
      app.setDefaultValue(app.modelValue);
      app.isLoading = false;
    },
    // 确认选择后的回调
    onConfirm(value) {
      this.$emit("update:modelValue", value);
    },
    /**
     * 设置默认选中的值
     * 该操作是为了每次打开选择器时聚焦到上次选择
     * @param {Object} value
     */
    setDefaultValue(value) {
      const options = this.options;
      const values = value.map((item) => item.value);
      this.defaultValue = options.length ? findOptionsKey(options, values) : [];
    },
    /**
     * 格式化级联选择器数据
     * @param {*} regions 地区数据
     */
    getOptions(regions) {
      const { getOptions, getChildren } = this;
      const options = [];
      for (const index in regions) {
        const item = regions[index];
        const children = getChildren(item);
        const optionItem = {
          value: item.id,
          label: item.name
        };
        if (children !== false) {
          optionItem.children = getOptions(children);
        }
        options.push(optionItem);
      }
      return options;
    },
    // 获取子集地区
    getChildren(item) {
      if (item.city) {
        return item.city;
      }
      if (item.region) {
        return item.region;
      }
      return false;
    },
    // 根据省市区名称获取optionItem
    getOptionItemByNames({ provinceName, cityName, countyName }) {
      const app = this;
      const data = [];
      app.options.forEach((provinceItem) => {
        if (provinceItem.label == provinceName || `${provinceItem.label}市` == provinceName) {
          data.push({ label: provinceItem.label, value: provinceItem.value });
          provinceItem.children.forEach((cityItem) => {
            if (cityItem.label == cityName) {
              data.push({ label: cityItem.label, value: cityItem.value });
              cityItem.children.forEach((countyItem) => {
                if (countyItem.label == countyName) {
                  data.push({ label: countyItem.label, value: countyItem.value });
                }
              });
            }
          });
        }
      });
      return data;
    }
  }
};
if (!Array) {
  const _easycom_u_loading2 = common_vendor.resolveComponent("u-loading");
  const _easycom_u_select2 = common_vendor.resolveComponent("u-select");
  (_easycom_u_loading2 + _easycom_u_select2)();
}
const _easycom_u_loading = () => "../../uni_modules/vk-uview-ui/components/u-loading/u-loading.js";
const _easycom_u_select = () => "../../uni_modules/vk-uview-ui/components/u-select/u-select.js";
if (!Math) {
  (_easycom_u_loading + _easycom_u_select)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isLoading
  }, $data.isLoading ? {
    b: common_vendor.p({
      mode: "circle"
    })
  } : {
    c: common_vendor.t($data.valueText ? $data.valueText : $props.placeholder),
    d: common_vendor.o(($event) => $options.handleSelect())
  }, {
    e: common_vendor.o($options.onConfirm),
    f: common_vendor.o(($event) => $data.show = $event),
    g: common_vendor.p({
      mode: "mutil-column-auto",
      list: $data.options,
      ["default-value"]: $data.defaultValue,
      modelValue: $data.show
    })
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-de36e3a3"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/select-region/select-region.js.map
