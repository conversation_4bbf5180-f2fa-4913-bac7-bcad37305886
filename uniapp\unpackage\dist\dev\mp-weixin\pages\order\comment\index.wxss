/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-1dbe63fd {
  padding-bottom: calc(constant(safe-area-inset-bottom) + 140rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 140rpx);
}
.goods-list.data-v-1dbe63fd {
  font-size: 28rpx;
  padding-top: 30rpx;
}
.goods-item.data-v-1dbe63fd {
  width: 94%;
  background: #fff;
  padding: 24rpx 24rpx;
  box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);
  margin: 0 auto 30rpx auto;
  border-radius: 20rpx;
}
.goods-item .goods-detail.data-v-1dbe63fd {
  padding: 24rpx 20rpx;
}
.goods-item .goods-detail .left .goods-image.data-v-1dbe63fd {
  display: block;
  width: 150rpx;
  height: 150rpx;
}
.goods-item .goods-detail .right.data-v-1dbe63fd {
  padding-left: 20rpx;
}
.goods-item .score-row.data-v-1dbe63fd {
  display: flex;
  justify-content: space-around;
  padding: 24rpx 20rpx;
}
.goods-item .score-row .score-item.data-v-1dbe63fd {
  display: flex;
  justify-content: center;
  align-items: center;
}
.goods-item .score-row .score-item.score-praise.data-v-1dbe63fd {
  color: var(--main-bg);
}
.goods-item .score-row .score-item.score-praise .score-icon.data-v-1dbe63fd {
  background: var(--main-bg);
}
.goods-item .score-row .score-item.score-review.data-v-1dbe63fd {
  color: var(--vice-bg);
}
.goods-item .score-row .score-item.score-review .score-icon.data-v-1dbe63fd {
  background: var(--vice-bg);
}
.goods-item .score-row .score-item.score-negative.data-v-1dbe63fd {
  color: #9b9b9b;
}
.goods-item .score-row .score-item.score-negative .score-icon.data-v-1dbe63fd {
  background: #9b9b9b;
}
.goods-item .score-row .score-item .score.data-v-1dbe63fd {
  padding: 10rpx 20rpx 10rpx 10rpx;
  border-radius: 30rpx;
}
.goods-item .score-row .score-item .score .score-icon.data-v-1dbe63fd {
  margin-right: 10rpx;
  padding: 10rpx;
  border-radius: 50%;
  font-size: 30rpx;
  color: #fff;
}
.goods-item .score-row .score-item.active .score.data-v-1dbe63fd {
  color: #fff;
}
.goods-item .score-row .score-item.active.score-praise .score.data-v-1dbe63fd {
  background: var(--main-bg);
}
.goods-item .score-row .score-item.active.score-review .score.data-v-1dbe63fd {
  background: var(--vice-bg);
}
.goods-item .score-row .score-item.active.score-negative .score.data-v-1dbe63fd {
  background: #9b9b9b;
}
.goods-item .form-content.data-v-1dbe63fd {
  padding: 14rpx 10rpx;
}
.goods-item .form-content .textarea.data-v-1dbe63fd {
  width: 100%;
  height: 220rpx;
  padding: 12rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 5rpx;
  box-sizing: border-box;
  font-size: 26rpx;
}
.goods-item .image-list.data-v-1dbe63fd {
  padding: 0 20rpx;
  margin-top: 20rpx;
  margin-bottom: -20rpx;
}
.goods-item .image-list.data-v-1dbe63fd:after {
  clear: both;
  content: " ";
  display: table;
}
.goods-item .image-list .image.data-v-1dbe63fd {
  display: block;
  width: 100%;
  height: 100%;
}
.goods-item .image-list .image-picker.data-v-1dbe63fd,
.goods-item .image-list .image-preview.data-v-1dbe63fd {
  width: 184rpx;
  height: 184rpx;
  margin-right: 30rpx;
  margin-bottom: 30rpx;
  float: left;
}
.goods-item .image-list .image-picker.data-v-1dbe63fd:nth-child(3n+0),
.goods-item .image-list .image-preview.data-v-1dbe63fd:nth-child(3n+0) {
  margin-right: 0;
}
.goods-item .image-list .image-picker.data-v-1dbe63fd {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1rpx dashed #ccc;
  color: #ccc;
}
.goods-item .image-list .image-picker .choose-icon.data-v-1dbe63fd {
  font-size: 48rpx;
  margin-bottom: 6rpx;
}
.goods-item .image-list .image-picker .choose-text.data-v-1dbe63fd {
  font-size: 24rpx;
}
.goods-item .image-list .image-preview.data-v-1dbe63fd {
  position: relative;
}
.goods-item .image-list .image-preview .image-delete.data-v-1dbe63fd {
  position: absolute;
  top: -15rpx;
  right: -15rpx;
  height: 42rpx;
  width: 42rpx;
  background: rgba(0, 0, 0, 0.64);
  border-radius: 50%;
  color: #fff;
  font-weight: bolder;
  font-size: 22rpx;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}
.goods-main.data-v-1dbe63fd {
  display: flex;
  margin-bottom: 20rpx;
}
.goods-main .goods-image.data-v-1dbe63fd {
  width: 180rpx;
  height: 180rpx;
}
.goods-main .goods-image .image.data-v-1dbe63fd {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.goods-main .goods-content.data-v-1dbe63fd {
  flex: 1;
  padding-left: 16rpx;
}
.goods-main .goods-content .goods-title.data-v-1dbe63fd {
  font-size: 26rpx;
  max-height: 76rpx;
}
.goods-main .goods-content .goods-props.data-v-1dbe63fd {
  margin-top: 14rpx;
  color: #ababab;
  font-size: 24rpx;
  overflow: hidden;
}
.goods-main .goods-content .goods-props .goods-props-item.data-v-1dbe63fd {
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
  background-color: #fcfcfc;
}
.goods-main .goods-trade.data-v-1dbe63fd {
  padding-top: 16rpx;
  width: 150rpx;
  text-align: right;
  color: #999;
  font-size: 26rpx;
}
.goods-main .goods-trade .goods-price.data-v-1dbe63fd {
  vertical-align: bottom;
  margin-bottom: 16rpx;
}
.goods-main .goods-trade .goods-price .unit.data-v-1dbe63fd {
  margin-right: -2rpx;
  font-size: 24rpx;
}
.footer-fixed.data-v-1dbe63fd {
  position: fixed;
  bottom: var(--window-bottom);
  left: 0;
  right: 0;
  z-index: 11;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.footer-fixed .btn-wrapper.data-v-1dbe63fd {
  height: 140rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}
.footer-fixed .btn-item.data-v-1dbe63fd {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  color: #fff;
  border-radius: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.footer-fixed .btn-item-main.data-v-1dbe63fd {
  background: linear-gradient(to right, var(--main-bg), var(--main-bg2));
  color: var(--main-text);
}
.footer-fixed .btn-item-main.disabled.data-v-1dbe63fd {
  opacity: 0.6;
}