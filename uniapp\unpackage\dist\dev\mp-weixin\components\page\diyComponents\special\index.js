"use strict";
const components_page_diyComponents_mixin = require("../mixin.js");
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  /**
   * 组件的属性列表
   * 用于组件自定义设置
   */
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object,
    dataList: Array
  },
  mixins: [components_page_diyComponents_mixin.mixin],
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {
    // 跳转到文章详情页
    handleNavDetail(articleId) {
      this.$navTo("pages/article/detail", { articleId });
    },
    // 跳转到更多
    handleNavMore() {
      this.$navTo("pages/article/index");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $props.params.image,
    b: common_vendor.o(($event) => $options.handleNavMore()),
    c: common_vendor.f($props.dataList, (dataItm, idx, i0) => {
      return {
        a: common_vendor.t(dataItm.title),
        b: common_vendor.o(($event) => $options.handleNavDetail(dataItm.article_id), idx),
        c: idx
      };
    }),
    d: $props.itemStyle.textColor,
    e: $props.itemStyle.display,
    f: common_vendor.n(`display_${$props.params.display}`),
    g: common_vendor.o(($event) => $options.handleNavMore()),
    h: `${$props.itemStyle.paddingTop * 2}rpx 0`,
    i: $props.itemStyle.background
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-2005b94c"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/components/page/diyComponents/special/index.js.map
