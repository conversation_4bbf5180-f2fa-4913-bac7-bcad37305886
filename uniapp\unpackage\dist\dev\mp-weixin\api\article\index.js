"use strict";
const utils_request_index = require("../../utils/request/index.js");
const api = {
  list: "article/list",
  detail: "article/detail"
};
function list(param, option) {
  return utils_request_index.$http.get(api.list, param, option);
}
function detail(articleId) {
  return utils_request_index.$http.get(api.detail, { articleId });
}
exports.detail = detail;
exports.list = list;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/article/index.js.map
