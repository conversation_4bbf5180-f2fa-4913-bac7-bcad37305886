/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.shop-list .shop-item.data-v-0340a2b9 {
  padding: 20rpx 30rpx;
  min-height: 180rpx;
  font-size: 26rpx;
  line-height: 1.5;
  border-bottom: 1px solid #eee;
}
.shop-item__title.data-v-0340a2b9 {
  font-size: 30rpx;
  color: #535353;
  margin-bottom: 10rpx;
}
.shop-item__address.data-v-0340a2b9,
.shop-item__phone.data-v-0340a2b9 {
  color: #919396;
}
.shop-item__distance.data-v-0340a2b9 {
  margin-top: 10rpx;
  color: #c1c1c1;
  height: 40rpx;
}
.shop-item__distance .iconfont.data-v-0340a2b9 {
  color: #81838e;
  margin-right: 5rpx;
}
.shop-item__right.data-v-0340a2b9 {
  margin-left: 20rpx;
  color: #535353;
  font-size: 38rpx;
}
.widget-location.data-v-0340a2b9 {
  position: fixed;
  right: calc(var(--window-right) + 40rpx);
  bottom: calc(var(--window-bottom) + 70rpx);
  width: 72rpx;
  height: 72rpx;
  z-index: 200;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.2);
  color: #555;
  font-size: 40rpx;
}