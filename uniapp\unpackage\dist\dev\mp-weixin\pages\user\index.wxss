/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.p-vip.data-v-79e6a490 {
  height: 1000rpx;
  padding: 20rpx 0rpx;
}
.p-vip .p-title.data-v-79e6a490 {
  text-align: center;
  font-weight: bold;
}
.p-vip .p-content.data-v-79e6a490 {
  height: 900rpx;
  padding: 20rpx 30rpx;
  padding-bottom: 250rpx;
}
.p-vip .p-footer.data-v-79e6a490 {
  position: absolute;
  bottom: 0;
  background: #FFF;
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
  color: #FFF;
}
.p-vip .p-footer .cancel.data-v-79e6a490 {
  background-color: #ff0000;
  padding: 10rpx 30rpx;
  margin: 0 30rpx;
  border-radius: 10rpx;
}
.p-vip .p-footer .queu.data-v-79e6a490 {
  background-color: #cd8c0c;
  padding: 10rpx 30rpx;
  border-radius: 10rpx;
  margin: 0 30rpx;
}
.container.data-v-79e6a490 {
  padding-bottom: 60rpx;
}
.end-time.data-v-79e6a490 {
  color: #616161;
  font-size: 24rpx;
  margin-right: 10rpx;
}
.xufei.data-v-79e6a490 {
  color: #fa2209;
  font-size: 24rpx;
  margin-right: 10rpx;
}
.main-header.data-v-79e6a490 {
  position: relative;
  width: 100%;
  height: 280rpx;
  background-size: 100% 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  padding-left: 30rpx;
}
.main-header .bg-image.data-v-79e6a490 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}
.main-header .user-info.data-v-79e6a490 {
  display: flex;
  height: 100rpx;
  z-index: 1;
  width: 100%;
}
.main-header .user-info .user-content.data-v-79e6a490 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 30rpx;
  width: 100%;
  color: #fa2209;
}
.main-header .user-info .user-content .nick-name.data-v-79e6a490 {
  font-size: 34rpx;
  font-weight: bold;
  max-width: 300rpx;
  margin-right: 10rpx;
}
.main-header .user-info .user-content .mobile.data-v-79e6a490 {
  margin-top: 15rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: space-between;
}
.main-header .user-info .user-content .user-grade2.data-v-79e6a490 {
  margin-top: 10rpx;
  width: 100%;
  justify-content: space-between;
}
.main-header .user-info .user-content .sign.data-v-79e6a490 {
  background-color: #fa2209;
  color: #FFF;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
}
.main-header .user-info .user-content .signed.data-v-79e6a490 {
  background-color: #cccccc;
  color: #FFF;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
}
.main-header .user-info .user-content .user-grade.data-v-79e6a490 {
  align-self: baseline;
  display: flex;
  align-items: center;
  background: #3c3c3c;
  margin-top: 8rpx;
  border-radius: 20rpx;
  padding: 2rpx 10rpx;
}
.main-header .user-info .user-content .user-grade .user-grade_icon .image.data-v-79e6a490 {
  display: block;
  width: 32rpx;
  height: 32rpx;
}
.main-header .user-info .user-content .user-grade .user-grade_name.data-v-79e6a490 {
  margin-left: 5rpx;
  font-size: 24rpx;
  color: #EEE0C3;
}
.main-header .user-info .user-content .login-tips.data-v-79e6a490 {
  margin-top: 12rpx;
  font-size: 30rpx;
}
.item-badge.data-v-79e6a490 {
  position: absolute;
  top: 0;
  right: 55rpx;
  background: #fa2209;
  color: #fff;
  border-radius: 100%;
  min-width: 38rpx;
  height: 38rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rpx;
  font-size: 24rpx;
}
.my-asset.data-v-79e6a490 {
  display: flex;
  background: #fff;
  padding: 40rpx 0;
}
.my-asset .asset-right.data-v-79e6a490 {
  width: 200rpx;
  border-left: 1rpx solid #eee;
}
.my-asset .asset-right-item.data-v-79e6a490 {
  text-align: center;
  color: #545454;
}
.my-asset .asset-right-item .item-icon.data-v-79e6a490 {
  font-size: 44rpx;
}
.my-asset .asset-right-item .item-name.data-v-79e6a490 {
  margin-top: 14rpx;
  font-size: 28rpx;
}
.my-asset .asset-left-item.data-v-79e6a490 {
  max-width: 183rpx;
  text-align: center;
  color: #666;
  padding: 0 16rpx;
}
.my-asset .asset-left-item .item-value.data-v-79e6a490 {
  font-size: 34rpx;
  color: var(--main-bg);
}
.my-asset .asset-left-item .item-name.data-v-79e6a490 {
  margin-top: 14rpx;
  font-size: 28rpx;
}
.order-navbar.data-v-79e6a490 {
  display: flex;
  margin: 20rpx auto 20rpx auto;
  padding: 20rpx 0 26rpx 0;
  width: 94%;
  box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);
  font-size: 30rpx;
  border-radius: 5rpx;
  background: #fff;
}
.order-navbar-item.data-v-79e6a490 {
  position: relative;
  width: 25%;
}
.order-navbar-item .item-icon.data-v-79e6a490 {
  text-align: center;
  margin: 0 auto;
  padding: 10rpx 0;
  color: #545454;
  font-size: 44rpx;
}
.order-navbar-item .item-name.data-v-79e6a490 {
  font-size: 28rpx;
  color: #545454;
  text-align: center;
  margin-right: 10rpx;
}
.my-service.data-v-79e6a490 {
  margin: 22rpx auto 22rpx auto;
  padding: 22rpx 0;
  width: 94%;
  box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);
  border-radius: 5rpx;
  background: #fff;
}
.my-service .service-title.data-v-79e6a490 {
  padding-left: 24rpx;
  margin-bottom: 20rpx;
  font-size: 30rpx;
}
.my-service .service-content.data-v-79e6a490 {
  margin-bottom: -20rpx;
}
.my-service .service-content .service-item.data-v-79e6a490 {
  position: relative;
  width: 25%;
  float: left;
  margin-bottom: 30rpx;
}
.my-service .service-content .service-item .item-icon.data-v-79e6a490 {
  text-align: center;
  margin: 0 auto;
  padding: 14rpx 0;
  color: var(--main-bg);
  font-size: 44rpx;
}
.my-service .service-content .service-item .item-name.data-v-79e6a490 {
  font-size: 28rpx;
  color: #545454;
  text-align: center;
}
.my-logout.data-v-79e6a490 {
  display: flex;
  justify-content: center;
  margin-top: 50rpx;
}
.my-logout .logout-btn.data-v-79e6a490 {
  width: 60%;
  margin: 0 auto;
  font-size: 28rpx;
  color: #616161;
  border-radius: 20rpx;
  border: 1px solid #dcdcdc;
  padding: 16rpx 0;
  text-align: center;
}
.my-mobile.data-v-79e6a490 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 40rpx;
  background: #FCEBD1;
}
.my-mobile .info.data-v-79e6a490 {
  color: #cd8c0c;
  font-size: 28rpx;
}
.my-mobile .btn-bind.data-v-79e6a490 {
  padding: 8rpx 24rpx;
  background-color: #EAB766;
  color: #fff;
  border-radius: 30rpx;
  font-size: 26rpx;
  text-align: center;
}
.my-mobile2.data-v-79e6a490 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx auto 20rpx auto;
  padding: 12rpx 40rpx;
  width: 94%;
  box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);
  font-size: 30rpx;
  border-radius: 5rpx;
  background: #fff;
}
.my-mobile2 .info.data-v-79e6a490 {
  font-size: 26rpx;
}
.my-mobile2 .btn-bind.data-v-79e6a490 {
  padding: 8rpx 24rpx;
  background-color: #EAB766;
  color: #fff;
  border-radius: 30rpx;
  font-size: 26rpx;
  text-align: center;
}