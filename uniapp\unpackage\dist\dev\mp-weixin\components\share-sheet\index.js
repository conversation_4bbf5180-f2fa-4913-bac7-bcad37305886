"use strict";
const common_vendor = require("../../common/vendor.js");
const core_config_index = require("../../core/config/index.js");
const core_app = require("../../core/app.js");
const utils_util = require("../../utils/util.js");
const common_model_Store = require("../../common/model/Store.js");
const GoodsPosterPopup = () => "../goods-poster-popup/index.js";
const _sfc_main = {
  name: "ShareSheet",
  components: {
    GoodsPosterPopup
  },
  emits: ["update:modelValue"],
  props: {
    // 控制组件显示隐藏
    modelValue: {
      Type: Boolean,
      default: false
    },
    // 点击遮罩层取消
    cancelWithMask: {
      type: Boolean,
      default: true
    },
    // 分享链接的标题
    shareTitle: {
      type: String,
      default: "商品分享"
    },
    // 分享链接的封面图
    shareImageUrl: {
      type: String,
      default: ""
    },
    // 取消按钮文字
    cancelText: {
      type: String,
      default: "关闭"
    },
    // 获取海报图的api方法
    posterApiCall: {
      type: Function,
      default: () => {
      }
    },
    // 获取海报图的api参数
    posterApiParam: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      // 是否启用分享到微信聊天 (仅APP端支持)
      enabledAppShareWeixin: core_config_index.Config.get("enabledAppShareWeixin"),
      // 是否显示商品海报图弹层
      showGoodsPosterPopup: false
    };
  },
  // 初始化方法
  created() {
    this.initSharesheet();
  },
  methods: {
    // 初始化选择项
    initSharesheet() {
      const app = this;
      common_vendor.index.getProvider({
        service: "share",
        success({ provider }) {
          if (!utils_util.inArray("weixin", provider)) {
            app.enabledAppShareWeixin = false;
          }
        }
      });
    },
    // 点击遮罩层(关闭菜单)
    onMaskClick() {
      if (this.cancelWithMask) {
        this.handleCancel();
      }
    },
    // 获取分享链接 (H5外链)
    getShareUrl() {
      const { path, query } = core_app.getCurrentPage();
      return new Promise((resolve, reject) => {
        common_model_Store.StoreModel.h5Url().then((baseUrl) => {
          const shareUrl = core_app.buildUrL(baseUrl, path, query);
          resolve(shareUrl);
        });
      });
    },
    // 复制商品链接
    handleCopyLink() {
      const app = this;
      app.getShareUrl().then((shareUrl) => {
        common_vendor.index.setClipboardData({
          data: shareUrl,
          success: () => app.$toast("链接复制成功，快去发送给朋友吧~"),
          fail: ({ errMsg }) => app.$toast("复制失败 " + errMsg),
          complete: () => app.handleCancel()
        });
      });
    },
    // APP发送给微信好友
    // APP分享到微信朋友圈
    // 关闭菜单
    handleCancel() {
      this.$emit("update:modelValue", false);
    },
    // 生成二维码海报
    handlePoster() {
      this.showGoodsPosterPopup = true;
      this.handleCancel();
    }
  }
};
if (!Array) {
  const _component_goods_poster_popup = common_vendor.resolveComponent("goods-poster-popup");
  _component_goods_poster_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.onMaskClick && $options.onMaskClick(...args)),
    b: common_vendor.o(($event) => $options.handleCancel()),
    c: common_vendor.o(($event) => $options.handlePoster()),
    d: common_vendor.o(($event) => $options.handleCopyLink()),
    e: $props.cancelText
  }, $props.cancelText ? {
    f: common_vendor.t($props.cancelText),
    g: common_vendor.o(($event) => $options.handleCancel())
  } : {}, {
    h: common_vendor.o(($event) => $data.showGoodsPosterPopup = $event),
    i: common_vendor.p({
      apiCall: $props.posterApiCall,
      apiParam: $props.posterApiParam,
      modelValue: $data.showGoodsPosterPopup
    }),
    j: $props.modelValue ? 1 : ""
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-5dc7a25c"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/share-sheet/index.js.map
