"use strict";
const utils_util = require("../../../../utils/util.js");
const utils_color = require("../../../../utils/color.js");
const components_page_diyComponents_mixin = require("../mixin.js");
require("../../../../common/enum/groupon/GoodsStatus.js");
require("../../../../common/enum/groupon/TaskStatus.js");
const common_enum_groupon_ActiveType = require("../../../../common/enum/groupon/ActiveType.js");
require("../../../../common/enum/groupon/ActiveStatus.js");
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  components: {},
  mixins: [components_page_diyComponents_mixin.mixin],
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object,
    dataList: Array
  },
  data() {
    return {
      inArray: utils_util.inArray,
      ActiveTypeEnum: common_enum_groupon_ActiveType.ActiveTypeEnum
    };
  },
  computed: {
    // 标签背景色
    tagBackgroundColor() {
      return utils_color.hex2rgba(this.appTheme.mainBg, 0.1);
    },
    // 标签边框颜色
    tagBorderColor() {
      return utils_color.hex2rgba(this.appTheme.mainBg, 0.6);
    }
  },
  /**
   * 组件的方法列表
   * 更新属性和数据的方法与更新页面数据的方法类似
   */
  methods: {
    // 跳转到拼团商品详情
    onTargetGoods(item) {
      this.$navTo("pages/groupon/goods/index", { grouponGoodsId: item.groupon_goods_id });
    }
  }
};
if (!Array) {
  const _easycom_u_tag2 = common_vendor.resolveComponent("u-tag");
  _easycom_u_tag2();
}
const _easycom_u_tag = () => "../../../../uni_modules/vk-uview-ui/components/u-tag/u-tag.js";
if (!Math) {
  _easycom_u_tag();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.dataList, (goods, idx, i0) => {
      return common_vendor.e({
        a: goods.active_type != $data.ActiveTypeEnum.NORMAL.value
      }, goods.active_type != $data.ActiveTypeEnum.NORMAL.value ? {
        b: common_vendor.t($data.ActiveTypeEnum[goods.active_type].name2)
      } : {}, {
        c: goods.goods_image
      }, $data.inArray("goodsName", $props.itemStyle.show) ? {
        d: common_vendor.t(goods.goods_name)
      } : {}, $data.inArray("peoples", $props.itemStyle.show) ? {
        e: "12bdf868-0-" + i0,
        f: common_vendor.p({
          color: _ctx.appTheme.mainBg,
          ["border-color"]: _ctx.appTheme.mainBg,
          text: `${goods.show_people}人团`,
          type: "error",
          size: "mini",
          mode: "plain"
        })
      } : {}, {
        g: $data.inArray("activeSales", $props.itemStyle.show) && goods.active_sales
      }, $data.inArray("activeSales", $props.itemStyle.show) && goods.active_sales ? {
        h: "12bdf868-1-" + i0,
        i: common_vendor.p({
          color: _ctx.appTheme.mainBg,
          ["border-color"]: $options.tagBorderColor,
          ["bg-color"]: $options.tagBackgroundColor,
          text: `已团${goods.active_sales}件`,
          type: "error",
          size: "mini"
        })
      } : {}, $data.inArray("grouponPrice", $props.itemStyle.show) ? {
        j: common_vendor.t(goods.groupon_price)
      } : {}, $data.inArray("grouponPrice", $props.itemStyle.show) ? {
        k: common_vendor.t(goods.original_price)
      } : {}, $data.inArray("button", $props.itemStyle.show) ? {} : {}, {
        l: common_vendor.o(($event) => $options.onTargetGoods(goods), idx),
        m: idx
      });
    }),
    b: $data.inArray("goodsName", $props.itemStyle.show),
    c: $data.inArray("peoples", $props.itemStyle.show),
    d: $data.inArray("grouponPrice", $props.itemStyle.show),
    e: $data.inArray("grouponPrice", $props.itemStyle.show),
    f: $data.inArray("button", $props.itemStyle.show),
    g: common_vendor.n(`display-${$props.itemStyle.display}`),
    h: common_vendor.n(`border-${$props.itemStyle.itemBorderRadius}`),
    i: `${$props.itemStyle.itemMargin * 2}rpx`,
    j: $props.itemStyle.background,
    k: `${$props.itemStyle.paddingY * 2}rpx ${$props.itemStyle.paddingX * 2}rpx`
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-12bdf868"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/components/page/diyComponents/groupon/index.js.map
