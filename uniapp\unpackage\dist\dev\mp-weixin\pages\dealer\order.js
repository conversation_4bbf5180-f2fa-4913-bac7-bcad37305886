"use strict";
const common_vendor = require("../../common/vendor.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const core_app = require("../../core/app.js");
const api_dealer_order = require("../../api/dealer/order.js");
const common_model_dealer_Setting = require("../../common/model/dealer/Setting.js");
const AvatarImage = () => "../../components/avatar-image/index.js";
const pageSize = 15;
const _sfc_main = {
  components: {
    AvatarImage
  },
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin],
  data() {
    return {
      // 选项卡列表
      tabList: [],
      // 当前选项
      curTab: 0,
      // 列表数据
      list: core_app.getEmptyPaginateObj(),
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: { size: pageSize },
        // 数量要大于12条才显示无更多数据
        noMoreSize: 12,
        // 空布局
        empty: {
          tip: "亲，暂无相关数据"
        }
      }
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getSetting();
  },
  methods: {
    // 获取分销设置
    getSetting() {
      const app = this;
      common_model_dealer_Setting.SettingModel.data().then((setting) => {
        const words = setting.words.order;
        app.setPageTitle(words.title);
        app.setTabList(words.words);
      });
    },
    // 设置页面标题
    setPageTitle(title) {
      common_vendor.index.setNavigationBarTitle({
        title: title.value
      });
    },
    // 设置选项卡数据
    setTabList(words) {
      const app = this;
      app.tabList = [
        { value: -1, name: words.all.value },
        { value: 0, name: words.unsettled.value },
        { value: 1, name: words.settled.value }
      ];
    },
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    // 获取提现列表
    getList(pageNo = 1) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_dealer_order.list({ settled: app.getTabValue(), page: pageNo }).then((result) => {
          const newList = result.data.list;
          app.list.data = core_app.getMoreListData(newList, app.list, pageNo);
          resolve(newList);
        });
      });
    },
    // 获取当前标签项的值
    getTabValue() {
      const app = this;
      if (app.tabList.length) {
        return app.tabList[app.curTab].value;
      }
      return -1;
    },
    // 切换标签项
    onChangeTab(index) {
      const app = this;
      app.curTab = index;
      app.onRefreshList();
    },
    // 刷新列表数据
    onRefreshList() {
      this.list = core_app.getEmptyPaginateObj();
      setTimeout(() => {
        this.mescroll.resetUpScroll();
      }, 120);
    }
  }
};
if (!Array) {
  const _easycom_u_tabs2 = common_vendor.resolveComponent("u-tabs");
  const _component_avatar_image = common_vendor.resolveComponent("avatar-image");
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  (_easycom_u_tabs2 + _component_avatar_image + _easycom_mescroll_body2)();
}
const _easycom_u_tabs = () => "../../uni_modules/vk-uview-ui/components/u-tabs/u-tabs.js";
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  (_easycom_u_tabs + _easycom_mescroll_body)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o($options.onChangeTab),
    b: common_vendor.o(($event) => $data.curTab = $event),
    c: common_vendor.p({
      list: $data.tabList,
      ["is-scroll"]: false,
      ["active-color"]: "#786cff",
      duration: 0.2,
      modelValue: $data.curTab
    }),
    d: common_vendor.f($data.list.data, (item, index, i0) => {
      return {
        a: common_vendor.t(item.order.order_no),
        b: common_vendor.t(item.order.state_text),
        c: "2f63016d-2-" + i0 + ",2f63016d-0",
        d: common_vendor.p({
          url: item.user.avatar_url,
          width: 100,
          borderWidth: 4,
          borderColor: `#fff`
        }),
        e: common_vendor.t(item.user.nick_name),
        f: common_vendor.t(item.order_price),
        g: common_vendor.t(item.my_money),
        h: common_vendor.t(item.create_time),
        i: index
      };
    }),
    e: common_vendor.sr("mescrollRef", "2f63016d-0"),
    f: common_vendor.o(_ctx.mescrollInit),
    g: common_vendor.o($options.upCallback),
    h: common_vendor.p({
      sticky: true,
      down: {
        use: false
      },
      up: $data.upOption
    }),
    i: common_vendor.s(_ctx.appThemeStyle)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-2f63016d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/dealer/order.js.map
