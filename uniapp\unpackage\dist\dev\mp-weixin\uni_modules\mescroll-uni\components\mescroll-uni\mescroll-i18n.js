"use strict";
const common_vendor = require("../../../../common/vendor.js");
const mescrollI18n = {
  // 默认语言
  def: "zh",
  // 获取当前语言类型
  getType() {
    return common_vendor.index.getStorageSync("mescroll-i18n") || this.def;
  },
  // 设置当前语言类型
  setType(type) {
    common_vendor.index.setStorageSync("mescroll-i18n", type);
  }
};
exports.mescrollI18n = mescrollI18n;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-i18n.js.map
