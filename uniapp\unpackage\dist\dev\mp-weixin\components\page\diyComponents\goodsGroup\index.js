"use strict";
const utils_util = require("../../../../utils/util.js");
const core_app = require("../../../../core/app.js");
const common_vendor = require("../../../../common/vendor.js");
const AddCartPopup = () => "../../../add-cart-popup/index.js";
const _sfc_main = {
  components: {
    AddCartPopup
  },
  props: {
    itemIndex: String,
    itemStyle: Object,
    params: Object,
    dataList: Array
  },
  data() {
    return {
      inArray: utils_util.inArray,
      // tab指针
      tabIndex: 0
    };
  },
  methods: {
    // 切换选项卡
    handleTabItem(index) {
      this.tabIndex = index;
    },
    // 跳转商品详情页
    handleGoodsItem(goodsId) {
      this.$navTo(`pages/goods/detail`, { goodsId });
    },
    // 点击加入购物车
    handleAddCart(item) {
      this.$refs.AddCartPopup.handle(item);
    },
    // 更新购物车角标
    onAddCart(total) {
      core_app.setCartTabBadge();
    }
  }
};
if (!Array) {
  const _component_AddCartPopup = common_vendor.resolveComponent("AddCartPopup");
  _component_AddCartPopup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.params.tabs, (tabItem, dataIdx, i0) => {
      return {
        a: common_vendor.t(tabItem.name),
        b: common_vendor.t(tabItem.subName),
        c: dataIdx,
        d: dataIdx == $data.tabIndex ? 1 : "",
        e: common_vendor.o(($event) => $options.handleTabItem(dataIdx), dataIdx)
      };
    }),
    b: $props.itemStyle.tabTextColor,
    c: $props.itemStyle.tabActiveFontColor,
    d: $props.itemStyle.tabActiveBgColor,
    e: $props.itemStyle.background,
    f: common_vendor.f($props.dataList[$data.tabIndex], (dataItm, dataIdx, i0) => {
      return common_vendor.e({
        a: dataItm.goods_image
      }, $data.inArray("goodsName", $props.itemStyle.show) ? {
        b: common_vendor.t(dataItm.goods_name),
        c: common_vendor.n($props.itemStyle.goodsNameRows == "two" ? "twoline-hide" : "oneline-hide"),
        d: common_vendor.n(`row-${$props.itemStyle.goodsNameRows}`)
      } : {}, $data.inArray("sellingPoint", $props.itemStyle.show) ? {
        e: common_vendor.t(dataItm.selling_point),
        f: $props.itemStyle.sellingColor
      } : {}, $data.inArray("goodsSales", $props.itemStyle.show) ? {
        g: common_vendor.t(dataItm.goods_sales)
      } : {}, $data.inArray("goodsPrice", $props.itemStyle.show) ? {
        h: common_vendor.t(dataItm.goods_price_min)
      } : {}, $data.inArray("linePrice", $props.itemStyle.show) ? {
        i: common_vendor.t(dataItm.line_price_min)
      } : {}, {
        j: common_vendor.o(($event) => $options.handleAddCart(dataItm), dataIdx),
        k: dataIdx,
        l: common_vendor.o(($event) => $options.handleGoodsItem(dataItm.goods_id), dataIdx)
      });
    }),
    g: $data.inArray("goodsName", $props.itemStyle.show),
    h: $data.inArray("sellingPoint", $props.itemStyle.show),
    i: $data.inArray("goodsSales", $props.itemStyle.show),
    j: $data.inArray("goodsPrice", $props.itemStyle.show),
    k: $data.inArray("linePrice", $props.itemStyle.show),
    l: $props.itemStyle.priceColor,
    m: common_vendor.n(`icon-jiagou${$props.itemStyle.btnCartStyle}`),
    n: $props.itemStyle.btnCartColor,
    o: $data.inArray("cartBtn", $props.itemStyle.show),
    p: common_vendor.n(`display-${$props.itemStyle.cardType}`),
    q: `${$props.itemStyle.itemMargin}px`,
    r: `${$props.itemStyle.borderRadius}px`,
    s: common_vendor.sr("AddCartPopup", "c6fc819d-0"),
    t: common_vendor.o($options.onAddCart),
    v: $props.itemStyle.background,
    w: `${$props.itemStyle.paddingY}px ${$props.itemStyle.paddingX}px`
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c6fc819d"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/components/page/diyComponents/goodsGroup/index.js.map
