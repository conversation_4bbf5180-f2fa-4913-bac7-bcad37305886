"use strict";
const api_groupon_task = require("../../../../api/groupon/task.js");
const common_vendor = require("../../../../common/vendor.js");
const AvatarImage = () => "../../../../components/avatar-image/index.js";
const CountDown = () => "../../../../components/countdown/index.js";
const _sfc_main = {
  components: {
    AvatarImage,
    CountDown
  },
  props: {
    // 拼团商品ID
    grouponGoodsId: {
      type: Number,
      default: null
    },
    // 商品ID
    list: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      // 显示更多拼单
      showMore: false,
      // 更多拼单列表
      moreList: []
    };
  },
  methods: {
    // 显示更多拼单
    onShowMore() {
      const app = this;
      if (!app.moreList.length) {
        api_groupon_task.listByGoods(app.grouponGoodsId).then((result) => {
          app.moreList = result.data.list;
          app.showMore = true;
        });
      } else {
        app.showMore = true;
      }
    },
    // 跳转到评论列表页
    onTargetTask(taskId) {
      this.$navTo("pages/groupon/task/index", { taskId });
    }
  }
};
if (!Array) {
  const _component_avatar_image = common_vendor.resolveComponent("avatar-image");
  const _component_count_down = common_vendor.resolveComponent("count-down");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  (_component_avatar_image + _component_count_down + _easycom_u_modal2)();
}
const _easycom_u_modal = () => "../../../../uni_modules/vk-uview-ui/components/u-modal/u-modal.js";
if (!Math) {
  _easycom_u_modal();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.list.length
  }, $props.list.length ? {
    b: common_vendor.o(($event) => $options.onShowMore()),
    c: common_vendor.f($props.list, (item, index, i0) => {
      return {
        a: "1a4f7103-0-" + i0,
        b: common_vendor.p({
          url: item.user.avatar_url,
          width: 60
        }),
        c: common_vendor.t(item.user.nick_name),
        d: common_vendor.t(item.surplus_people),
        e: "1a4f7103-1-" + i0,
        f: common_vendor.p({
          date: item.end_time,
          separator: "colon",
          theme: "text"
        }),
        g: common_vendor.o(($event) => $options.onTargetTask(item.task_id), index),
        h: index
      };
    }),
    d: common_vendor.f($data.moreList, (item, index, i0) => {
      return {
        a: "1a4f7103-3-" + i0 + ",1a4f7103-2",
        b: common_vendor.p({
          url: item.user.avatar_url,
          width: 60
        }),
        c: common_vendor.t(item.user.nick_name),
        d: common_vendor.o(($event) => $options.onTargetTask(item.task_id), index),
        e: index
      };
    }),
    e: common_vendor.o(($event) => $data.showMore = $event),
    f: common_vendor.p({
      title: "可参与的拼单",
      modelValue: $data.showMore
    }),
    g: common_vendor.s(_ctx.appThemeStyle)
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1a4f7103"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages/groupon/goods/components/TaskList.js.map
