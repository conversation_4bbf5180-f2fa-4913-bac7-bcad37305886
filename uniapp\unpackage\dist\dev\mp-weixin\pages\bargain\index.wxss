/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.container.data-v-37c69674 {
  padding-bottom: calc(constant(safe-area-inset-bottom) + 96rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 96rpx);
}
.bargain-hall.data-v-37c69674 {
  padding-top: 20rpx;
}
.goods-item.data-v-37c69674 {
  margin-bottom: 20rpx;
  background: #fff;
  padding: 20rpx 16rpx;
}
.goods-item.data-v-37c69674:last-child {
  margin-bottom: 0;
}
.goods-item .goods-image .image.data-v-37c69674 {
  display: block;
  width: 220rpx;
  height: 220rpx;
}
.goods-item .goods-info.data-v-37c69674 {
  width: 498rpx;
  padding-top: 8rpx;
  margin-left: 15rpx;
  position: relative;
}
.goods-item .goods-info .goods-name.data-v-37c69674 {
  font-size: 28rpx;
  min-height: 68rpx;
  line-height: 1.3;
}
.goods-item .goods-info .peoples.data-v-37c69674 {
  margin-top: 15rpx;
}
.goods-item .goods-info .peoples .user-list.data-v-37c69674 {
  margin-right: 10rpx;
}
.goods-item .goods-info .peoples .user-list .user-item-avatar.data-v-37c69674 {
  margin-left: -8rpx;
}
.goods-item .goods-info .peoples .user-list .user-item-avatar.data-v-37c69674:first-child {
  margin-left: 0;
}
.goods-item .goods-info .peoples .people__text.data-v-37c69674 {
  font-size: 24rpx;
  color: #818181;
}
.goods-item .goods-info .goods-price.data-v-37c69674 {
  margin-top: 15rpx;
  color: #818181;
  font-size: 25rpx;
  text-decoration: line-through;
}
.goods-item .goods-info .floor-price.data-v-37c69674 {
  color: var(--main-bg);
}
.goods-item .goods-info .floor-price .small.data-v-37c69674 {
  font-size: 24rpx;
}
.goods-item .goods-info .floor-price .big.data-v-37c69674 {
  font-size: 32rpx;
}
.goods-item .task-rate.data-v-37c69674 {
  font-size: 25rpx;
  color: #a4a4a4;
  margin-top: 15rpx;
}

/* 我的砍价 */
.task-status.data-v-37c69674 {
  margin-top: 32rpx;
  height: 58rpx;
}
.task-status__text.data-v-37c69674 {
  font-size: 26rpx;
}
.count-down.data-v-37c69674 {
  font-size: 25rpx;
}
.opt-touch.data-v-37c69674 {
  position: absolute;
  bottom: 0;
  right: 10rpx;
}
.touch-btn.data-v-37c69674 {
  color: #fff;
  font-size: 28rpx;
  background: #d3a975;
  border-radius: 30rpx;
  padding: 10rpx 28rpx;
}
.footer-fixed.data-v-37c69674 {
  position: fixed;
  bottom: var(--window-bottom);
  left: 0;
  right: 0;
  z-index: 11;
  box-shadow: 0 -4rpx 40rpx 0 rgba(151, 151, 151, 0.24);
  background: #fff;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.footer-container.data-v-37c69674 {
  display: flex;
  align-items: center;
  height: 96rpx;
}
.tabbar-item.data-v-37c69674 {
  font-size: 30rpx;
}
.tabbar-item.active .tabbar-item-content.data-v-37c69674 {
  color: var(--main-bg);
}
.tabbar-item .tabbar-item-icon.data-v-37c69674 {
  margin-right: 15rpx;
}
.tabbar-item__divider.data-v-37c69674 {
  padding: 22rpx 0;
}
.divider-line.data-v-37c69674 {
  width: 1rpx;
  height: 62rpx;
  background: #ddd;
}