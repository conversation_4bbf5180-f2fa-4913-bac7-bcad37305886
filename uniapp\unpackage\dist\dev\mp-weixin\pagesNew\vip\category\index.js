"use strict";
const core_mixins_wxofficial = require("../../../core/mixins/wxofficial.js");
const uni_modules_mescrollUni_components_mescrollUni_mixins_mescrollComp = require("../../../uni_modules/mescroll-uni/components/mescroll-uni/mixins/mescroll-comp.js");
const common_vendor = require("../../../common/vendor.js");
require("../../../store/index.js");
require("../../../core/config/index.js");
const common_enum_setting_Key = require("../../../common/enum/setting/Key.js");
const common_enum_store_page_category_Style = require("../../../common/enum/store/page/category/Style.js");
const common_model_Setting = require("../../../common/model/Setting.js");
const api_category_index = require("../../../api/category/index.js");
const Search = () => "../../../components/search/index.js";
const PromotePopup = () => "../../../components/promote-popup/index.js";
const Commodity = () => "./components/commodity.js";
let lastRefreshTime;
const _sfc_main = {
  components: {
    Search,
    Commodity,
    PromotePopup
  },
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mixins_mescrollComp.MescrollCompMixin, core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 枚举类
      PageCategoryStyleEnum: common_enum_store_page_category_Style.PageCategoryStyleEnum,
      // 分类列表
      list: [],
      // 分类模板设置
      setting: {},
      // 正在加载中
      isLoading: true
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.onRefreshPage();
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const curTime = (/* @__PURE__ */ new Date()).getTime();
    if (curTime - lastRefreshTime > 5 * 60 * 1e3) {
      this.onRefreshPage();
    }
  },
  methods: {
    // 刷新页面
    onRefreshPage() {
      lastRefreshTime = (/* @__PURE__ */ new Date()).getTime();
      this.getPageData();
    },
    // 获取页面数据
    getPageData() {
      const app = this;
      app.isLoading = true;
      Promise.all([
        // 获取分类模板设置
        // 优化建议: 可以将此处的false改为true 启用缓存
        common_model_Setting.SettingModel.data(false),
        // 获取分类列表
        api_category_index.listVip()
      ]).then((result) => {
        app.initSetting(result[0]);
        app.initCategory(result[1]);
        app.setWxofficialShareData();
      }).finally(() => app.isLoading = false);
    },
    /**
     * 初始化分类模板设置
     * @param {Object} result
     */
    initSetting(setting) {
      this.setting = setting[common_enum_setting_Key.SettingKeyEnum.PAGE_CATEGORY_TEMPLATE.value];
    },
    /**
     * 初始化分类列表数据
     * @param {Object} result
     */
    initCategory(result) {
      this.list = result.data.list;
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      const { setting } = this;
      this.updateShareCardData({ title: setting.shareTitle });
    }
  },
  /**
   * 设置分享内容
   */
  onShareAppMessage() {
    const app = this;
    return {
      title: _this.templet.shareTitle,
      path: "/pages/category/index?" + app.$getShareUrlParams()
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    const app = this;
    return {
      title: _this.templet.shareTitle,
      path: "/pages/category/index?" + app.$getShareUrlParams()
    };
  }
};
if (!Array) {
  const _component_commodity = common_vendor.resolveComponent("commodity");
  _component_commodity();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.sr("mescrollItem", "af4e0e57-0"),
    b: common_vendor.p({
      list: $data.list,
      setting: $data.setting
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-af4e0e57"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesNew/vip/category/index.js.map
