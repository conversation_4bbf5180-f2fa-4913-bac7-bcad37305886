"use strict";
const utils_request_index = require("../utils/request/index.js");
const api = {
  list: "refund/list",
  goods: "refund/goods",
  apply: "refund/apply",
  detail: "refund/detail",
  delivery: "refund/delivery"
};
const list = (param, option) => {
  return utils_request_index.$http.get(api.list, param, option);
};
const goods = (orderGoodsId, param) => {
  return utils_request_index.$http.get(api.goods, { orderGoodsId, ...param });
};
const apply = (orderGoodsId, data) => {
  return utils_request_index.$http.post(api.apply, { orderGoodsId, form: data });
};
const detail = (orderRefundId, param) => {
  return utils_request_index.$http.get(api.detail, { orderRefundId, ...param });
};
const delivery = (orderRefundId, data) => {
  return utils_request_index.$http.post(api.delivery, { orderRefundId, form: data });
};
exports.apply = apply;
exports.delivery = delivery;
exports.detail = detail;
exports.goods = goods;
exports.list = list;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/refund.js.map
