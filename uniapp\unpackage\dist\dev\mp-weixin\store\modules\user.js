"use strict";
const store_mutationTypes = require("../mutation-types.js");
const utils_storage = require("../../utils/storage.js");
const api_login_index = require("../../api/login/index.js");
const loginSuccess = (commit, { token, userId }) => {
  const expiryTime = 30 * 86400;
  utils_storage.storage.set(store_mutationTypes.USER_ID, userId, expiryTime);
  utils_storage.storage.set(store_mutationTypes.ACCESS_TOKEN, token, expiryTime);
  commit("SET_TOKEN", token);
  commit("SET_USER_ID", userId);
};
const user = {
  state: {
    // 用户认证token
    token: "",
    // 用户ID
    userId: null
  },
  mutations: {
    SET_TOKEN: (state, value) => {
      state.token = value;
    },
    SET_USER_ID: (state, value) => {
      state.userId = value;
    }
  },
  actions: {
    // 用户登录 (普通登录: 输入手机号和验证码)
    Login({ commit }, data) {
      return new Promise((resolve, reject) => {
        api_login_index.login({ form: data }).then((response) => {
          const result = response.data;
          loginSuccess(commit, result);
          resolve(response);
        }).catch(reject);
      });
    },
    // 微信小程序一键授权登录 (获取用户基本信息)
    LoginMpWx({ commit }, data) {
      return new Promise((resolve, reject) => {
        api_login_index.loginMpWx({ form: data }, { isPrompt: false }).then((response) => {
          const result = response.data;
          loginSuccess(commit, result);
          resolve(response);
        }).catch(reject);
      });
    },
    // 微信公众号一键授权登录 (获取用户基本信息)
    LoginWxOfficial({ commit }, data) {
      return new Promise((resolve, reject) => {
        api_login_index.loginWxOfficial({ form: data }, { isPrompt: false }).then((response) => {
          const result = response.data;
          loginSuccess(commit, result);
          resolve(response);
        }).catch(reject);
      });
    },
    // 微信小程序一键授权登录 (授权手机号)
    LoginMpWxMobile({ commit }, data) {
      return new Promise((resolve, reject) => {
        api_login_index.loginMpWxMobile({ form: data }, { isPrompt: false }).then((response) => {
          const result = response.data;
          loginSuccess(commit, result);
          resolve(response);
        }).catch(reject);
      });
    },
    // 支付宝小程序一键授权登录 (获取用户基本信息)
    LoginMpAlipay({ commit }, data) {
      return new Promise((resolve, reject) => {
        api_login_index.loginMpAlipay({ form: data }, { isPrompt: false }).then((response) => {
          const result = response.data;
          loginSuccess(commit, result);
          resolve(response);
        }).catch(reject);
      });
    },
    // 退出登录
    Logout({ commit }, data) {
      const store = this;
      return new Promise((resolve, reject) => {
        if (store.getters.userId > 0) {
          utils_storage.storage.remove(store_mutationTypes.USER_ID);
          utils_storage.storage.remove(store_mutationTypes.ACCESS_TOKEN);
          commit("SET_TOKEN", "");
          commit("SET_USER_ID", null);
          resolve();
        }
      });
    }
  }
};
exports.user = user;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/store/modules/user.js.map
