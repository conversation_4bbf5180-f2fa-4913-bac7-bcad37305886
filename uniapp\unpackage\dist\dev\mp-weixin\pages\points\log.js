"use strict";
const common_vendor = require("../../common/vendor.js");
const uni_modules_mescrollUni_components_mescrollUni_mescrollMixins = require("../../uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js");
const api_points_log = require("../../api/points/log.js");
const core_app = require("../../core/app.js");
const pageSize = 15;
const _sfc_main = {
  mixins: [uni_modules_mescrollUni_components_mescrollUni_mescrollMixins.MescrollMixin],
  data() {
    return {
      info: {},
      isLoading: true,
      // 充值记录
      list: core_app.getEmptyPaginateObj(),
      // 控制onShow事件是否刷新订单列表
      canReset: false,
      // 上拉加载配置
      upOption: {
        // 首次自动执行
        auto: true,
        // 每页数据的数量; 默认10
        page: {
          size: pageSize
        },
        // 数量要大于12条才显示无更多数据
        noMoreSize: 12,
        // 空布局
        empty: {
          tip: "亲，暂无相关数据"
        }
      }
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },
  onShow() {
    this.getData();
    this.canReset && this.getLogList();
  },
  methods: {
    toTixian() {
      common_vendor.index.navigateTo({
        url: "/pagesNew/points/apply"
      });
    },
    toLog() {
      common_vendor.index.navigateTo({
        url: "/pages/dealer/withdraw/list?type=3"
      });
    },
    toSign() {
      common_vendor.index.navigateTo({
        url: "/pages/sign/index"
      });
    },
    onTargetDetail(articleId) {
      this.$navTo("pages/article/xieyi", {
        articleId
      });
    },
    /**
     * 上拉加载的回调 (页面初始化时也会执行一次)
     * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
     * @param {Object} page
     */
    upCallback(page) {
      const app = this;
      app.getLogList(page.num).then((list) => {
        const curPageLen = list.data.length;
        const totalSize = list.data.total;
        app.mescroll.endBySize(curPageLen, totalSize);
      }).catch(() => app.mescroll.endErr());
    },
    getData() {
      const app = this;
      api_points_log.getData({}).then((result) => {
        app.info = result.data.list;
        app.isLoading = false;
      });
    },
    // 获取积分明细列表
    getLogList(pageNo = 1) {
      const app = this;
      return new Promise((resolve, reject) => {
        api_points_log.list({
          page: pageNo
        }).then((result) => {
          const newList = result.data.list;
          app.canReset = true;
          app.list.data = core_app.getMoreListData(newList, app.list, pageNo);
          resolve(newList);
        });
      });
    }
  }
};
if (!Array) {
  const _easycom_mescroll_body2 = common_vendor.resolveComponent("mescroll-body");
  _easycom_mescroll_body2();
}
const _easycom_mescroll_body = () => "../../uni_modules/mescroll-uni/components/mescroll-body/mescroll-body.js";
if (!Math) {
  _easycom_mescroll_body();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    b: common_vendor.t($data.info.points),
    c: common_vendor.o((...args) => $options.toSign && $options.toSign(...args)),
    d: common_vendor.t($data.info.points_all),
    e: common_vendor.t($data.info.points_xiaofei),
    f: common_vendor.t($data.info.points_freeze),
    g: common_vendor.o(($event) => $options.onTargetDetail(10002)),
    h: common_vendor.o((...args) => $options.toLog && $options.toLog(...args)),
    i: common_vendor.f($data.list.data, (item, index, i0) => {
      return {
        a: common_vendor.t(item.describe),
        b: common_vendor.t(item.create_time),
        c: common_vendor.t(item.value > 0 ? "+" : ""),
        d: common_vendor.t(item.value),
        e: common_vendor.n(item.value > 0 ? "col-green" : "col-6"),
        f: index
      };
    }),
    j: common_vendor.sr("mescrollRef", "fcd717e4-0"),
    k: common_vendor.o(_ctx.mescrollInit),
    l: common_vendor.o($options.upCallback),
    m: common_vendor.p({
      sticky: true,
      down: {
        use: false
      },
      up: $data.upOption
    }),
    n: $data.info.can == 1
  }, $data.info.can == 1 ? {
    o: common_vendor.o((...args) => $options.toTixian && $options.toTixian(...args))
  } : {}) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fcd717e4"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/points/log.js.map
