"use strict";
const common_vendor = require("../../common/vendor.js");
const core_mixins_wxofficial = require("../../core/mixins/wxofficial.js");
const api_shop = require("../../api/shop.js");
const _sfc_main = {
  mixins: [core_mixins_wxofficial.WxofficialMixin],
  data() {
    return {
      // 正在加载中
      isLoading: true,
      // 当前门店ID
      shopId: void 0,
      // 门店详情
      detail: null
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.shopId = options.shopId;
    this.getShopDetail();
  },
  methods: {
    // 获取门店详情
    getShopDetail() {
      const app = this;
      app.isLoading = true;
      api_shop.detail(app.shopId).then((result) => {
        app.detail = result.data.detail;
        app.setWxofficialShareData();
      }).finally(() => app.isLoading = false);
    },
    // 拨打电话
    onMakePhoneCall() {
      const app = this;
      common_vendor.index.makePhoneCall({
        phoneNumber: app.detail.phone
      });
    },
    // 查看位置
    onOpenLocation() {
      const app = this;
      const { detail } = app;
      common_vendor.index.openLocation({
        name: detail.shop_name,
        address: detail.region.province + detail.region.city + detail.region.region + detail.address,
        longitude: Number(detail.longitude),
        latitude: Number(detail.latitude),
        scale: 15
      });
    },
    // 设置微信公众号链接分享卡片内容
    setWxofficialShareData() {
      const app = this;
      app.updateShareCardData({ title: app.detail.shop_name });
    }
  },
  /**
   * 分享当前页面
   */
  onShareAppMessage() {
    const app = this;
    const params = app.$getShareUrlParams({ shopId: app.shopId });
    return {
      title: app.detail.shop_name,
      path: "/pages/shop/detail?" + params
    };
  },
  /**
   * 分享到朋友圈
   * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)
   * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html
   */
  onShareTimeline() {
    const app = this;
    const params = app.$getShareUrlParams({ shopId: app.shopId });
    return {
      title: app.detail.shop_name,
      path: "/pages/shop/detail?" + params
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLoading
  }, !$data.isLoading ? common_vendor.e({
    b: $data.detail.logo_url,
    c: common_vendor.t($data.detail.shop_name),
    d: $data.detail.summary
  }, $data.detail.summary ? {
    e: common_vendor.t($data.detail.summary)
  } : {}, {
    f: common_vendor.t($data.detail.shop_hours),
    g: common_vendor.t($data.detail.region.province),
    h: common_vendor.t($data.detail.region.city),
    i: common_vendor.t($data.detail.region.region),
    j: common_vendor.t($data.detail.address),
    k: common_vendor.o(($event) => $options.onOpenLocation()),
    l: common_vendor.t($data.detail.phone),
    m: common_vendor.o(($event) => $options.onMakePhoneCall())
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e68d3657"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/shop/detail.js.map
