/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入自定义主题 */
.u-notice-bar-wrap.data-v-a898d50a {
  overflow: hidden;
}
.u-notice-bar.data-v-a898d50a {
  padding: 18rpx 24rpx;
  overflow: hidden;
}
.u-direction-row.data-v-a898d50a {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.u-left-icon.data-v-a898d50a {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-notice-box.data-v-a898d50a {
  flex: 1;
  display: flex;
  flex-direction: row;
  overflow: hidden;
  margin-left: 12rpx;
}
.u-right-icon.data-v-a898d50a {
  margin-left: 12rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-notice-content.data-v-a898d50a {
  line-height: 1;
  white-space: nowrap;
  font-size: 26rpx;
  animation: u-loop-animation-a898d50a 10s linear infinite both;
  text-align: right;
  padding-left: 100%;
}
@keyframes u-loop-animation-a898d50a {
0% {
    transform: translate3d(0, 0, 0);
}
100% {
    transform: translate3d(-100%, 0, 0);
}
}